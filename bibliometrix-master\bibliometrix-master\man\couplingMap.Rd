% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/couplingMap.R
\name{couplingMap}
\alias{couplingMap}
\title{Coupling Analysis}
\usage{
couplingMap(
  M,
  analysis = "documents",
  field = "CR",
  n = 500,
  label.term = NULL,
  ngrams = 1,
  impact.measure = "local",
  minfreq = 5,
  community.repulsion = 0.1,
  stemming = FALSE,
  size = 0.5,
  n.labels = 1,
  repel = TRUE,
  cluster = "walktrap"
)
}
\arguments{
\item{M}{is a bibliographic dataframe.}

\item{analysis}{is the textual attribute used to select the unit of analysis. It can be \code{analysis = c("documents", "authors", "sources")}.}

\item{field}{is the textual attribute used to measure the coupling strength. It can be \code{field = c("CR", "ID","DE", "TI", "AB")}.}

\item{n}{is an integer. It indicates the number of units to include in the analysis.}

\item{label.term}{is a character. It indicates which content metadata have to use for cluster labeling. It can be \code{label.term = c("ID","DE","TI","AB")}.
If \code{label.term = NULL} cluster items will be use for labeling.}

\item{ngrams}{is an integer between 1 and 4. It indicates the type of n-gram to extract from texts. 
An n-gram is a contiguous sequence of n terms. The function can extract n-grams composed by 1, 2, 3 or 4 terms. Default value is \code{ngrams=1}.}

\item{impact.measure}{is a character. It indicates the impact measure used to rank cluster elements (documents, authors or sources).
It can be \code{impact.measure = c("local", "global")}.\\
With \code{impact.measure = "local"}, \link{couplingMap} calculates elements impact using the Normalized Local Citation Score while 
using \code{impact.measure = "global"}, the function uses the Normalized Global Citation Score to measure elements impact.}

\item{minfreq}{is a integer. It indicates the minimum frequency (per thousand) of a cluster. It is a number in the range (0,1000).}

\item{community.repulsion}{is a real. It indicates the repulsion force among network communities. It is a real number between 0 and 1. Default is \code{community.repulsion = 0.1}.}

\item{stemming}{is logical. If it is TRUE the word (from titles or abstracts) will be stemmed (using the Porter's algorithm).}

\item{size}{is numerical. It indicates the size of the cluster circles and is a number in the range (0.01,1).}

\item{n.labels}{is integer. It indicates how many labels associate to each cluster. Default is \code{n.labels = 1}.}

\item{repel}{is logical. If it is TRUE ggplot uses geom_label_repel instead of geom_label.}

\item{cluster}{is a character. It indicates the type of cluster to perform among ("optimal", "louvain","leiden", "infomap","edge_betweenness","walktrap", "spinglass", "leading_eigen", "fast_greedy").}
}
\value{
a list containing:
\tabular{lll}{
\code{map}\tab   \tab The coupling map as ggplot2 object\cr
\code{clusters}\tab   \tab Centrality and Density values for each cluster. \cr
\code{data}\tab   \tab A list of units following in each cluster\cr
\code{nclust}\tab   \tab The number of clusters\cr
\code{NCS}\tab     \tab The Normalized Citation Score dataframe\cr
\code{net}\tab    \tab A list containing the network output (as provided from the networkPlot function)}
}
\description{
It performs a coupling network analysis and plots community detection results on a bi-dimensional map (Coupling Map).
}
\details{
The analysis can be performed on three different units: documents, authors or sources and 
the coupling strength can be measured using the classical approach (coupled by references) 
or a novel approach based on unit contents (keywords or terms from titles and abstracts) 

The x-axis measures the cluster centrality (by Callon's Centrality index) while the y-axis measures the cluster impact 
by Mean Normalized Local Citation Score (MNLCS). 
The Normalized Local Citation Score (NLCS) of a document is calculated 
by dividing the actual count of local citing items by the expected citation rate for documents with the same year of publication.
}
\examples{

\dontrun{
data(management, package = "bibliometrixData")
res <- couplingMap(management, analysis = "authors", field = "CR", n = 250, impact.measure="local", 
                   minfreq = 3, size = 0.5, repel = TRUE)
plot(res$map)
}

}
\seealso{
\code{\link{biblioNetwork}} function to compute a bibliographic network.

\code{\link{cocMatrix}} to compute a bibliographic bipartite network.

\code{\link{networkPlot}} to plot a bibliographic network.
}
