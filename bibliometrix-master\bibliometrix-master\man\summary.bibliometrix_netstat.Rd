% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/summary.bibliometrix_netstat.R
\name{summary.bibliometrix_netstat}
\alias{summary.bibliometrix_netstat}
\title{Summarizing network analysis results}
\usage{
\method{summary}{bibliometrix_netstat}(object, ...)
}
\arguments{
\item{object}{is the object for which a summary is desired.}

\item{...}{can accept two arguments:\cr
\code{k} integer, used for table formatting (number of rows). Default value is 10.\cr}
}
\value{
The function \code{summary} computes and returns on display several statistics both at network and vertex level.
}
\description{
\code{summary} method for class '\code{bibliometrix_netstat}'
}
\examples{

# to run the example, please remove # from the beginning of the following lines
#data(scientometrics, package = "bibliometrixData")

#NetMatrix <- biblioNetwork(scientometrics, analysis = "collaboration", 
#                   network = "authors", sep = ";")
#netstat <- networkStat(NetMatrix, stat = "all", type = "degree")
#summary(netstat)

}
