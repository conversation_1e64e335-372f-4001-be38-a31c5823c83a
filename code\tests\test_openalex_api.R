# =================================================================================================
# === OpenAlex API测试脚本 ===
# =================================================================================================
# 版本: 1.0.0
# 描述: 测试OpenAlex API的连接和基本功能

# 设置工作目录
if (!exists("project_root")) {
  project_root <- getwd()
  if (!grepl("数据处理部分$", project_root)) {
    project_root <- file.path(project_root, "数据处理部分")
  }
  setwd(project_root)
}

# 加载必要的包
required_packages <- c(
  "httr",
  "jsonlite",
  "tidyverse"
)

# 检查并安装缺失的包
missing_packages <- required_packages[!required_packages %in% installed.packages()[,"Package"]]
if (length(missing_packages) > 0) {
  install.packages(missing_packages)
}

# 加载所有必要的包
for (pkg in required_packages) {
  suppressPackageStartupMessages(library(pkg, character.only = TRUE))
}

# 加载配置和存储模块
source("code/config/config.R")
source("code/data/storage.R")
config <- export_config()

# 初始化存储
init_storage()

# 测试函数：获取单个文献信息
test_get_work <- function(work_id) {
  # 构建API URL
  url <- paste0(config$api$openalex$base_url, "/works/", work_id)
  
  # 发送请求
  response <- GET(
    url,
    add_headers(
      "User-Agent" = paste0("OpenAlexAPI/1.0 (mailto:", config$api$openalex$email, ")")
    )
  )
  
  # 检查响应状态
  if (status_code(response) == 200) {
    # 解析JSON响应
    data <- fromJSON(rawToChar(response$content))
    
    # 保存原始响应
    save_raw_response(data, "work", work_id)
    
    # 保存处理后的数据
    save_work_data(data)
    
    # 记录API调用
    log_api_call("work", work_id, "success")
    
    return(list(
      success = TRUE,
      data = data
    ))
  } else {
    # 记录失败的API调用
    log_api_call("work", work_id, paste("failed:", status_code(response)))
    
    return(list(
      success = FALSE,
      error = paste("API请求失败，状态码:", status_code(response))
    ))
  }
}

# 测试函数：搜索文献
test_search_works <- function(query, page = 1, per_page = 10) {
  # 构建API URL
  url <- paste0(config$api$openalex$base_url, "/works")
  
  # 构建查询参数
  params <- list(
    search = query,
    page = page,
    per_page = per_page
  )
  
  # 发送请求
  response <- GET(
    url,
    query = params,
    add_headers(
      "User-Agent" = paste0("OpenAlexAPI/1.0 (mailto:", config$api$openalex$email, ")")
    )
  )
  
  # 检查响应状态
  if (status_code(response) == 200) {
    # 解析JSON响应
    data <- fromJSON(rawToChar(response$content))
    
    # 保存原始响应
    save_raw_response(data, "works_search", paste0("query_", gsub(" ", "_", query)))
    
    # 保存每个文献的数据
    if (!is.null(data$results) && length(data$results) > 0) {
      # 遍历每个文献
      for (i in seq_along(data$results)) {
        work <- data$results[[i]]
        if (is.list(work) && !is.null(work$id)) {
          tryCatch({
            save_work_data(work)
          }, error = function(e) {
            cat(sprintf("保存文献 %s 时出错: %s\n", work$id, e$message))
          })
        }
      }
    }
    
    # 记录API调用
    log_api_call("works_search", query, "success")
    
    return(list(
      success = TRUE,
      data = data
    ))
  } else {
    # 记录失败的API调用
    log_api_call("works_search", query, paste("failed:", status_code(response)))
    
    return(list(
      success = FALSE,
      error = paste("API请求失败，状态码:", status_code(response))
    ))
  }
}

# 测试函数：搜索作者
test_search_authors <- function(query, page = 1, per_page = 10) {
  # 构建API URL
  url <- paste0(config$api$openalex$base_url, "/authors")
  
  # 构建查询参数
  params <- list(
    search = query,
    page = page,
    per_page = per_page
  )
  
  # 发送请求
  response <- GET(
    url,
    query = params,
    add_headers(
      "User-Agent" = paste0("OpenAlexAPI/1.0 (mailto:", config$api$openalex$email, ")")
    )
  )
  
  # 检查响应状态
  if (status_code(response) == 200) {
    # 解析JSON响应
    data <- fromJSON(rawToChar(response$content))
    
    # 保存原始响应
    save_raw_response(data, "authors_search", paste0("query_", gsub(" ", "_", query)))
    
    # 记录API调用
    log_api_call("authors_search", query, "success")
    
    return(list(
      success = TRUE,
      data = data
    ))
  } else {
    # 记录失败的API调用
    log_api_call("authors_search", query, paste("failed:", status_code(response)))
    
    return(list(
      success = FALSE,
      error = paste("API请求失败，状态码:", status_code(response))
    ))
  }
}

# 测试函数：获取作者信息
test_get_author <- function(author_id) {
  # 构建API URL
  url <- paste0(config$api$openalex$base_url, "/authors/", author_id)
  
  # 发送请求
  response <- GET(
    url,
    add_headers(
      "User-Agent" = paste0("OpenAlexAPI/1.0 (mailto:", config$api$openalex$email, ")")
    )
  )
  
  # 检查响应状态
  if (status_code(response) == 200) {
    # 解析JSON响应
    data <- fromJSON(rawToChar(response$content))
    
    # 保存原始响应
    save_raw_response(data, "author", author_id)
    
    # 保存处理后的数据
    save_author_data(data)
    
    # 记录API调用
    log_api_call("author", author_id, "success")
    
    return(list(
      success = TRUE,
      data = data
    ))
  } else {
    # 记录失败的API调用
    log_api_call("author", author_id, paste("failed:", status_code(response)))
    
    return(list(
      success = FALSE,
      error = paste("API请求失败，状态码:", status_code(response))
    ))
  }
}

# 执行测试
main <- function() {
  # 1. 测试获取单个文献
  cat("测试获取单个文献...\n")
  work_result <- test_get_work("W2741809807")  # 使用一个示例文献ID
  if (work_result$success) {
    cat("成功获取文献信息\n")
    print(str(work_result$data))
  } else {
    cat("获取文献信息失败:", work_result$error, "\n")
  }
  
  # 2. 测试搜索文献
  cat("\n测试搜索文献...\n")
  search_result <- test_search_works("bibliometric analysis", page = 1, per_page = 5)
  if (search_result$success) {
    cat("成功搜索文献\n")
    print(str(search_result$data))
  } else {
    cat("搜索文献失败:", search_result$error, "\n")
  }
  
  # 3. 测试搜索作者
  cat("\n测试搜索作者...\n")
  author_search_result <- test_search_authors("Lutz Bornmann", page = 1, per_page = 1)
  if (author_search_result$success) {
    cat("成功搜索作者\n")
    if (length(author_search_result$data$results) > 0) {
      author_id <- author_search_result$data$results$id[1]
      cat("找到作者ID:", author_id, "\n")
      
      # 4. 测试获取作者信息
      cat("\n测试获取作者信息...\n")
      author_result <- test_get_author(author_id)
      if (author_result$success) {
        cat("成功获取作者信息\n")
        print(str(author_result$data))
      } else {
        cat("获取作者信息失败:", author_result$error, "\n")
      }
    } else {
      cat("未找到作者\n")
    }
  } else {
    cat("搜索作者失败:", author_search_result$error, "\n")
  }
}

# 运行测试
main() 