# =================================================================================================
# === 文献计量分析模块 ===
# =================================================================================================
# 版本: 1.0.0
# 描述: 负责文献计量学分析功能

# 加载必要的包
required_packages <- c(
  "bibliometrix",
  "tidyverse",
  "igraph",
  "ggplot2",
  "Matrix"
)

# 检查并安装缺失的包
missing_packages <- required_packages[!required_packages %in% installed.packages()[,"Package"]]
if (length(missing_packages) > 0) {
  install.packages(missing_packages)
}

# 加载所有必要的包
for (pkg in required_packages) {
  suppressPackageStartupMessages(library(pkg, character.only = TRUE))
}

# 基本文献计量分析
perform_basic_analysis <- function(data) {
  # 1. 执行基本分析
  results <- biblioAnalysis(data, sep = ";")
  
  # 2. 生成分析摘要
  summary_results <- summary(results, k = 10)
  
  return(list(
    results = results,
    summary = summary_results
  ))
}

# 关键词共现分析
analyze_keyword_cooccurrence <- function(data, field = "DE") {
  # 1. 创建文档-词语矩阵
  dtm <- cocMatrix(data, Field = field, type = "matrix", sep = ";")
  
  # 2. 创建共现矩阵
  cooc_matrix <- Matrix::crossprod(dtm)
  
  return(list(
    dtm = dtm,
    cooc_matrix = cooc_matrix
  ))
}

# 作者合作网络分析
analyze_author_collaboration <- function(data) {
  # 1. 提取作者信息
  authors <- strsplit(data$AU, ";")
  
  # 2. 创建合作网络
  network <- create_collaboration_network(authors)
  
  return(network)
}

# 机构合作网络分析
analyze_institution_collaboration <- function(data) {
  # 1. 提取机构信息
  institutions <- strsplit(data$C1, ";")
  
  # 2. 创建合作网络
  network <- create_collaboration_network(institutions)
  
  return(network)
}

# 引文网络分析
analyze_citation_network <- function(data) {
  # 1. 提取引用信息
  citations <- strsplit(data$CR, ";")
  
  # 2. 创建引文网络
  network <- create_citation_network(citations)
  
  return(network)
}

# 导出函数
export_functions <- c(
  "perform_basic_analysis",
  "analyze_keyword_cooccurrence",
  "analyze_author_collaboration",
  "analyze_institution_collaboration",
  "analyze_citation_network"
) 