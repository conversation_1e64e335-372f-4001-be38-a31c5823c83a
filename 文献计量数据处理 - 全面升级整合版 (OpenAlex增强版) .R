﻿# =================================================================================================
# === 文献计量数据处理 - 全面升级整合版 (OpenAlex增强版) ===
# =================================================================================================
# 版本日期: 2024-04-07
# 描述: 全面重构数据处理流程，优化API调用，增强机构处理与格式兼容性

# --- 重要：首先确保Windows控制台使用UTF-8代码页 ---
if (.Platform$OS.type == "windows") {
  # 直接使用系统命令设置代码页，并确保它立即生效
  try(system("powershell -command \"[Console]::OutputEncoding = [System.Text.Encoding]::UTF8\"", intern = TRUE), silent = TRUE)
  try(system("chcp 65001", intern = TRUE), silent = TRUE)
  
  # 尝试不同的区域设置方法
  loc_methods <- c(
    "Chinese (Simplified)_China.UTF-8",
    "Chinese (Simplified)_China.65001",
    "zh-CN",
    "Chinese_China.936"
  )
  
  # 逐一尝试不同的区域设置
  locale_success <- FALSE
  for (loc in loc_methods) {
    tryCatch({
      res <- Sys.setlocale("LC_ALL", loc)
      if (res != "") {
        cat("成功设置区域为:", res, "\n")
        locale_success <- TRUE
        break
      }
    }, error = function(e) {
      # 失败时继续尝试下一个
    }, warning = function(w) {
      # 忽略警告
    })
  }
  
  if (!locale_success) {
    # 如果无法设置完整的LC_ALL，尝试分开设置
    Sys.setlocale("LC_CTYPE", "en_US.UTF-8")
    Sys.setlocale("LC_COLLATE", "C")
    cat("无法设置中文环境，已设置基本UTF-8支持\n")
  }
}

# 加载中文编码修复工具
tryCatch({
  source("c:/Users/<USER>/Desktop/google数据处相关/r_cn_encoding_fix.r")
  cat("已加载中文编码修复工具\n")
}, error = function(e) {
  cat("无法加载中文编码修复工具，请确保文件路径正确\n")
})

# --- 全局变量初始化 ---

# 设置编码
if (.Platform$OS.type == "windows") {
  Sys.setlocale("LC_ALL", "Chinese (Simplified)_China.UTF-8")  # Windows中文设置
} else {
  Sys.setlocale("LC_ALL", "zh_CN.UTF-8")  # Linux/Mac中文设置
}
options(encoding = "UTF-8")  # 设置R的编码为UTF-8

# 初始化全局配置
config <- list(
  use_openalex_api = TRUE,    # 是否使用OpenAlex API
  use_crossref_api = TRUE,    # 是否使用Crossref API
  api_timeout = 30,           # API请求超时（秒）
  max_retries = 3,            # API请求最大重试次数
  batch_size = 50,            # API批量请求大小
  cache_api_results = TRUE    # 是否缓存API结果
)

# 确保文件连接使用UTF-8编码
file_conn_options <- list(encoding = "UTF-8")
options(file.connection.encoding = "UTF-8")

# --- 0. 设置: 加载库与定义路径 ---

# 定义所需的R包列表
required_packages <- c(
  "tidyverse",    # 数据处理和可视化
  "bibliometrix", # 文献计量分析
  "lubridate",    # 日期处理
  "naniar",       # 缺失值分析
  "rcrossref",    # Crossref API接口
  "httr",         # HTTP请求
  "jsonlite",     # JSON数据处理
  "progress",     # 进度条显示
  "DBI",          # 数据库接口
  "RSQLite",      # SQLite数据库支持
  "writexl",      # Excel文件写入
  "stringdist",   # 字符串相似度计算
  "readxl",       # Excel文件读取
  "countrycode",  # 国家代码转换
  "openxlsx",     # Excel文件处理
  "purrr",        # 函数式编程工具
  "stringi",      # 字符串处理
  "text2vec",     # 文本向量化和TF-IDF
  "Matrix",       # 高效矩阵操作
  "future",       # 并行计算框架
  "future.apply"  # 并行版本的apply函数
)

# 检查并安装缺失的包
missing_packages <- required_packages[!required_packages %in% installed.packages()[,"Package"]]
if (length(missing_packages) > 0) {
  cat("正在安装缺失的包:", paste(missing_packages, collapse=", "), "\n")
  install.packages(missing_packages)
}

# 加载所有必要的包
cat("正在加载必要的 R 包...\n")
for (pkg in required_packages) {
  suppressPackageStartupMessages(library(pkg, character.only = TRUE))
  cat("  已加载:", pkg, "\n")
}
cat("所有需要的包加载完成。\n")

# --- 定义文件和数据库路径 ---
cat("\n正在定义文件路径...\n")
raw_data_file <- "C:/Users/<USER>/Desktop/数据文件/citespace数据/download1-500.txt"  # 原始数据文件路径
output_location <- "C:/Users/<USER>/Desktop/google数据处相关"  # 输出文件目录
db_file <- file.path(output_location, "api_cache_v5.sqlite")  # SQLite数据库文件
log_file_txt <- file.path(output_location, "data_processing_log_v5.txt")  # 文本日志文件
log_file_excel <- file.path(output_location, "data_processing_summary_v5.xlsx")  # Excel日志文件
fuzzy_duplicates_file <- file.path(output_location, "fuzzy_duplicates_to_review_v5.xlsx")  # 模糊重复记录文件
field_conflicts_file <- file.path(output_location, "field_conflicts_v5.xlsx")  # 字段冲突文件
author_mapping_file <- file.path(output_location, "author_identifiers_mapping_v5.xlsx")  # 作者映射文件
institution_mapping_file <- file.path(output_location, "institution_mapping_v5.xlsx")  # 机构映射文件
final_data_file <- file.path(output_location, "processed_data_final_v5.xlsx")  # 最终处理数据文件
unique_institutions_file <- file.path(output_location, "unique_institutions_v5.xlsx")  # 唯一机构列表
topic_analysis_file <- file.path(output_location, "topic_analysis_v5.xlsx")  # 主题分析结果
diagnostic_dir <- file.path(output_location, "api_diagnostics")  # API诊断目录
dir.create(diagnostic_dir, showWarnings = FALSE)
cat("文件路径定义完成。\n")
cat("  原始数据:", raw_data_file, "\n")
cat("  输出目录:", output_location, "\n")
cat("  数据库:", db_file, "\n")
cat("  日志文件:", log_file_txt, "\n")
cat("  摘要文件:", log_file_excel, "\n")
cat("  诊断目录:", diagnostic_dir, "\n")

# --- 启动日志记录 ---
cat("\n正在启动日志记录...\n")
if (!dir.exists(output_location)) {
  dir.create(output_location, recursive = TRUE)
  cat("创建输出目录:", output_location, "\n")
}

# 使用清空文件内容的方式替代删除文件
if (file.exists(log_file_txt)) {
  cat("正在清空并重新写入日志文件...\n")
  # 截断文件内容为空而不是删除
  file.create(log_file_txt, showWarnings = FALSE)
} else {
  cat("创建新的日志文件...\n")
}

sink(log_file_txt, append = FALSE, split = TRUE)
start_time <- Sys.time()
cat("=============================================================\n")
cat("=== 文献数据处理流程开始 (全面升级整合版) ===\n")
cat("版本日期: 2024-04-07\n")
cat("开始时间:", format(start_time), "\n")
cat("=============================================================\n\n")
log_summary_list <- list()

# --- 增强版机构和国家提取函数 ---
extract_institutions_enhanced <- function(c1_string) {
  if (is.na(c1_string) || c1_string == "") return(list())
  
  # 分割多个地址
  addresses <- unlist(strsplit(c1_string, ";"))
  institutions <- lapply(addresses, function(addr) {
    addr <- trimws(addr)
    if (addr == "") return(NULL)
    
    # 分割地址的各个部分
    parts <- unlist(strsplit(addr, ","))
    parts <- trimws(parts)
    
    # 初始化变量
    country <- NULL
    country_code <- NULL
    state_code <- NULL
    city <- NULL
    
    # 处理最后一个部分（通常是国家/地区）
    if (length(parts) > 0) {
      last_part <- parts[length(parts)]
      
      # 匹配国家代码
      country_match <- str_match(last_part, "([A-Z]{2,3})\\s*$")
      if (!is.na(country_match[1,2])) {
        country_code <- country_match[1,2]
        country_clean <- str_replace(last_part, country_match[1,1], "")
        country_clean <- trimws(country_clean)
        if (country_clean != "") {
          country <- country_clean
        }
      } else if (grepl("USA|UNITED STATES|AMERICA", toupper(last_part))) {
        # 特殊处理美国地址
        country <- "USA"
        country_code <- "US"
        
        # 提取州代码
        state_match <- str_match(last_part, "([A-Z]{2})\\s+\\d+")
        if (!is.na(state_match[1,2])) {
          state_code <- state_match[1,2]
        }
      } else {
        # 处理常见国家名称
        common_countries <- c("CHINA", "ENGLAND", "UK", "UNITED KINGDOM", "CANADA", "AUSTRALIA", 
                            "GERMANY", "FRANCE", "ITALY", "JAPAN", "BRAZIL", "INDIA", "SPAIN", 
                            "NETHERLANDS", "SWEDEN", "DENMARK", "NORWAY", "SWITZERLAND")
        for (c in common_countries) {
          if (grepl(c, toupper(last_part))) {
            country <- c
            break
          }
        }
        
        if (is.null(country)) {
          city <- last_part
        }
      }
    }
    
    # 提取城市信息
    if (is.null(city) && length(parts) > 1) {
      potential_city <- parts[length(parts)-1]
      if (!grepl("\\d{4,}", potential_city) && !grepl("UNIV|DEPT|SCHOOL", toupper(potential_city))) {
        city <- potential_city
      }
    }
    
    # 提取机构名称和部门
    inst_name <- if (length(parts) > 0) parts[1] else ""
    dept_name <- NA_character_
    if (length(parts) > 1) {
      if (grepl("DEPT|SCHOOL|FACULTY|COLLEGE|INSTITUTE|LAB|CENTER|CENTRE", toupper(parts[2]))) {
        dept_name <- parts[2]
      }
    }
    
    # 标准化机构名称
    inst_name_clean <- toupper(gsub("\\s+", " ", inst_name))
    inst_name_clean <- gsub("UNIV", "UNIVERSITY", inst_name_clean)
    inst_name_clean <- gsub("COLL", "COLLEGE", inst_name_clean)
    inst_name_clean <- gsub("INST", "INSTITUTE", inst_name_clean)
    inst_name_clean <- gsub("NATL", "NATIONAL", inst_name_clean)
    
    # 处理国家代码映射
    if (!is.null(country) && is.null(country_code)) {
      country_map <- list(
        "USA" = "US", "UNITED STATES" = "US", "AMERICA" = "US", "U.S.A." = "US",
        "UK" = "GB", "UNITED KINGDOM" = "GB", "ENGLAND" = "GB", 
        "CHINA" = "CN", "GERMANY" = "DE", "FRANCE" = "FR", 
        "ITALY" = "IT", "JAPAN" = "JP", "CANADA" = "CA",
        "AUSTRALIA" = "AU", "BRAZIL" = "BR", "INDIA" = "IN",
        "SPAIN" = "ES", "NETHERLANDS" = "NL", "SWEDEN" = "SE",
        "DENMARK" = "DK", "NORWAY" = "NO", "SWITZERLAND" = "CH"
      )
      country_code <- country_map[[toupper(country)]]
    }
    
    # 返回机构信息列表
    list(
      raw_address = addr,
      institution = inst_name,
      institution_clean = inst_name_clean,
      department = dept_name,
      city = city,
      state_code = state_code,
      country = country,
      country_code = country_code
    )
  })
  
  # 移除空值并返回结果
  institutions <- institutions[!sapply(institutions, is.null)]
  return(institutions)
}

# 增强CiteSpace格式生成函数
generate_citespace_format <- function(data, output_file) {
  required_cols <- c("AU", "PY", "SO", "VL", "IS", "BP", "EP", "TI", "AB", "DI", "TC")
  missing_cols <- setdiff(required_cols, colnames(data))
  
  if (length(missing_cols) > 0) {
    warning("缺少以下列用于CiteSpace格式: ", paste(missing_cols, collapse=", "))
    for (col in missing_cols) {
      data[[col]] <- NA_character_
    }
  }
  
  data$AU_formatted <- sapply(data$AU, function(au) {
    if (is.na(au)) return("")
    authors <- unlist(strsplit(au, ";"))
    authors <- trimws(authors)
    paste(authors, collapse = "; ")
  })
  
  data$BP_EP <- ifelse(!is.na(data$BP) & !is.na(data$EP), 
                       paste0(data$BP, "-", data$EP),
                       ifelse(!is.na(data$BP), data$BP, ""))
  
  data$VL_IS <- ifelse(!is.na(data$VL) & !is.na(data$IS), 
                       paste0(data$VL, "(", data$IS, ")"),
                       ifelse(!is.na(data$VL), data$VL, ""))
  
  data$formatted_entry <- with(data, {
    paste0(
      AU_formatted, " (", PY, "). ",
      TI, ". ",
      SO, ", ",
      VL_IS, ", ",
      BP_EP, ". ",
      ifelse(!is.na(DI), paste0("DOI: ", DI), "")
    )
  })
  
  citespace_data <- data %>%
    select(
      AU = AU_formatted,
      PY,
      J9 = SO,
      VL,
      IS,
      BP,
      EP,
      DI,
      TI,
      DE,
      ID,
      AB,
      CR
    ) %>%
    mutate(across(everything(), ~ifelse(is.na(.), "", as.character(.))))
  
  write.table(
    citespace_data, 
    file = output_file,
    sep = "\t", 
    row.names = FALSE, 
    quote = TRUE,
    fileEncoding = "UTF-8"
  )
  
  cat("已生成兼容CiteSpace格式文件:", output_file, "\n")
  return(citespace_data)
}

# 基本主题分析函数
# 输入：数据框、文本字段名、最小频率阈值、停用词文件
# 输出：词频统计结果
analyze_text_topics <- function(data, text_field, min_freq = 3, stopwords_file = NULL) {
  # 检查字段是否存在
  if (!(text_field %in% colnames(data))) {
    stop(paste("字段不存在:", text_field))
  }
  
  # 加载自定义停用词
  custom_stopwords <- c()
  if (!is.null(stopwords_file) && file.exists(stopwords_file)) {
    custom_stopwords <- readLines(stopwords_file, warn = FALSE)
  }
  
  # 定义常见停用词
  common_stopwords <- c("a", "an", "the", "this", "that", "these", "those", "and", "or", "but", 
                      "if", "when", "where", "how", "why", "which", "what", "who", "whom", 
                      "then", "there", "here", "with", "without", "to", "from", "by", "for", 
                      "in", "on", "at", "of", "is", "are", "was", "were", "be", "being", "been",
                      "have", "has", "had", "do", "does", "did", "can", "could", "will", "would",
                      "shall", "should", "may", "might", "must", "as", "we", "our", "us", "study",
                      "studies", "result", "results", "method", "methods", "conclusion", "conclusions")
  
  # 合并停用词列表
  all_stopwords <- unique(c(common_stopwords, custom_stopwords))
  
  # 提取并清理文本
  text_data <- data[[text_field]]
  text_data <- text_data[!is.na(text_data)]
  text_data <- tolower(text_data)
  text_data <- gsub("[[:punct:]]", " ", text_data)
  
  # 分词
  words <- unlist(strsplit(text_data, "\\s+"))
  words <- words[words != ""]
  words <- words[!words %in% all_stopwords]
  
  # 计算词频
  word_freq <- table(words)
  word_freq_df <- as.data.frame(word_freq, stringsAsFactors = FALSE)
  colnames(word_freq_df) <- c("term", "frequency")
  
  # 排序和过滤
  word_freq_df <- word_freq_df[order(word_freq_df$frequency, decreasing = TRUE), ]
  word_freq_df <- word_freq_df[word_freq_df$frequency >= min_freq, ]
  
  # 计算相对频率和累积频率
  total_words <- sum(word_freq_df$frequency)
  word_freq_df$rel_frequency <- word_freq_df$frequency / total_words
  word_freq_df$cumulative_freq <- cumsum(word_freq_df$rel_frequency)
  
  return(word_freq_df)
}

# --- 数据库初始化 (使用增强表结构) ---
cat("\n--- 数据库初始化 (缓存表 v5 - 增强字段) ---\n")
con <- dbConnect(RSQLite::SQLite(), dbname = db_file)
cat("已连接到 SQLite 数据库:", db_file, "\n")
cache_table_name <- "api_cache_v5"

if (!dbExistsTable(con, cache_table_name)) {
  cat("缓存表 '", cache_table_name, "' 不存在，正在创建...\n")
  
  dbExecute(con, paste0("CREATE TABLE ", cache_table_name, " (
    doi TEXT, 
    identifier TEXT NOT NULL, 
    identifier_type TEXT NOT NULL, 
    api_source TEXT NOT NULL, 
    query_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP, 
    title_api TEXT, 
    authors_api TEXT, 
    authors_orcid_map TEXT, 
    authors_raw_json TEXT, 
    journal_api TEXT, 
    year_api INTEGER, 
    volume_api TEXT, 
    issue_api TEXT, 
    pages_api TEXT, 
    issn_p TEXT, 
    issn_e TEXT, 
    publisher_api TEXT, 
    type_api TEXT, 
    openalex_id TEXT, 
    orcid_api TEXT, 
    ror_id_api TEXT, 
    institutions_api TEXT, 
    institutions_raw_json TEXT, 
    country_codes_api TEXT, 
    concepts_api TEXT, 
    oa_status_api TEXT, 
    cited_by_count_api INTEGER, 
    url_api TEXT, 
    abstract_api TEXT, 
    alt_method TEXT, 
    raw_result_json TEXT, 
    verification_status TEXT DEFAULT 'unverified', 
    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
    researcher_id TEXT,
    scopus_id TEXT,
    institution_id TEXT,
    author_identifiers_map TEXT,
    PRIMARY KEY (identifier, identifier_type, api_source))"))
  
  cat("缓存表创建成功。\n")
  log_summary_list[["数据库初始化"]] <- tibble(步骤 = "数据库初始化", 结果 = "创建了新的缓存表 (v5 增强版)")
} else { 
  cat("缓存表 '", cache_table_name, "' 已存在。\n")
  
  table_info <- dbGetQuery(con, paste0("PRAGMA table_info(", cache_table_name, ")"))
  existing_columns <- table_info$name
  
  new_columns <- c(
    "authors_orcid_map", "authors_raw_json", "institutions_raw_json", 
    "verification_status", "last_updated", "alt_method",
    "researcher_id", "scopus_id", "institution_id", "author_identifiers_map"
  )
  
  for (col in new_columns) {
    if (!col %in% existing_columns) {
      cat("  添加新列到现有表:", col, "\n")
      if (col %in% c("authors_orcid_map", "authors_raw_json", "institutions_raw_json", "alt_method", 
                    "researcher_id", "scopus_id", "institution_id", "author_identifiers_map")) {
        dbExecute(con, paste0("ALTER TABLE ", cache_table_name, " ADD COLUMN ", col, " TEXT"))
      } else if (col == "verification_status") {
        dbExecute(con, paste0("ALTER TABLE ", cache_table_name, " ADD COLUMN ", col, " TEXT DEFAULT 'unverified'"))
      } else if (col == "last_updated") {
        dbExecute(con, paste0("ALTER TABLE ", cache_table_name, " ADD COLUMN ", col, " DATETIME DEFAULT CURRENT_TIMESTAMP"))
      }
    }
  }
  
  log_summary_list[["数据库初始化"]] <- tibble(步骤 = "数据库初始化", 结果 = "使用了已存在的缓存表并检查更新")
}

# --- 1. 数据导入与基础清理 ---
cat("\n--- 阶段 1: 数据导入与基础清理 ---\n")

# --- 1.1 数据导入 ---
cat("1.1 正在从文件导入数据:", raw_data_file, "\n")
if (!file.exists(raw_data_file)) { 
  stop("错误：未找到原始数据文件: ", raw_data_file) 
}

wos_data_list <- tryCatch({ 
  bibliometrix::convert2df(file = raw_data_file, dbsource = "wos", format = "plaintext") 
}, error = function(e) { 
  stop("错误：使用 bibliometrix::convert2df 读取时出错: ", e$message) 
})

if (is.list(wos_data_list) && !is.data.frame(wos_data_list)) { 
  if (length(wos_data_list) == 1 && is.data.frame(wos_data_list[[1]])) { 
    wos_df <- as_tibble(wos_data_list[[1]]) 
  } else { 
    warning("警告：convert2df 返回列表，尝试合并行。")
    wos_df <- bind_rows(wos_data_list) %>% as_tibble() 
  } 
} else if (is.data.frame(wos_data_list)) { 
  wos_df <- as_tibble(wos_data_list) 
} else { 
  stop("错误：bibliometrix::convert2df 返回未知格式。") 
}

cat("数据导入成功。\n")
initial_dims <- paste(nrow(wos_df), "行,", ncol(wos_df), "列")
cat("初始维度:", initial_dims, "\n")
log_summary_list[["导入"]] <- tibble(
  步骤 = "数据导入", 
  结果 = paste("成功导入", nrow(wos_df), "条记录"), 
  维度 = initial_dims
)

# --- 1.2 数据字段检查与基本信息 ---
cat("\n1.2 数据字段检查与基本信息\n")
cat("可用字段列表:\n")
print(colnames(wos_df))
cat("\n核心字段统计:\n")
core_fields <- c("TI", "AU", "AF", "SO", "PY", "DI", "UT", "AB", "C1", "RP", "TC", "CR")
core_fields_available <- intersect(core_fields, colnames(wos_df))
cat("可用核心字段:", paste(core_fields_available, collapse=", "), "\n")
cat("缺失核心字段:", paste(setdiff(core_fields, core_fields_available), collapse=", "), "\n")

log_summary_list[["核心字段"]] <- tibble(
  步骤 = "核心字段检查",
  可用字段 = paste(core_fields_available, collapse=", "),
  缺失字段 = paste(setdiff(core_fields, core_fields_available), collapse=", ")
)

# --- 1.3 初步字段标准化 ---
cat("\n1.3 正在执行初步字段标准化...\n")

cat("正在标准化日期 (使用 PY)...\n")
wos_df <- wos_df %>% 
  mutate(
    PY_numeric = suppressWarnings(as.numeric(PY)),
    date_precision = case_when(
      !is.na(PY_numeric) & nchar(as.character(PY_numeric)) == 4 ~ "年份有效",
      !is.na(PY) & is.na(PY_numeric) ~ "原始PY无效",
      is.na(PY) ~ "缺失"
    )
  )

# 移除date_iso字段，只保留年份信息
# wos_df$date_iso <- NULL

cat("日期标准化反馈:\n")
date_prec_summary <- wos_df %>% count(date_precision, name = "数量")
print(date_prec_summary)
log_summary_list[["日期标准化"]] <- date_prec_summary %>% 
  rename(步骤 = date_precision, 结果 = 数量) %>% 
  mutate(步骤 = paste("日期标准化:", 步骤))

cat("正在执行基础文本清理...\n")
text_cols <- c("TI", "AB", "DE", "ID", "SO", "JI", "AU", "AF", "C1", "CR", "TC")
wos_df <- wos_df %>% 
  mutate(across(any_of(text_cols) & where(is.character), ~ str_squish(str_trim(.))))

if ("TI" %in% colnames(wos_df)) {
  wos_df <- wos_df %>%
    mutate(
      TI_orig = TI,
      TI_lower = str_to_lower(TI),
      TI = str_to_title(TI)
    )
  cat("  已标准化标题格式并保留原始版本。\n")
}

cat("文本清理已应用。\n")
log_summary_list[["文本清理"]] <- tibble(步骤 = "基础文本清理", 结果 = "已应用")

# 标准化WOS号格式
if ("UT" %in% colnames(wos_df)) {
  wos_df <- wos_df %>%
    mutate(
      UT_orig = UT,
      UT_cleaned = str_extract(UT, "WOS:\\d+"),
      UT_cleaned = na_if(UT_cleaned, "")
    )
  cat("  已标准化WOS号格式并保留原始版本。\n")
  
  wos_stats <- wos_df %>% 
    summarize(
      有WOS号记录 = as.character(sum(!is.na(UT_cleaned))),
      无WOS号记录 = as.character(sum(is.na(UT_cleaned))),
      WOS号覆盖率 = sprintf("%.1f%%", sum(!is.na(UT_cleaned)) / n() * 100)
    )
  cat("WOS号统计:\n")
  print(wos_stats)
  
  log_summary_list[["WOS号统计"]] <- wos_stats %>%
    pivot_longer(everything(), names_to = "指标", values_to = "值") %>%
    mutate(步骤 = "WOS号标准化")
}

# --- 1.4 缺失数据分析 ---
cat("\n1.4 正在分析缺失数据...\n")
missing_summary <- wos_df %>% miss_var_summary()
cat("缺失数据摘要 (按百分比降序所有列):\n")
print(missing_summary %>% arrange(desc(pct_miss)), n = Inf)

# 字段中文解释映射
field_mapping <- c(
  "TI" = "标题",
  "AU" = "作者",
  "PY" = "出版年份",
  "SO" = "来源出版物",
  "DI" = "DOI",
  "UT" = "WOS号",
  "AB" = "摘要",
  "DE" = "作者关键词",
  "ID" = "关键词增强",
  "C1" = "作者单位",
  "RP" = "通讯作者",
  "CR" = "参考文献",
  "TC" = "引用次数",
  "SC" = "研究领域",
  "WC" = "Web of Science类别",
  "J9" = "期刊缩写",
  "JI" = "期刊ISO缩写",
  "PA" = "出版商地址",
  "SN" = "ISSN",
  "BN" = "ISBN",
  "VL" = "卷",
  "IS" = "期",
  "BP" = "起始页",
  "EP" = "结束页",
  "DT" = "文档类型",
  "LA" = "语言",
  "CT" = "会议标题",
  "CY" = "会议年份",
  "CL" = "会议地点",
  "SP" = "会议赞助商",
  "FU" = "资助机构",
  "NR" = "参考文献数量"
)

log_summary_list[["缺失数据摘要"]] <- missing_summary %>% 
  arrange(desc(pct_miss)) %>% 
  mutate(
    字段中文名 = case_when(
      variable %in% names(field_mapping) ~ paste0(variable, "(", field_mapping[variable], ")"),
      TRUE ~ variable
    )
  ) %>%
  rename(列名 = variable, 缺失数 = n_miss, 缺失百分比 = pct_miss)

high_missing_fields <- missing_summary %>% 
  filter(pct_miss > 50) %>% 
  pull(variable)
if (length(high_missing_fields) > 0) {
  cat("警告: 以下字段缺失率超过50%:", paste(high_missing_fields, collapse=", "), "\n")
  log_summary_list[["高缺失字段"]] <- tibble(
    步骤 = "高缺失字段警告", 
    字段 = paste(high_missing_fields, collapse=", "),
    说明 = "这些字段缺失率超过50%，需优先考虑API补全"
  )
}

# --- 2. 精确去重处理 ---
cat("\n--- 阶段 2: 数据去重 (DOI和WOS号精确匹配) ---\n")
initial_row_count_dedup <- nrow(wos_df)
cat("去重前记录数:", initial_row_count_dedup, "\n")

# --- 2.1 DOI精确去重 ---
doi_col <- "DI"
if (doi_col %in% colnames(wos_df)) {
  cat("正在基于 DOI 列进行去重:", doi_col, "\n")
  
  wos_df <- wos_df %>% 
    mutate(
      doi_cleaned = str_to_lower(str_remove(!!sym(doi_col), "^doi:")),
      doi_cleaned = na_if(doi_cleaned, "")
    ) 
  
  duplicates_doi <- wos_df %>% 
    filter(!is.na(doi_cleaned)) %>% 
    add_count(doi_cleaned) %>% 
    filter(n > 1) %>% 
    arrange(doi_cleaned, SR)
  
  if (nrow(duplicates_doi) > 0) { 
    cat("发现", nrow(duplicates_doi), "条记录涉及", 
        duplicates_doi %>% distinct(doi_cleaned) %>% nrow(), "组重复 DOI。\n")
    
    duplicate_groups <- duplicates_doi %>%
      select(doi_cleaned, SR, TI, PY) %>%
      group_by(doi_cleaned) %>%
      mutate(group_id = cur_group_id())
    
    log_summary_list[["DOI重复详情"]] <- duplicate_groups
    
    duplicate_doi_file <- file.path(output_location, "doi_duplicates_details_v5.xlsx")
    write_xlsx(duplicate_groups, path = duplicate_doi_file)
    cat("  DOI重复详情已保存至:", duplicate_doi_file, "\n")
  } else { 
    cat("未发现基于 DOI 精确匹配的重复记录。\n") 
  }
  
  wos_df_doi <- wos_df %>% filter(!is.na(doi_cleaned))
  wos_df_no_doi <- wos_df %>% filter(is.na(doi_cleaned))
  
  wos_df_doi_dedup <- wos_df_doi %>% 
    group_by(doi_cleaned) %>%
    mutate(all_SR = paste(SR, collapse = "; ")) %>%
    slice(1) %>% 
    ungroup()
  
  wos_df <- bind_rows(wos_df_doi_dedup, wos_df_no_doi)
  
  final_row_count_dedup <- nrow(wos_df)
  removed_count_dedup <- initial_row_count_dedup - final_row_count_dedup
  cat("DOI 精确去重后记录数:", final_row_count_dedup, "\n")
  cat("移除的记录数:", removed_count_dedup, "\n")
  
  log_summary_list[["DOI去重"]] <- tibble(
    步骤 = "DOI精确去重", 
    结果 = paste("移除了", removed_count_dedup, "条记录"), 
    剩余记录 = final_row_count_dedup,
    详情 = ifelse(removed_count_dedup > 0, 
                paste("涉及", duplicates_doi %>% distinct(doi_cleaned) %>% nrow(), "组重复DOI"), 
                "无重复")
  )
} else { 
  cat("未找到 DOI 列 '", doi_col, "'。跳过 DOI 精确去重。\n")
  log_summary_list[["DOI去重"]] <- tibble(
    步骤 = "DOI精确去重", 
    结果 = "未找到DOI列，跳过"
  ) 
}

# --- 2.1.2 WOS号(UT字段)精确去重 ---
ut_col <- "UT"
if (ut_col %in% colnames(wos_df)) {
  cat("\n正在基于 WOS号(UT字段) 进行去重:", ut_col, "\n")
  
  # 提取WOS号，保留数字部分
  wos_df <- wos_df %>% 
    mutate(
      ut_cleaned = str_extract(!!sym(ut_col), "WOS:\\d+"),
      ut_cleaned = na_if(ut_cleaned, "")
    )
  
  duplicates_ut <- wos_df %>% 
    filter(!is.na(ut_cleaned)) %>% 
    add_count(ut_cleaned) %>% 
    filter(n > 1) %>% 
    arrange(ut_cleaned, SR)
  
  if (nrow(duplicates_ut) > 0) { 
    cat("发现", nrow(duplicates_ut), "条记录涉及", 
        duplicates_ut %>% distinct(ut_cleaned) %>% nrow(), "组重复 WOS号。\n")
    
    duplicate_ut_groups <- duplicates_ut %>%
      select(ut_cleaned, SR, TI, PY) %>%
      group_by(ut_cleaned) %>%
      mutate(group_id = cur_group_id())
    
    log_summary_list[["WOS号重复详情"]] <- duplicate_ut_groups
    
    duplicate_ut_file <- file.path(output_location, "wos_duplicates_details_v5.xlsx")
    write_xlsx(duplicate_ut_groups, path = duplicate_ut_file)
    cat("  WOS号重复详情已保存至:", duplicate_ut_file, "\n")
  } else { 
    cat("未发现基于 WOS号 精确匹配的重复记录。\n") 
  }
  
  before_ut_dedup <- nrow(wos_df)
  
  wos_df_ut <- wos_df %>% filter(!is.na(ut_cleaned))
  wos_df_no_ut <- wos_df %>% filter(is.na(ut_cleaned))
  
  wos_df_ut_dedup <- wos_df_ut %>% 
    group_by(ut_cleaned) %>%
    mutate(all_SR = paste(SR, collapse = "; ")) %>%
    slice(1) %>% 
    ungroup()
  
  wos_df <- bind_rows(wos_df_ut_dedup, wos_df_no_ut)
  
  removed_count_ut_dedup <- before_ut_dedup - nrow(wos_df)
  cat("WOS号 精确去重后记录数:", nrow(wos_df), "\n")
  cat("移除的记录数:", removed_count_ut_dedup, "\n")
  
  log_summary_list[["WOS号去重"]] <- tibble(
    步骤 = "WOS号精确去重", 
    结果 = paste("移除了", removed_count_ut_dedup, "条记录"), 
    剩余记录 = nrow(wos_df),
    详情 = ifelse(removed_count_ut_dedup > 0, 
                paste("涉及", duplicates_ut %>% distinct(ut_cleaned) %>% nrow(), "组重复WOS号"), 
                "无重复")
  )
} else { 
  cat("未找到 WOS号 列 '", ut_col, "'。跳过 WOS号 精确去重。\n")
  log_summary_list[["WOS号去重"]] <- tibble(
    步骤 = "WOS号精确去重", 
    结果 = "未找到WOS号列，跳过"
  ) 
}

# --- 2.2 无DOI无WOS号记录模糊匹配去重 (强化版) ---
cat("\n--- 阶段 2.2: 无DOI无WOS号文献模糊匹配去重 (强化版) ---\n")

find_potential_duplicates_no_identifiers <- function(data) {
  # 筛选有DOI或WOS号的记录
  data_with_identifiers <- data %>%
    filter((!is.na(doi_cleaned) & doi_cleaned != "") | (!is.na(ut_cleaned) & ut_cleaned != ""))

  # 筛选既无DOI又无WOS号的记录
  data_no_identifiers <- data %>%
    filter((is.na(doi_cleaned) | doi_cleaned == "") & (is.na(ut_cleaned) | ut_cleaned == ""))

  # 计算并记录无标识符记录统计
  no_identifier_stats <- tibble(
    总记录数 = as.character(nrow(data)),
    有标识符记录数 = as.character(nrow(data_with_identifiers)),
    无标识符记录数 = as.character(nrow(data_no_identifiers)),
    无标识符记录比例 = sprintf("%.2f%%", nrow(data_no_identifiers)/nrow(data)*100)
  )

  log_summary_list[["无标识符记录统计"]] <- no_identifier_stats %>%
    pivot_longer(everything(), names_to = "指标", values_to = "值") %>%
    mutate(
      步骤 = "无标识符记录识别",
      说明 = case_when(
        指标 == "总记录数" ~ "数据集中的总记录数",
        指标 == "有标识符记录数" ~ "包含DOI或WOS号的记录数",
        指标 == "无标识符记录数" ~ "既无DOI又无WOS号的记录数",
        指标 == "无标识符记录比例" ~ "无标识符记录占总体的百分比",
        TRUE ~ NA_character_
      )
    )

  # 输出统计信息
  cat("标识符统计:\n")
  print(no_identifier_stats)

  if (nrow(data_no_identifiers) < 2) {
    cat("无标识符记录数不足，跳过模糊匹配去重\n")
    return(NULL)
  }

  cat("正在对", nrow(data_no_identifiers), "条无标识符记录执行模糊匹配去重...\n")
  cat("无标识符记录比例:", no_identifier_stats$无标识符记录比例, "\n")
  cat("使用TF-IDF向量化和余弦相似度进行比较，匹配字段组合：标题(主要)+出版年(±1年)+第一作者姓氏\n")
  cat("相似度阈值设置：>=0.90为可能重复，0.85-0.90的条目标记为待人工审核\n")

  # 准备比较数据
  no_doi_compare <- data_no_identifiers %>%
    select(SR, TI_lower, AU, PY, SO) %>%
    mutate(
      AU_first = sapply(strsplit(as.character(AU), ";"), function(x) {
        if (length(x) > 0) {
          first_author <- trimws(x[1])
          # 提取姓氏 (假设姓氏是最后一个词)
          surname <- tail(unlist(strsplit(first_author, " ")), 1)
          return(tolower(surname))
        } else {
          return(NA_character_)
        }
      }),
      SO_abbr = stringi::stri_trans_general(SO, "latin-ascii") %>%
        str_to_upper() %>%
        str_replace_all("[^A-Z0-9]", " ") %>%
        str_squish(),
      PY_numeric = as.numeric(PY),
      record_id = row_number()
    )

  # 移除缺失关键字段的记录
  no_doi_compare <- no_doi_compare %>%
    filter(!is.na(TI_lower) & TI_lower != "" & !is.na(PY_numeric))

  cat("处理", nrow(no_doi_compare), "条有效记录用于TF-IDF相似度计算\n")

  # 创建TF-IDF向量
  # 使用text2vec和Matrix包处理文本向量化
  
  # 创建TF-IDF向量化器
  it_train <- itoken(no_doi_compare$TI_lower,
                   preprocessor = tolower,
                   tokenizer = word_tokenizer,
                   ids = no_doi_compare$record_id)
  
  vocab <- create_vocabulary(it_train)
  vectorizer <- vocab_vectorizer(vocab)
  dtm <- create_dtm(it_train, vectorizer)
  tfidf <- TfIdf$new()
  dtm_tfidf <- fit_transform(dtm, tfidf)
  
  # 计算标题余弦相似度矩阵 (优化计算)
  similarity_matrix <- sim2(dtm_tfidf, dtm_tfidf, method = "cosine", norm = "l2")
  
  # 初始化结果列表
  potential_dupes <- list()
  
  cat("计算余弦相似度和筛选可能的重复记录...\n")
  
  # 构建记录ID到索引的映射
  idx_map <- setNames(seq_len(nrow(no_doi_compare)), no_doi_compare$record_id)
  
  # 遍历上三角矩阵中的相似度
  for (i in 1:(nrow(similarity_matrix)-1)) {
    record_i <- no_doi_compare[i, ]
    
    # 获取与当前记录相似度高的候选记录
    sim_scores <- similarity_matrix[i, (i+1):ncol(similarity_matrix)]
    high_sim_idx <- which(sim_scores >= 0.85) + i
    
    if (length(high_sim_idx) > 0) {
      for (j in high_sim_idx) {
        record_j <- no_doi_compare[j, ]
        
        # 检查年份差异
        year_diff <- abs(record_i$PY_numeric - record_j$PY_numeric)
        if (is.na(year_diff) || year_diff > 1) next
        
        # 计算第一作者相似度
        if (!is.na(record_i$AU_first) && !is.na(record_j$AU_first) && 
            record_i$AU_first != "" && record_j$AU_first != "") {
          author_sim <- stringdist::stringsim(record_i$AU_first, record_j$AU_first, method = "jw")
        } else {
          author_sim <- 0
        }
        
        # 如果作者相似度足够高或姓氏一致
        author_match <- author_sim > 0.8 || record_i$AU_first == record_j$AU_first
        
        if (author_match) {
          # 计算期刊相似度（作为附加信息）
          if (!is.na(record_i$SO_abbr) && !is.na(record_j$SO_abbr) && 
              record_i$SO_abbr != "" && record_j$SO_abbr != "") {
            journal_sim <- stringdist::stringsim(record_i$SO_abbr, record_j$SO_abbr, method = "jw")
          } else {
            journal_sim <- 0
          }
          
          # 计算组合相似度
          title_sim <- similarity_matrix[i, j]
          combined_sim <- (title_sim * 0.7) + (author_sim * 0.2) + (journal_sim * 0.1)
          
          # 判断相似度级别
          similarity_level <- case_when(
            combined_sim >= 0.90 ~ "可能重复",
            combined_sim >= 0.85 ~ "待人工审核",
            TRUE ~ "不太可能重复"
          )
          
          if (combined_sim >= 0.85) {
            potential_dupes[[length(potential_dupes) + 1]] <- list(
              record1_id = record_i$record_id,
              record2_id = record_j$record_id,
              record1_sr = record_i$SR,
              record2_sr = record_j$SR,
              title_sim = title_sim,
              author_sim = author_sim,
              journal_sim = journal_sim,
              combined_sim = combined_sim,
              similarity_level = similarity_level,
              title1 = record_i$TI_lower,
              title2 = record_j$TI_lower,
              author1 = record_i$AU_first,
              author2 = record_j$AU_first,
              journal1 = record_i$SO_abbr,
              journal2 = record_j$SO_abbr,
              year1 = record_i$PY_numeric,
              year2 = record_j$PY_numeric
            )
          }
        }
      }
    }
  }

  if (length(potential_dupes) > 0) {
    potential_dupes_df <- bind_rows(lapply(potential_dupes, as_tibble)) %>%
      arrange(desc(combined_sim))

    # 统计不同相似度级别的数量
    review_count <- sum(potential_dupes_df$similarity_level == "待人工审核")
    duplicate_count <- sum(potential_dupes_df$similarity_level == "可能重复")
    
    cat("找到", nrow(potential_dupes_df), "组可能的无DOI重复记录\n")
    cat("其中", duplicate_count, "组相似度>=0.90（可能重复）\n")
    cat("其中", review_count, "组相似度在0.85-0.90之间（待人工审核）\n")
    
    return(potential_dupes_df)
  } else {
    cat("未发现潜在的无DOI重复记录\n")
    return(NULL)
  }
}

# 执行模糊匹配去重
potential_dupes_df <- find_potential_duplicates_no_identifiers(wos_df)
if (!is.null(potential_dupes_df)) {
  write_xlsx(potential_dupes_df, path = fuzzy_duplicates_file)
  cat("已保存潜在重复记录到:", fuzzy_duplicates_file, "\n")

  log_summary_list[["无DOI模糊匹配"]] <- tibble(
    步骤 = "无DOI模糊匹配去重",
    结果 = paste("发现", nrow(potential_dupes_df), "组潜在重复"),
    相似度阈值 = "0.90(可能重复)/0.85(待人工审核)",
    算法 = "TF-IDF向量化+余弦相似度",
    说明 = "使用标题+出版年(±1年)+第一作者姓氏匹配"
  )
} else {
  log_summary_list[["无DOI模糊匹配"]] <- tibble(
    步骤 = "无DOI模糊匹配去重",
    结果 = "未发现潜在重复"
  )
}

# --- 3. 机构信息提取与标准化 ---
cat("\n--- 阶段 3: 机构信息提取与标准化 ---\n")

process_institutions <- function(data) {
  if (!"C1" %in% colnames(data)) {
    cat("错误: C1列不存在\n")
    return(data)
  }
  
  data$C1 <- as.character(data$C1)
  
  cat("正在提取机构信息...\n")
  institutions_list <- lapply(1:nrow(data), function(i) {
    c1 <- data$C1[i]
    if (is.na(c1) || c1 == "") return(list())
    
    insts <- extract_institutions_enhanced(c1)
    if (length(insts) == 0) return(list())
    
    lapply(insts, function(inst) {
      inst$SR <- data$SR[i]
      inst$PY <- data$PY[i]
      inst$DI <- data$doi_cleaned[i]
      return(inst)
    })
  })
  
  institutions_flat <- unlist(institutions_list, recursive = FALSE)
  institutions_flat <- institutions_flat[!sapply(institutions_flat, is.null)]
  
  if (length(institutions_flat) > 0) {
    institutions_df <- bind_rows(institutions_flat)
    
    write_xlsx(institutions_df, path = file.path(output_location, "extracted_institutions_v5.xlsx"))
    cat("已提取", nrow(institutions_df), "条机构信息记录\n")
    
    unique_institutions <- institutions_df %>%
      group_by(institution_clean) %>%
      summarize(
        institution = first(institution),
        country = first(country),
        country_code = first(country_code),
        state_code = first(state_code),
        city = first(city),
        occurrence_count = n(),
        .groups = "drop"
      ) %>%
      arrange(desc(occurrence_count))
    
    write_xlsx(unique_institutions, path = unique_institutions_file)
    cat("共发现", nrow(unique_institutions), "个唯一机构\n")
    
    log_summary_list[["机构提取"]] <- tibble(
      步骤 = "机构信息提取",
      总提取记录数 = nrow(institutions_df),
      唯一机构数 = nrow(unique_institutions),
      说明 = "已保存至unique_institutions_v5.xlsx"
    )
    
    data <- data %>%
      rowwise() %>%
      mutate(
        institutions_count = if (is.na(C1)) 0 else length(extract_institutions_enhanced(C1)),
        primary_institution = if (institutions_count > 0) {
          insts <- extract_institutions_enhanced(C1)
          if (length(insts) > 0) insts[[1]]$institution else NA_character_
        } else {
          NA_character_
        },
        primary_country = if (institutions_count > 0) {
          insts <- extract_institutions_enhanced(C1)
          if (length(insts) > 0 && !is.null(insts[[1]]$country)) insts[[1]]$country else NA_character_
        } else {
          NA_character_
        },
        primary_country_code = if (institutions_count > 0) {
          insts <- extract_institutions_enhanced(C1)
          if (length(insts) > 0 && !is.null(insts[[1]]$country_code)) insts[[1]]$country_code else NA_character_
        } else {
          NA_character_
        }
      ) %>%
      ungroup()
    
    country_stats <- institutions_df %>%
      filter(!is.na(country)) %>%
      group_by(country, country_code) %>%
      summarize(count = n(), .groups = "drop") %>%
      arrange(desc(count))
    
    write_xlsx(country_stats, path = file.path(output_location, "country_distribution_v5.xlsx"))
    cat("已生成国家分布统计，共", nrow(country_stats), "个国家/地区\n")
    
    cat("已将机构计数和主要机构信息添加到原始数据\n")
  } else {
    cat("未提取到任何机构信息\n")
    log_summary_list[["机构提取"]] <- tibble(
      步骤 = "机构信息提取",
      结果 = "未提取到机构信息",
      说明 = "C1字段可能为空或格式不规范"
    )
  }
  
  return(list(data = data, institutions_df = if (exists("institutions_df")) institutions_df else NULL))
}

institutions_results <- process_institutions(wos_df)
wos_df <- institutions_results$data
institutions_df <- institutions_results$institutions_df

# --- 4. API数据补充 ---
cat("\n--- 阶段 4: API数据补充 ---\n")

# --- 4.1 Crossref API数据丰富 ---
cat("4.1 准备通过Crossref API补充数据...\n")

# 初始化API结果列表，避免"找不到对象"错误
new_api_results_list_cr <- list()
new_api_results_list_oa <- list()

# 设置API缓存数据库
db_file <- file.path(output_location, "api_cache_v5.sqlite")
cat("使用API缓存数据库:", db_file, "\n")

con <- dbConnect(SQLite(), db_file)

# 检查并创建缓存表
if (!dbExistsTable(con, "crossref_cache")) {
  cat("创建Crossref API缓存表...\n")
  dbExecute(con, "
    CREATE TABLE crossref_cache (
      query TEXT PRIMARY KEY,
      result TEXT,
      timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  ")
}

if (!dbExistsTable(con, "openalex_cache")) {
  cat("创建OpenAlex API缓存表...\n")
  dbExecute(con, "
    CREATE TABLE openalex_cache (
      query TEXT PRIMARY KEY,
      result TEXT,
      timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  ")
}

# Crossref与OpenAlex并行API调用增强函数
fetch_enhanced_metadata <- function(dois, wos_ids, max_attempts = 3, throttle = 0.5, batch_size = 25) {
  if (length(dois) == 0 && length(wos_ids) == 0) {
    return(list(crossref_data = list(), openalex_data = list()))
  }

  cat("启动DOI和WOS号并行处理 (批次大小:", batch_size, ")...\n")

  # 准备查询列表
  query_ids <- unique(c(dois, wos_ids))
  query_ids <- query_ids[!is.na(query_ids)]

  if (length(query_ids) == 0) {
    cat("无有效标识符可查询。\n")
    return(list(crossref_data = list(), openalex_data = list()))
  }

  # 定义并行处理批次函数
  process_batch_parallel <- function(batch_ids, max_attempts = 3, throttle = 0.5) {
    start_time <- Sys.time()
    
    # 初始化结果
    batch_crossref_results <- list()
    batch_openalex_results <- list()
    batch_diagnostics <- list()
    
    # 对每个ID并行处理Crossref和OpenAlex API
    for (id in batch_ids) {
      # 检查是DOI还是WOS号
      is_doi <- grepl("^10\\.", id, ignore.case = TRUE)
      identifier_type <- ifelse(is_doi, "doi", "wos")
      
      # 并行查询两个API (使用future并行包)
      # 直接使用future和future.apply包（已在脚本开头加载）
      
      # 设置并行处理
      future::plan(future::multisession, workers = 2)
      
      api_results <- future.apply::future_lapply(
        list("crossref", "openalex"), 
        function(api) {
          if (api == "crossref") {
            if (identifier_type == "doi") {
              result <- tryCatch({
                safe_cr_enhanced(id, identifier_type = "doi", max_attempts = max_attempts)
              }, error = function(e) {
                list(error = e$message, result = NULL)
              })
            } else {
              result <- tryCatch({
                safe_cr_enhanced(id, identifier_type = "wos", max_attempts = max_attempts)
              }, error = function(e) {
                list(error = e$message, result = NULL)
              })
            }
            return(list(api = "crossref", result = result))
          } else {
            # 只有DOI才能查询OpenAlex
            if (identifier_type == "doi") {
              result <- tryCatch({
                fetch_openalex_simplified(id, retries = max_attempts)
              }, error = function(e) {
                list(error = e$message, result = NULL)
              })
              return(list(api = "openalex", result = result))
            } else {
              return(list(api = "openalex", result = list(error = "非DOI标识符不支持OpenAlex", result = NULL)))
            }
          }
        }
      )
      
      # 恢复顺序处理计划
      future::plan(future::sequential)
      
      # 提取结果
      cr_result <- NULL
      oa_result <- NULL
      
      for (res in api_results) {
        if (res$api == "crossref") {
          cr_result <- res$result
          batch_crossref_results[[id]] <- cr_result
        } else {
          oa_result <- res$result
          batch_openalex_results[[id]] <- oa_result
        }
      }
      
      # 检查结果状态
      cr_success <- !is.null(cr_result) && is.null(cr_result$error) && !is.null(cr_result$result)
      oa_success <- !is.null(oa_result) && is.null(oa_result$error) && !is.null(oa_result$result)
      
      # 记录诊断信息
      end_time <- Sys.time()
      response_time <- as.numeric(difftime(end_time, start_time, units = "secs")) * 1000
      
      batch_diagnostics[[id]] <- list(
        id = id,
        identifier_type = identifier_type,
        crossref_success = cr_success,
        openalex_success = oa_success,
        response_time = response_time
      )
      
      # 防止API限制
      Sys.sleep(throttle)
    }
    
    return(list(
      crossref_data = batch_crossref_results,
      openalex_data = batch_openalex_results, 
      diagnostics = batch_diagnostics
    ))
  }

  # 分批处理
  total_batches <- ceiling(length(query_ids) / batch_size)
  pb <- progress_bar$new(
    format = "[:bar] :percent 已完成 (批次: :current/:total, 已用时: :elapsed, 剩余: :eta)",
    total = total_batches,
    clear = FALSE,
    width = 80
  )

  # 结果存储
  crossref_results <- list()
  openalex_results <- list()
  api_diagnostics <- list()

  # 分批处理标识符
  for (i in seq(1, length(query_ids), by = batch_size)) {
    batch_ids <- query_ids[i:min(i+batch_size-1, length(query_ids))]
    batch_number <- ceiling(i/batch_size)

    log_entry <- paste0("批次 ", batch_number, "/", total_batches,
                        " (", length(batch_ids), "个标识符)")
    cat("\n处理", log_entry, "\n")

    # 并行获取Crossref和OpenAlex数据
    batch_results <- process_batch_parallel(
      batch_ids,
      max_attempts = max_attempts,
      throttle = throttle
    )

    # 合并结果
    crossref_results <- c(crossref_results, batch_results$crossref_data)
    openalex_results <- c(openalex_results, batch_results$openalex_data)
    api_diagnostics <- c(api_diagnostics, batch_results$diagnostics)

    # 更新进度条
    pb$tick()

    # 防止API限制
    Sys.sleep(throttle)
  }

  # 汇总诊断信息
  api_summary <- tibble(
    总请求数 = length(api_diagnostics),
    Crossref成功 = sum(sapply(api_diagnostics, function(x) !is.null(x$crossref_success) && x$crossref_success)),
    OpenAlex成功 = sum(sapply(api_diagnostics, function(x) !is.null(x$openalex_success) && x$openalex_success)),
    平均响应时间_毫秒 = mean(sapply(api_diagnostics, function(x) x$response_time), na.rm = TRUE)      
  )

  cat("\nAPI调用统计:\n")
  print(api_summary)

  # 返回结果
  return(list(
    crossref_data = crossref_results,
    openalex_data = openalex_results
  ))
}

# --- 4.2 OpenAlex API 数据丰富 (升级版) ---
cat("4.2 准备通过OpenAlex API补充数据...\n")

# 初始化API结果列表，避免"找不到对象"错误
new_api_results_list_oa <- list()

# 设置API缓存数据库
db_file <- file.path(output_location, "api_cache_v5.sqlite")
cat("使用API缓存数据库:", db_file, "\n")

con <- dbConnect(SQLite(), db_file)

# 检查并创建缓存表
if (!dbExistsTable(con, "openalex_cache")) {
  cat("创建OpenAlex API缓存表...\n")
  dbExecute(con, "
    CREATE TABLE openalex_cache (
      query TEXT PRIMARY KEY,
      result TEXT,
      timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  ")
}

# 设置API源
current_api_source_oa <- "openalex"

cat("正在识别需要查询 OpenAlex 的记录...\n")
all_valid_dois_oa <- wos_df %>% 
  filter(!is.na(.data[[doi_col]]) & .data[[doi_col]] != "") %>% 
  mutate(doi_cleaned = str_to_lower(str_trim(.data[[doi_col]]))) %>% 
  pull(doi_cleaned) %>% 
  unique()

cat("数据集中共有", length(all_valid_dois_oa), "个唯一有效 DOI 将尝试通过 OpenAlex 查询。\n")

query_oa <- paste0("SELECT DISTINCT identifier FROM ", cache_table_name, 
                   " WHERE api_source = ? AND identifier_type = 'doi'")
cached_dois_oa_df <- dbGetQuery(con, query_oa, params = list(current_api_source_oa))
cached_dois_oa <- cached_dois_oa_df$identifier

cat("数据库中已缓存", length(cached_dois_oa), "个 '", current_api_source_oa, "' DOI 结果。\n")

dois_to_fetch_oa <- setdiff(all_valid_dois_oa, cached_dois_oa)
cat("需要新调用 OpenAlex API 的 DOI 数量:", length(dois_to_fetch_oa), "\n")

log_summary_list[["API识别_OA"]] <- tibble(
  步骤 = "API识别(OpenAlex)", 
  待查总数 = length(all_valid_dois_oa), 
  已缓存 = length(cached_dois_oa), 
  需新获取 = length(dois_to_fetch_oa)
)

# 为解决连接超时问题设置更多选项
api_timeout_settings <- list(
  timeout = 180,           # 超时时间3分钟
  connecttimeout = 120,    # 连接超时2分钟
  retries = 5,             # 增加到5次重试
  retry_delay = 15,        # 每次重试间隔15秒
  user_agent = "文献计量分析/1.0 (研究用途)",
  use_alt_api = TRUE       # 是否尝试使用备用API端点
)

# 完全重写的安全API请求函数
safe_api_request <- function(url, doi = NULL, api_type = "openalex") {
  cat("请求API:", url, "\n")
  
  # 如果提供了DOI，尝试从备选API获取（如果主API失败）
  alt_url <- NULL
  if (api_timeout_settings$use_alt_api && !is.null(doi)) {
    if (api_type == "openalex") {
      # 备选方案1: 使用S2 API作为OpenAlex的备选
      alt_url <- paste0("https://api.semanticscholar.org/v1/paper/", URLencode(doi, reserved = TRUE))
    } else if (api_type == "crossref") {
      # 备选方案2: 使用DataCite API作为CrossRef的备选
      alt_url <- paste0("https://api.datacite.org/works/", URLencode(doi, reserved = TRUE))
    }
  }
  
  # 尝试主API请求
  for (attempt in 1:api_timeout_settings$retries) {
    tryCatch({
      cat(sprintf("尝试主API %d/%d...\n", attempt, api_timeout_settings$retries))
      
      # 创建带有更多超时设置的handle
      handle <- curl::new_handle()
      curl::handle_setopt(handle, 
                         timeout = api_timeout_settings$timeout,
                         connecttimeout = api_timeout_settings$connecttimeout,
                         low_speed_limit = 10,
                         low_speed_time = 60,
                         httpheader = c("User-Agent" = api_timeout_settings$user_agent),
                         ssl_verifypeer = FALSE,  # 禁用SSL验证，可能解决一些SSL问题
                         verbose = TRUE)          # 显示详细信息用于调试
      
      # 执行请求
      response <- curl::curl_fetch_memory(url, handle = handle)
      
      if (response$status_code >= 200 && response$status_code < 300) {
        cat("请求成功, 状态码:", response$status_code, "\n")
        return(list(success = TRUE, data = rawToChar(response$content), source = "primary"))
      } else {
        cat("请求返回错误状态码:", response$status_code, "\n")
        if (attempt < api_timeout_settings$retries) {
          cat(sprintf("等待 %d 秒后重试...\n", api_timeout_settings$retry_delay))
          Sys.sleep(api_timeout_settings$retry_delay)
        }
      }
    }, error = function(e) {
      cat("主API请求出错:", conditionMessage(e), "\n")
      if (attempt < api_timeout_settings$retries) {
        wait_time <- api_timeout_settings$retry_delay * attempt  # 渐进式增加等待时间
        cat(sprintf("等待 %d 秒后重试...\n", wait_time))
        Sys.sleep(wait_time)
      }
    })
  }
  
  # 如果主API全部失败且存在备选API，则尝试备选API
  if (!is.null(alt_url)) {
    cat("主API请求失败，尝试备选API:", alt_url, "\n")
    
    for (attempt in 1:2) { # 备选API少尝试几次
      tryCatch({
        cat(sprintf("尝试备选API %d/2...\n", attempt))
        
        # 备选API的handle
        handle <- curl::new_handle()
        curl::handle_setopt(handle, 
                           timeout = api_timeout_settings$timeout,
                           connecttimeout = api_timeout_settings$connecttimeout,
                           httpheader = c("User-Agent" = api_timeout_settings$user_agent),
                           ssl_verifypeer = FALSE)
        
        # 执行备选请求
        response <- curl::curl_fetch_memory(alt_url, handle = handle)
        
        if (response$status_code >= 200 && response$status_code < 300) {
          cat("备选API请求成功, 状态码:", response$status_code, "\n")
          return(list(success = TRUE, data = rawToChar(response$content), source = "alternative"))
        } else {
          cat("备选API请求返回错误状态码:", response$status_code, "\n")
          Sys.sleep(5) # 简单等待
        }
      }, error = function(e) {
        cat("备选API请求出错:", conditionMessage(e), "\n")
        Sys.sleep(5)
      })
    }
  }
  
  # 所有尝试都失败
  cat("所有API请求尝试均失败\n")
  return(list(success = FALSE, data = NA_character_, source = "none"))
}

# 数据库连接管理函数
initialize_db_connection <- function(db_file_path) {
  tryCatch({
    if (!file.exists(db_file_path)) {
      dir.create(dirname(db_file_path), showWarnings = FALSE, recursive = TRUE)
      con <- DBI::dbConnect(RSQLite::SQLite(), db_file_path)
      DBI::dbExecute(con, "CREATE TABLE IF NOT EXISTS api_cache (doi TEXT PRIMARY KEY, api TEXT, response TEXT, timestamp TEXT)")
      return(con)
    } else {
      con <- DBI::dbConnect(RSQLite::SQLite(), db_file_path)
      # 验证连接是否有效
      tryCatch({
        DBI::dbExecute(con, "SELECT 1")
      }, error = function(e) {
        # 如果连接无效，尝试修复数据库
        message("数据库连接验证失败，尝试修复数据库...")
        DBI::dbDisconnect(con, force = TRUE)
        # 创建备份
        backup_file <- paste0(db_file_path, ".backup_", format(Sys.time(), "%Y%m%d%H%M%S"))
        file.copy(db_file_path, backup_file)
        # 重新连接
        con <- DBI::dbConnect(RSQLite::SQLite(), db_file_path)
        DBI::dbExecute(con, "PRAGMA integrity_check")
        DBI::dbExecute(con, "VACUUM")
      })
      return(con)
    }
  }, error = function(e) {
    message(paste("数据库连接初始化失败:", e$message))
    # 如果创建连接失败，尝试使用临时文件
    temp_db <- tempfile(fileext = ".sqlite")
    message(paste("尝试使用临时数据库:", temp_db))
    con <- DBI::dbConnect(RSQLite::SQLite(), temp_db)
    DBI::dbExecute(con, "CREATE TABLE IF NOT EXISTS api_cache (doi TEXT PRIMARY KEY, api TEXT, response TEXT, timestamp TEXT)")
    return(con)
  })
}

ensure_db_connection <- function(connection, db_file_path) {
  # 增强的连接检查和恢复
  is_valid <- FALSE
  
  # 检查连接对象是否存在且有效
  if (!missing(connection) && exists("connection") && !is.null(connection)) {
    tryCatch({
      # 尝试执行简单查询来验证连接
      if (DBI::dbIsValid(connection)) {
        is_valid <- TRUE
        # 进一步验证连接可用性
        tryCatch({
          DBI::dbExecute(connection, "SELECT 1")
        }, error = function(e) {
          is_valid <- FALSE
          message("数据库连接测试失败，需要重新初始化")
        })
      }
    }, error = function(e) {
      is_valid <- FALSE
      message(paste("连接验证异常:", e$message))
    })
  }
  
  if (!is_valid) {
    message("正在重新初始化数据库连接...")
    # 如果之前的连接无效但仍存在，尝试强制关闭
    if (exists("connection") && !is.null(connection)) {
      tryCatch({
        DBI::dbDisconnect(connection, force = TRUE)
      }, error = function(e) {
        # 忽略关闭错误
      })
    }
    # 创建新连接
    return(initialize_db_connection(db_file_path))
  }
  
  return(connection)
}

safe_query_cache <- function(connection, query, params = NULL, db_file_path) {
  max_retries <- 3
  retry_count <- 0
  
  while (retry_count < max_retries) {
    tryCatch({
      # 确保数据库连接有效
      con <- ensure_db_connection(connection, db_file_path)
      
      # 执行查询
      if (is.null(params)) {
        result <- DBI::dbGetQuery(con, query)
      } else {
        stmt <- DBI::dbSendQuery(con, query)
        DBI::dbBind(stmt, params)
        result <- DBI::dbFetch(stmt)
        DBI::dbClearResult(stmt)
      }
      
      return(list(success = TRUE, result = result, connection = con))
    }, error = function(e) {
      retry_count <<- retry_count + 1
      message(paste("数据库查询失败 (尝试", retry_count, "/", max_retries, "):", e$message))
      Sys.sleep(1)  # 短暂延迟后重试
      
      # 最后一次尝试时，确保重新创建连接
      if (retry_count == max_retries - 1) {
        if (exists("con") && !is.null(con)) {
          tryCatch({
            DBI::dbDisconnect(con, force = TRUE)
          }, error = function(e) {})
        }
        connection <- NULL  # 强制重新初始化连接
      }
    })
  }
  
  # 所有重试都失败
  message("数据库操作失败，达到最大重试次数")
  return(list(success = FALSE, result = data.frame(), connection = connection))
}

safe_write_cache <- function(connection, doi, api, response, db_file_path) {
  max_retries <- 3
  retry_count <- 0
  
  while (retry_count < max_retries) {
    tryCatch({
      # 确保数据库连接有效
      con <- ensure_db_connection(connection, db_file_path)
      
      # 准备写入
      query <- "INSERT OR REPLACE INTO api_cache (doi, api, response, timestamp) VALUES (?, ?, ?, ?)"
      timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
      
      # 执行
      stmt <- DBI::dbSendQuery(con, query)
      DBI::dbBind(stmt, list(doi, api, response, timestamp))
      DBI::dbClearResult(stmt)
      
      return(list(success = TRUE, connection = con))
    }, error = function(e) {
      retry_count <<- retry_count + 1
      message(paste("数据库写入失败 (尝试", retry_count, "/", max_retries, "):", e$message))
      Sys.sleep(1)  # 短暂延迟后重试
      
      # 最后一次尝试时，确保重新创建连接
      if (retry_count == max_retries - 1) {
        if (exists("con") && !is.null(con)) {
          tryCatch({
            DBI::dbDisconnect(con, force = TRUE)
          }, error = function(e) {})
        }
        connection <- NULL  # 强制重新初始化连接
      }
    })
  }
  
  # 所有重试都失败
  message("数据库写入失败，达到最大重试次数")
  return(list(success = FALSE, connection = connection))
}

# 在脚本开始时初始化数据库连接
cat("初始化API缓存数据库...\n")
api_cache_file <- file.path(output_location, "api_cache_v5.sqlite")
con <- initialize_db_connection(api_cache_file)

# 修改的OpenAlex数据获取函数
fetch_openalex_data <- function(doi, use_cache = TRUE) {
  # 检查缓存
  if (use_cache) {
    query_result <- safe_query_cache(
      con, 
      "SELECT response FROM api_cache WHERE doi = ? AND api = 'openalex'", 
      params = list(doi),
      api_cache_file
    )
    
    # 更新连接变量
    con <<- query_result$connection
    
    if (query_result$success && nrow(query_result$result) > 0 && !is.na(query_result$result$response[1])) {
      cat("使用OpenAlex API缓存结果:", doi, "\n")
      return(query_result$result$response[1])
    }
  }
  
  # 编码DOI并设置多种可能的URL格式
  doi_encoded <- URLencode(doi, reserved = TRUE)
  url <- paste0("https://api.openalex.org/works/doi:", doi_encoded)
  
  # 使用安全请求函数
  result <- safe_api_request(url, doi, "openalex")
  
  # 处理结果
  if (result$success) {
    response_data <- result$data
    
    # 缓存结果
    if (use_cache) {
      write_result <- safe_write_cache(con, doi, "openalex", response_data, api_cache_file)
      # 更新连接变量
      con <<- write_result$connection
    }
    
    return(response_data)
  } else {
    cat("无法获取OpenAlex数据，返回NA\n")
    return(NA_character_)
  }
}

# 修改的Crossref数据获取函数
fetch_crossref_data <- function(doi, use_cache = TRUE) {
  # 检查缓存
  if (use_cache) {
    query_result <- safe_query_cache(
      con, 
      "SELECT response FROM api_cache WHERE doi = ? AND api = 'crossref'", 
      params = list(doi),
      api_cache_file
    )
    
    # 更新连接变量
    con <<- query_result$connection
    
    if (query_result$success && nrow(query_result$result) > 0 && !is.na(query_result$result$response[1])) {
      cat("使用Crossref API缓存结果:", doi, "\n")
      return(query_result$result$response[1])
    }
  }
  
  # 编码DOI
  doi_encoded <- URLencode(doi, reserved = TRUE)
  url <- paste0("https://api.crossref.org/works/", doi_encoded)
  
  # 使用安全请求函数
  result <- safe_api_request(url, doi, "crossref")
  
  # 处理结果
  if (result$success) {
    response_data <- result$data
    
    # 缓存结果
    if (use_cache) {
      write_result <- safe_write_cache(con, doi, "crossref", response_data, api_cache_file)
      # 更新连接变量
      con <<- write_result$connection
    }
    
    return(response_data)
  } else {
    cat("无法获取Crossref数据，返回NA\n")
    return(NA_character_)
  }
}

# 确保脚本结束时关闭数据库连接
cleanup_db_connection <- function() {
  tryCatch({
    if (exists("con") && !is.null(con)) {
      # 检查连接是否有效
      valid_connection <- FALSE
      tryCatch({
        valid_connection <- DBI::dbIsValid(con)
      }, error = function(e) {
        message("检查连接有效性时出错，假定连接无效")
      })
      
      if (valid_connection) {
        message("正在安全关闭数据库连接...")
        suppressWarnings({
          DBI::dbDisconnect(con)
        })
      } else {
        message("数据库连接已无效，尝试强制关闭")
        suppressWarnings({
          try(DBI::dbDisconnect(con, force = TRUE), silent = TRUE)
        })
      }
    }
  }, error = function(e) {
    message(paste("关闭数据库连接时发生错误:", e$message))
    # 尝试强制关闭
    if (exists("con") && !is.null(con)) {
      suppressWarnings({
        try(DBI::dbDisconnect(con, force = TRUE), silent = TRUE)
      })
    }
  })
  
  # 确保连接变量被清理
  if (exists("con")) {
    con <- NULL
  }
}

# 脚本结束时执行清理
on.exit(cleanup_db_connection(), add = TRUE)

# --- 5. 主题分析 ---
cat("\n--- 阶段 5: 主题分析 ---\n")

cat("5.1 正在对标题进行主题分析...\n")
title_terms <- analyze_text_topics(wos_df, "TI_lower", min_freq = 3)
cat("  从标题中提取了", nrow(title_terms), "个高频关键词。\n")

cat("5.2 正在对摘要进行主题分析...\n")
abstract_terms <- analyze_text_topics(wos_df, "AB", min_freq = 5)
cat("  从摘要中提取了", nrow(abstract_terms), "个高频关键词。\n")

cat("5.3 正在分析作者关键词...\n")
if ("DE" %in% colnames(wos_df)) {
  all_keywords <- wos_df %>% 
    filter(!is.na(DE)) %>% 
    pull(DE) %>% 
    paste(collapse = "; ")
  
  keywords_list <- unlist(strsplit(all_keywords, "; "))
  keywords_list <- trimws(keywords_list)
  
  keyword_freq <- table(keywords_list)
  keyword_freq_df <- as.data.frame(keyword_freq, stringsAsFactors = FALSE)
  colnames(keyword_freq_df) <- c("keyword", "frequency")
  
  keyword_freq_df <- keyword_freq_df %>%
    arrange(desc(frequency)) %>%
    filter(keyword != "") %>%
    mutate(
      rel_frequency = frequency / sum(frequency),
      cumulative_freq = cumsum(rel_frequency)
    )
  
  cat("  分析了", length(keywords_list), "个关键词，", 
      nrow(keyword_freq_df), "个唯一关键词。\n")
  } else {
  cat("  数据集中缺少作者关键词 (DE) 列.\n")
  keyword_freq_df <- tibble(
    keyword = character(0),
    frequency = integer(0),
    rel_frequency = numeric(0),
    cumulative_freq = numeric(0)
  )
}

cat("5.4 正在生成综合词汇表...\n")
combined_terms <- bind_rows(
  title_terms %>% mutate(source = "标题", term = tolower(term)),
  abstract_terms %>% mutate(source = "摘要", term = tolower(term)),
  keyword_freq_df %>% mutate(source = "关键词", term = tolower(keyword)) %>% 
    select(term, frequency, rel_frequency, cumulative_freq, source)
) %>% 
  group_by(term) %>%
  summarize(
    total_frequency = sum(frequency),
    sources = paste(source, collapse = "+"),
    source_count = n_distinct(source),
    .groups = "drop"
  ) %>%
  arrange(desc(total_frequency))

combined_terms <- combined_terms %>%
  mutate(
    importance = case_when(
      source_count == 3 ~ "核心主题 (3源)",
      source_count == 2 ~ "重要主题 (2源)",
      source_count == 1 & total_frequency >= 10 ~ "高频单源",
      TRUE ~ "一般主题"
    )
  )

topic_analysis <- list(
  title_terms = title_terms,
  abstract_terms = abstract_terms,
  keyword_terms = keyword_freq_df,
  combined_terms = combined_terms
)

write_xlsx(topic_analysis, topic_analysis_file)
cat("主题分析结果已保存到:", topic_analysis_file, "\n")

log_summary_list[["主题分析"]] <- tibble(
  步骤 = "主题分析",
  标题词汇数 = nrow(title_terms),
  摘要词汇数 = nrow(abstract_terms),
  关键词数 = nrow(keyword_freq_df),
  综合词汇数 = nrow(combined_terms),
  核心主题数 = sum(combined_terms$importance == "核心主题 (3源)")
)
# --- 6. 数据整合与最终输出 ---
cat("\n--- 阶段 6: 数据整合与最终输出 ---\n")

# 数据库连接修复和验证
tryCatch({
  # 检查连接变量是否存在
  if (!exists("con") || is.null(con) || !DBI::dbIsValid(con)) {
    message("检测到数据库连接无效，尝试重新初始化...")
    # 尝试关闭旧连接，如果存在的话
    if (exists("con") && !is.null(con)) {
      tryCatch({
        DBI::dbDisconnect(con, force = TRUE)
      }, error = function(e) {
        message("关闭旧连接时出错，将继续创建新连接")
      })
    }
    
    # 重新初始化连接
    con <- initialize_db_connection(api_cache_file)
    
    # 测试连接
    tryCatch({
      DBI::dbExecute(con, "SELECT 1")
      message("数据库连接已成功重新初始化")
    }, error = function(e) {
      stop("无法重新建立数据库连接，请检查数据库文件或权限")
    })
  } else {
    # 验证现有连接
    tryCatch({
      DBI::dbExecute(con, "SELECT 1")
      message("现有数据库连接正常")
    }, error = function(e) {
      message("现有连接测试失败，尝试重新连接...")
      tryCatch({
        DBI::dbDisconnect(con, force = TRUE)
      }, error = function(e) {})
      
      con <- initialize_db_connection(api_cache_file)
      message("数据库连接已重置")
    })
  }
}, error = function(e) {
  message(paste("数据库连接检查过程出错:", e$message))
  message("将尝试继续处理，但可能会遇到数据库相关错误")
})

# 添加安全获取API缓存数据的函数
safe_get_cached_api_data <- function(doi_list, api_name, db_file_path) {
  # 参数验证
  if (length(doi_list) == 0) {
    message("警告: DOI列表为空，无法从缓存获取API数据")
    return(list(success = FALSE, data = list(), error = "DOI列表为空"))
  }
  
  # 初始化返回数据
  all_results <- list()
  success_count <- 0
  error_count <- 0
  
  # 确保数据库连接正常
  tryCatch({
    if (!exists("con") || is.null(con) || !DBI::dbIsValid(con)) {
      message("缓存获取前检测到数据库连接无效，重新初始化...")
      # 关闭旧连接（如果存在）
      if (exists("con") && !is.null(con)) {
        tryCatch({
          DBI::dbDisconnect(con, force = TRUE)
        }, error = function(e) {})
      }
      # 重新初始化
      con <<- initialize_db_connection(db_file_path)
    }
    
    # 测试连接
    DBI::dbExecute(con, "SELECT 1")
  }, error = function(e) {
    message(paste("缓存获取前数据库连接测试失败:", e$message))
    message("尝试重置连接...")
    tryCatch({
      if (exists("con") && !is.null(con)) {
        DBI::dbDisconnect(con, force = TRUE)
      }
      con <<- initialize_db_connection(db_file_path)
    }, error = function(e2) {
      message(paste("重置连接失败:", e2$message))
      return(list(success = FALSE, data = list(), error = "数据库连接失败"))
    })
  })
  
  # 分批处理，每批不超过100个DOI
  batch_size <- 100
  doi_batches <- split(doi_list, ceiling(seq_along(doi_list) / batch_size))
  
  for (i in seq_along(doi_batches)) {
    current_batch <- doi_batches[[i]]
    message(paste("处理DOI缓存批次", i, "/", length(doi_batches), 
                 "（", length(current_batch), "个DOI）"))
    
    # 构建查询，使用参数化查询防止SQL注入
    placeholders <- paste(rep("?", length(current_batch)), collapse = ", ")
    query <- paste0("SELECT doi, api, response FROM api_cache WHERE api = ? AND doi IN (", 
                    placeholders, ")")
    
    # 执行查询
    query_result <- NULL
    tryCatch({
      query_result <- safe_query_cache(con, query, c(api_name, current_batch), db_file_path)
      
      # 更新连接引用
      if (query_result$success) {
        con <<- query_result$connection
        
        # 处理结果
        for (j in 1:nrow(query_result$result)) {
          row <- query_result$result[j, ]
          all_results[[row$doi]] <- tryCatch({
            jsonlite::fromJSON(row$response)
          }, error = function(e) {
            error_count <- error_count + 1
            message(paste("解析缓存的JSON数据失败 (DOI:", row$doi, "):", e$message))
            NULL
          })
          
          if (!is.null(all_results[[row$doi]])) {
            success_count <- success_count + 1
          }
        }
      } else {
        error_count <- error_count + length(current_batch)
        message(paste("批次", i, "查询缓存失败"))
      }
    }, error = function(e) {
      error_count <- error_count + length(current_batch)
      message(paste("批次", i, "处理失败:", e$message))
    })
    
    # 给系统一点喘息时间
    if (i < length(doi_batches)) {
      Sys.sleep(0.5)
    }
  }
  
  message(paste("API缓存数据获取完成: 成功", success_count, "条, 失败", error_count, "条"))
  return(list(
    success = success_count > 0,
    data = all_results,
    success_count = success_count,
    error_count = error_count
  ))
}

# 在第6.1阶段的实现中使用新函数
cat("6.1 正在从缓存获取API数据...\n")

# 安全获取OpenAlex API数据
tryCatch({
  # 检查config变量
  use_openalex <- TRUE  # 默认启用
  if (exists("config")) {
    if (is.list(config) && !is.null(config$use_openalex_api)) {
      use_openalex <- isTRUE(config$use_openalex_api)
    } else {
      message("config变量不是预期的列表格式或缺少use_openalex_api设置，使用默认值")
    }
  } else {
    message("未找到config变量，使用默认设置")
  }
  
  if (use_openalex && length(unique(wos_df$DOI)) > 0) {
    # 获取有效DOI
    valid_dois <- unique(na.omit(wos_df$DOI))
    message(paste("正在从缓存获取", length(valid_dois), "个DOI的OpenAlex数据..."))
    
    # 使用新的安全函数
    openalex_cache_result <- safe_get_cached_api_data(valid_dois, "openalex", api_cache_file)
    
    if (openalex_cache_result$success && length(openalex_cache_result$data) > 0) {
      # 将列表转换为数据框，如果失败则返回空tibble
      openalex_data <- tryCatch({
        # 使用 bind_rows 将列表合并为数据框，.id 可以保留原始列表元素的名称（这里是DOI）
        bind_rows(openalex_cache_result$data, .id = "doi_from_cache")
      }, error = function(e) {
        message(paste("将OpenAlex缓存结果转换为数据框时出错:", e$message))
        tibble() # 返回空的 tibble
      })
      # 确保 openalex_data 是一个 tibble
      if(!is_tibble(openalex_data)) openalex_data <- tibble()

      message(paste("成功从缓存获取并转换了", nrow(openalex_data), "条OpenAlex数据"))
    } else {
      message("从缓存获取OpenAlex数据失败或无数据，将使用空的OpenAlex数据")
      openalex_data <- tibble() # 确保初始化为空 tibble
    }
  } else {
    message("跳过OpenAlex数据缓存获取 (未启用API或无DOI)")
    openalex_data <- tibble() # 确保初始化为空 tibble
  }
}, error = function(e) {
  message(paste("获取OpenAlex缓存数据时出错:", e$message))
  message("将使用空的OpenAlex数据继续")
  openalex_data <- tibble() # 确保初始化为空 tibble
})

# 同样处理其他API的缓存数据
# ... existing code ...

cat("6.2 正在合并数据...\n")
# 清理 Crossref 数据
if(exists("crossref_data") && length(crossref_data) > 0) {
  crossref_clean <- crossref_data %>%
    select(
      doi = identifier,
      title_cr = title_api,
      authors_cr = authors_api,
      journal_cr = journal_api,
      year_cr = year_api,
      volume_cr = volume_api,
      issue_cr = issue_api,
      pages_cr = pages_api,
      publisher_cr = publisher_api,
      type_cr = type_api,
      abstract_cr = abstract_api,
      issn_p_cr = issn_p,
      issn_e_cr = issn_e
    ) %>%
    mutate(doi = str_to_lower(doi))
} else {
  cat("警告：crossref_data不存在或为空\n")
  # --- 修改开始 ---
  # 创建包含必要列的空 tibble，确保 join 可以进行
  crossref_clean <- tibble(
    doi = character(),
    title_cr = character(),
    authors_cr = character(),
    journal_cr = character(),
    year_cr = integer(),
    volume_cr = character(),
    issue_cr = character(),
    pages_cr = character(),
    publisher_cr = character(),
    type_cr = character(),
    abstract_cr = character(),
    issn_p_cr = character(),
    issn_e_cr = character()
  )
  # --- 修改结束 ---
}

# 清理 OpenAlex 数据（如果存在）
if (exists("openalex_data") && is.data.frame(openalex_data) && nrow(openalex_data) > 0) {
  # ... select 和 mutate 操作保持不变 ...
      # 例如，你可能需要调整 select 中的 doi 列来源
      openalex_clean <- openalex_data %>%
        select(
          doi = doi_from_cache, # 确认这里的列名是否需要调整
          openalex_id,
          title_oa = title_api,
          authors_oa = authors_api,
          journal_oa = journal_api,
          year_oa = year_api,
          volume_oa = volume_api,
          issue_oa = issue_api,
          pages_oa = pages_api,
          cited_by_count_oa = cited_by_count_api,
          concepts_oa = concepts_api,
          oa_status = oa_status_api,
          institutions_oa = institutions_api,
          ror_ids_oa = ror_id_api,
          country_codes_oa = country_codes_api,
          type_oa = type_api
          # 确保这里的列名与 bind_rows 生成或 API 返回的列名匹配
        ) %>%
        mutate(doi = str_to_lower(doi)) # 确保 doi 列存在且正确
} else {
  cat("警告：openalex_data不存在、不是有效的数据框或为空\n") # 更新警告信息
  openalex_clean <- tibble(
    doi = character(),
    openalex_id = character(),
    title_oa = character(),
    authors_oa = character(),
    journal_oa = character(),
    year_oa = integer(),
    volume_oa = character(),
    issue_oa = character(),
    pages_oa = character(),
    cited_by_count_oa = integer(),
    concepts_oa = character(),
    oa_status = character(),
    institutions_oa = character(),
    ror_ids_oa = character(),
    country_codes_oa = character(),
    type_oa = character()
  )
}

# 合并原始数据与 API 数据
wos_df_final <- wos_df %>%
  left_join(crossref_clean, by = c("doi_cleaned" = "doi")) %>%
  left_join(openalex_clean, by = c("doi_cleaned" = "doi"))

cat("数据合并完成，新维度:", paste(nrow(wos_df_final), "行,", ncol(wos_df_final), "列"), "\n")

# 检查字段冲突
cat("6.3 正在检查字段冲突...\n")

# 冲突分析函数
analyze_title_conflict <- function(values) {
  values <- na.omit(values)
  
  # 判断冲突类型
  if (length(values) < 2) return("数据不足以分析")
  
  # 检查是否是大小写差异
  if (length(unique(tolower(values))) == 1) return("仅大小写差异")
  
  # 检查是否是标点符号差异
  clean_values <- gsub("[[:punct:]]", "", values)
  if (length(unique(clean_values)) == 1) return("仅标点符号差异")
  
  # 计算字符串相似度
  if (length(values) == 2) {
    similarity <- stringdist::stringsim(values[1], values[2], method = "jw")
    if (similarity > 0.9) return("高度相似(>90%)")
    if (similarity > 0.7) return("中度相似(70-90%)")
    if (similarity > 0.5) return("低度相似(50-70%)")
    return("显著差异(<50%相似)")
  }
  
  # 多个值之间的比较
  return("多源数据差异")
}

analyze_author_conflict <- function(values) {
  values <- na.omit(values)
  
  if (length(values) < 2) return("数据不足以分析")
  
  # 检查是否只是作者顺序不同
  authors_sets <- lapply(values, function(x) {
    sort(unlist(strsplit(x, ";")))
  })
  
  if (length(unique(lapply(authors_sets, paste, collapse=";"))) == 1) 
    return("仅作者顺序差异")
  
  # 检查作者数量差异
  author_counts <- sapply(authors_sets, length)
  if (max(author_counts) - min(author_counts) > 2)
    return("作者数量差异显著")
  
  # 检查是否有共同作者
  common_authors <- Reduce(intersect, authors_sets)
  if (length(common_authors) > 0) {
    common_ratio <- length(common_authors) / max(author_counts)
    if (common_ratio > 0.7) return("部分作者差异(>70%相同)")
    if (common_ratio > 0.5) return("部分作者差异(50-70%相同)")
    return("作者列表显著差异(<50%相同)")
  }
  
  return("完全不同的作者列表")
}

analyze_year_conflict <- function(values) {
  values <- as.numeric(na.omit(values))
  
  if (length(values) < 2) return("数据不足以分析")
  
  year_diff <- max(values) - min(values)
  
  if (year_diff == 0) return("可能是格式差异")
  if (year_diff == 1) return("相邻年份差异(可能是在线先发表)")
  if (year_diff > 1 && year_diff <= 5) return("中等年份差异(1-5年)")
  return("显著年份差异(>5年)")
}

analyze_journal_conflict <- function(values) {
  values <- na.omit(values)
  
  if (length(values) < 2) return("数据不足以分析")
  
  # 检查是否是缩写与全名差异
  if (length(values) == 2) {
    # 检查一个是否是另一个的缩写
    shorter <- which.min(nchar(values))
    longer <- setdiff(1:2, shorter)
    
    shorter_parts <- unlist(strsplit(values[shorter], " "))
    # 检查缩写是否都是长名称的开头字母
    matches <- sapply(shorter_parts, function(p) {
      grepl(paste0("\\b", substr(p, 1, 1)), values[longer], ignore.case = TRUE)
    })
    if (all(matches)) return("可能是期刊缩写与全名")
  }
  
  # 计算相似度
  if (length(values) == 2) {
    similarity <- stringdist::stringsim(values[1], values[2], method = "jw")
    if (similarity > 0.9) return("高度相似(>90%)")
    if (similarity > 0.7) return("中度相似(可能是变体名称)")
    if (similarity > 0.5) return("低度相似(50-70%)")
    return("完全不同的期刊")
  }
  
  return("多源数据期刊名称差异")
}

conflict_fields <- list(
  title = c("TI", "title_cr", "title_oa"),
  authors = c("AU", "authors_cr", "authors_oa"),
  journal = c("SO", "journal_cr", "journal_oa"),
  year = c("PY_numeric", "year_cr", "year_oa"),
  volume = c("VL", "volume_cr", "volume_oa"),
  issue = c("IS", "issue_cr", "issue_oa"),
  pages = c("BP", "pages_cr", "pages_oa")  # 注意：BP 仅代表起始页，需后续处理
)

field_conflicts <- list()
conflict_reasons <- list()

for (field_group in names(conflict_fields)) {
  cols <- conflict_fields[[field_group]]
  cols_present <- cols[cols %in% colnames(wos_df_final)]

  if (length(cols_present) > 1) {
    conflict_check <- wos_df_final %>%
      select(SR, doi_cleaned, all_of(cols_present)) %>%
      mutate(across(all_of(cols_present), ~ str_to_lower(str_squish(as.character(.))))) %>%
      rowwise() %>%
      mutate(
        n_unique = length(unique(na.omit(c_across(all_of(cols_present))))),
        is_conflict = n_unique > 1
      ) %>%
      ungroup() %>%
      filter(is_conflict)

    if (nrow(conflict_check) > 0) {
      cat("  发现", field_group, "字段冲突，涉及", nrow(conflict_check), "条记录。\n")
      
      # 创建冲突原因分析
      conflict_reasons_df <- conflict_check %>%
        rowwise() %>%
        mutate(
          conflict_values = paste(na.omit(c_across(all_of(cols_present))), collapse = " | "),
          conflict_source = case_when(
            field_group == "title" ~ analyze_title_conflict(c_across(all_of(cols_present))),
            field_group == "authors" ~ analyze_author_conflict(c_across(all_of(cols_present))),
            field_group == "year" ~ analyze_year_conflict(c_across(all_of(cols_present))),
            field_group == "journal" ~ analyze_journal_conflict(c_across(all_of(cols_present))),
            TRUE ~ "数据源间格式差异"
          )
        ) %>%
        ungroup() %>%
        select(SR, doi_cleaned, conflict_values, conflict_source)
      
      field_conflicts[[field_group]] <- conflict_check
      conflict_reasons[[field_group]] <- conflict_reasons_df
      
      # 显示冲突原因分布
      reason_counts <- conflict_reasons_df %>%
        count(conflict_source) %>%
        mutate(percentage = sprintf("%.1f%%", 100 * n / sum(n)))
      
      cat("  冲突原因分析:\n")
      print(reason_counts)
    }
  }
}

if (length(field_conflicts) > 0) {
  # 合并冲突检查和冲突原因
  conflicts_combined <- bind_rows(field_conflicts, .id = "field_group")
  conflict_reasons_combined <- bind_rows(conflict_reasons, .id = "field_group")
  
  # 创建一个包含详细冲突信息和原因的工作表
  conflicts_with_reasons <- conflicts_combined %>%
    left_join(conflict_reasons_combined, by = c("field_group", "SR", "doi_cleaned"))
  
  # 保存到Excel文件的不同工作表
  conflict_sheets <- list(
    "所有冲突" = conflicts_with_reasons,
    "冲突原因分析" = conflict_reasons_combined %>% 
      group_by(field_group, conflict_source) %>% 
      summarise(数量 = n(), .groups = "drop") %>%
      arrange(field_group, desc(数量))
  )
  
  # 为每种冲突类型添加单独的工作表
  for (field_group in names(field_conflicts)) {
    conflict_sheets[[paste0(field_group, "_冲突")]] <- field_conflicts[[field_group]]
  }
  
  write_xlsx(conflict_sheets, field_conflicts_file)
  cat("字段冲突已保存至:", field_conflicts_file, "\n")

  log_summary_list[["字段冲突"]] <- tibble(
    步骤 = "字段冲突检查",
    冲突字段组 = names(field_conflicts),
    冲突记录数 = map_int(field_conflicts, nrow),
    主要冲突原因 = sapply(names(field_conflicts), function(fg) {
      top_reason <- conflict_reasons[[fg]] %>%
        count(conflict_source, sort = TRUE) %>%
        slice(1) %>%
        pull(conflict_source)
      return(paste0(top_reason, " (", 
                   conflict_reasons[[fg]] %>% 
                     filter(conflict_source == top_reason) %>% 
                     nrow(), "条)"))
    }),
    说明 = "详情见 field_conflicts_v5.xlsx"
  )
} else {
  cat("未发现字段冲突。\n")
  log_summary_list[["字段冲突"]] <- tibble(
    步骤 = "字段冲突检查",
    结果 = "未发现冲突"
  )
}

# 优先级合并字段（优先使用原始数据，若缺失则用 API 数据）
cat("6.4 正在执行字段优先级合并...\n")
wos_df_final <- wos_df_final %>%
  mutate(
    title_final = coalesce(TI, title_cr, title_oa),
    authors_final = coalesce(AU, authors_cr, authors_oa),
    journal_final = coalesce(SO, journal_cr, journal_oa),
    year_final = coalesce(PY_numeric, year_cr, year_oa),
    volume_final = coalesce(VL, volume_cr, volume_oa),
    issue_final = coalesce(IS, issue_cr, issue_oa),
    pages_final = case_when(
      !is.na(BP) & !is.na(EP) ~ paste0(BP, "-", EP),
      !is.na(BP) ~ as.character(BP),
      TRUE ~ coalesce(pages_cr, pages_oa)
    )
  )

cat("字段优先级合并完成。\n")

# 初始化最终数据集列
final_columns <- c(
  "SR", "UT", "doi_cleaned", "title_final", "authors_final", "journal_final",
  "year_final", "volume_final", "issue_final", "pages_final", "TC", "CR",
  "AB", "DE", "ID", "institutions_count", "primary_institution",
  "primary_country", "primary_country_code", "cited_by_count_oa",
  "openalex_id", "type_cr", "type_oa", "publisher_cr", "issn_p_cr", "issn_e_cr", 
  "author_identifiers_map"
)

# --- 添加机构和国家字段到最终输出字段 ---
final_columns <- c(final_columns, "Primary_Institution", "Primary_Country")
cat("添加机构和国家字段到最终输出\n")

# --- 6.6 创建最终数据集 ---
cat("\n--- 阶段 6.6: 创建最终数据集 ---\n")

# 生成最终数据集
cat("6.5 正在生成最终数据集...\n")

missing_cols <- setdiff(final_columns, colnames(wos_df_final))
if (length(missing_cols) > 0) {
  cat("警告：以下列缺失，将填充为 NA:", paste(missing_cols, collapse=", "), "\n")
  wos_df_final[missing_cols] <- NA
}

# 保留所有原始bibliometrix字段
bibliometrix_cols <- colnames(wos_df)
cat("保留所有bibliometrix原始字段，共", length(bibliometrix_cols), "个字段\n")

# 确保作者标识符和机构信息被保留
api_enriched_cols <- c(
  "doi_cleaned", "title_final", "authors_final", "journal_final", "year_final", 
  "volume_final", "issue_final", "pages_final", "UT_cleaned",
  "institutions_count", "primary_institution", "primary_country", "primary_country_code",
  "cited_by_count_oa", "openalex_id", "institutions_oa", "ror_ids_oa", "country_codes_oa",
  "type_cr", "type_oa", "publisher_cr", "issn_p_cr", "issn_e_cr", "author_identifiers_map"
)

# 构建最终输出数据集
wos_df_final_output <- wos_df_final %>%
  # 先选择所有bibliometrix原始字段
  select(any_of(bibliometrix_cols)) %>%
  # 添加API丰富的字段
  bind_cols(
    wos_df_final %>% 
      select(any_of(api_enriched_cols)) %>%
      # 移除已存在的列，避免重复
      select(-any_of(intersect(bibliometrix_cols, api_enriched_cols)))
  )

# 重命名主要列，但保留原始字段名称
wos_df_final_output <- wos_df_final_output %>%
  rename(
    DOI = doi_cleaned,
    WOS_ID = UT_cleaned,
    API_Title = title_final,
    API_Authors = authors_final,
    API_Journal = journal_final,
    API_Year = year_final,
    API_Volume = volume_final,
    API_Issue = issue_final,
    API_Pages = pages_final,
    Institutions_Count = institutions_count,
    Primary_Institution = primary_institution,
    Primary_Country = primary_country,
    Primary_Country_Code = primary_country_code,
    Cited_By_Count_OpenAlex = cited_by_count_oa,
    OpenAlex_ID = openalex_id,
    Type_Crossref = type_cr,
    Type_OpenAlex = type_oa,
    Publisher = publisher_cr,
    ISSN_Print = issn_p_cr,
    ISSN_Electronic = issn_e_cr,
    Author_Identifiers = author_identifiers_map
  )

# 统计最终字段数
cat("最终数据集包含", ncol(wos_df_final_output), "个字段\n")

# 计算所有字段的覆盖率统计
cat("6.5.1 正在统计字段覆盖率...\n")

# 创建字段中文释义映射表
field_chinese_desc <- list(
  "DOI" = "数字对象标识符",
  "WOS_ID" = "Web of Science 标识号",
  "TI" = "标题",
  "AU" = "作者",
  "PY" = "出版年份",
  "SO" = "来源出版物",
  "AB" = "摘要",
  "DE" = "作者关键词",
  "ID" = "关键词增强版",
  "TC" = "WOS被引次数",
  "CR" = "引用参考文献",
  "API_Title" = "API补充标题",
  "API_Authors" = "API补充作者",
  "API_Journal" = "API补充期刊",
  "API_Year" = "API补充年份",
  "API_Volume" = "API补充卷号",
  "API_Issue" = "API补充期号",
  "API_Pages" = "API补充页码",
  "Primary_Institution" = "第一作者机构",
  "Primary_Country" = "第一作者国家",
  "Primary_Country_Code" = "第一作者国家代码",
  "Cited_By_Count_OpenAlex" = "OpenAlex引用数",
  "OpenAlex_ID" = "OpenAlex标识号",
  "Type_Crossref" = "Crossref文献类型",
  "Type_OpenAlex" = "OpenAlex文献类型",
  "Publisher" = "出版商",
  "ISSN_Print" = "印刷版ISSN",
  "ISSN_Electronic" = "电子版ISSN",
  "Author_Identifiers" = "作者标识符",
  "Institutions_Count" = "机构数量",
  "Institutions_OpenAlex" = "OpenAlex机构信息",
  "ROR_IDs" = "ROR机构标识符",
  "Country_Codes" = "国家代码",
  "UT" = "WOS唯一标识符",
  "UT_cleaned" = "清洗后WOS标识符",
  "doi_cleaned" = "清洗后DOI",
  "title_final" = "最终标题",
  "authors_final" = "最终作者列表",
  "journal_final" = "最终期刊",
  "year_final" = "最终年份",
  "volume_final" = "最终卷号",
  "issue_final" = "最终期号",
  "pages_final" = "最终页码",
  "SR" = "WOS记录标识符",
  "J9" = "期刊缩写",
  "VL" = "卷号",
  "IS" = "期号",
  "BP" = "起始页",
  "EP" = "结束页",
  "DI" = "DOI原始字段",
  "WC" = "Web of Science类别",
  "SC" = "学科类别",
  "LA" = "语言",
  "DT" = "文档类型",
  "C1" = "作者地址",
  "RP" = "通讯作者地址",
  "EM" = "电子邮件地址",
  "FU" = "资助信息",
  "NR" = "参考文献数量",
  "Z9" = "总被引次数",
  "U1" = "使用次数",
  "U2" = "使用次数180天",
  "PU" = "出版商",
  "PI" = "出版商城市",
  "PA" = "出版商地址",
  "BN" = "ISBN",
  "AF" = "作者全名",
  "CA" = "团体作者",
  "GP" = "书籍组",
  "BE" = "编辑",
  "SN" = "ISSN",
  "EI" = "eISSN",
  "PD" = "出版日期",
  "PY_numeric" = "出版年份(数值)",
  "OI" = "ORCID标识符",
  "RI" = "ResearcherID",
  "Primary_Institution" = "主要机构",
  "Primary_Country" = "主要国家",
  "Primary_Country_Code" = "主要国家代码",
  "Raw_Country" = "原始国家名称",
  "Std_Institution" = "标准化机构名称",
  "institutions_oa" = "OpenAlex机构信息",
  "ror_ids_oa" = "ROR机构标识符",
  "country_codes_oa" = "OpenAlex国家代码",
  "concepts_oa" = "OpenAlex概念分类",
  "oa_status" = "开放获取状态",
  "author_identifiers_map" = "作者标识符映射",
  "openalex_id" = "OpenAlex文献ID"
)

# 添加常见列的中文释义
add_chinese_desc <- function(field_name) {
  if (field_name %in% names(field_chinese_desc)) {
    return(field_chinese_desc[[field_name]])
  } else {
    # 尝试通过模式匹配
    for (key in names(field_chinese_desc)) {
      if (grepl(key, field_name, fixed = TRUE)) {
        return(paste0(field_chinese_desc[[key]], "(变体)"))
      }
    }
    return("其他字段")
  }
}

# 计算所有字段的覆盖率
field_coverage <- wos_df_final_output %>%
  summarise(across(everything(), ~ sum(!is.na(.))/n())) %>%
  pivot_longer(cols = everything(),
               names_to = "字段名",
               values_to = "覆盖率") %>%
  mutate(
    中文释义 = sapply(字段名, add_chinese_desc),
    覆盖率百分比 = sprintf("%.2f%%", 覆盖率 * 100),
    缺失率 = 1 - 覆盖率,
    缺失率百分比 = sprintf("%.2f%%", 缺失率 * 100)
  ) %>%
  arrange(desc(缺失率))  # 按缺失率从高到低排序

# 保存字段覆盖率统计
field_coverage_file <- file.path(output_location, "field_coverage_statistics_v5.xlsx")
write_xlsx(field_coverage, field_coverage_file)
cat("字段覆盖率统计已保存至:", field_coverage_file, "\n")

# 输出主要字段的覆盖情况
cat("\n主要字段覆盖率统计 (按缺失率排序):\n")
main_fields <- c("DOI", "WOS_ID", "TI", "AU", "PY", "SO", "AB", "DE", "TC",
                "API_Title", "API_Authors", "API_Journal", "Primary_Institution",
                "Primary_Country", "Author_Identifiers", "OpenAlex_ID")
main_fields_coverage <- field_coverage %>%
  filter(字段名 %in% main_fields) %>%
  # 先排序，再选择列
  arrange(desc(缺失率)) %>%  # 从高到低排序，相当于按缺失率从高到低
  select(字段名, 中文释义, 覆盖率百分比, 缺失率百分比)
print(main_fields_coverage, n = nrow(main_fields_coverage))

# 将覆盖率统计添加到日志
log_summary_list[["字段覆盖率"]] <- field_coverage %>%
  # 按缺失率从高到低排序后取前20行
  arrange(desc(缺失率)) %>%
  slice_head(n = 20) %>%  
  select(字段名, 中文释义, 覆盖率百分比, 缺失率百分比) %>%
  mutate(步骤 = "字段覆盖率统计-缺失最多的字段")

# 保存最终数据
write_xlsx(wos_df_final_output, final_data_file)
cat("最终数据集已保存至:", final_data_file, "\n")

# 注释掉生成CiteSpace格式的代码
# cat("6.6 正在生成CiteSpace格式输出...\n")
# citespace_output_file <- file.path(output_location, "citespace_format_v5.txt")
# citespace_data <- generate_citespace_format(wos_df_final_output, citespace_output_file)
# cat("CiteSpace格式文件已保存至:", citespace_output_file, "\n")

# 关闭数据库连接
cat("6.7 正在关闭数据库连接...\n")
# 添加错误处理，确保安全关闭数据库连接
tryCatch({
  if (exists("con") && DBI::dbIsValid(con)) {
    dbDisconnect(con)
    cat("数据库连接已关闭。\n")
  } else {
    cat("数据库连接不存在或已经关闭。\n")
  }
}, error = function(e) {
  cat("关闭数据库连接时发生错误:", conditionMessage(e), "\n")
})

# --- 7. 日志总结与结束 ---
cat("\n--- 阶段 7: 日志总结与结束 ---\n")
end_time <- Sys.time()
total_time <- round(difftime(end_time, start_time, units = "mins"), 2)
cat("总运行时间:", total_time, "分钟\n")

log_summary_list <- lapply(log_summary_list, function(df) {
  # 检查"结果"列是否存在
  if ("结果" %in% names(df)) {
    df$结果 <- as.character(df$结果)
  }
  return(df)
})

log_summary_df <- bind_rows(log_summary_list)
write_xlsx(log_summary_df, log_file_excel)
cat("日志摘要已保存至:", log_file_excel, "\n")

cat("正在结束日志记录...\n")
sink()
cat("日志记录已结束，详情请查看:", log_file_txt, "\n")

cat("\n=== 数据处理流程完成 ===\n")
cat("所有输出文件均已保存至:", output_location, "\n")

# --- 作者标识符提取与匹配函数 ---
extract_author_identifiers <- function(author_string, references_string = NULL, openalex_authors = NULL, crossref_authors = NULL) {
  if (is.null(author_string) || author_string == "") return(list())

  # 分割作者
  authors <- unlist(strsplit(author_string, ";"))
  authors <- trimws(authors)

  # 初始化结果列表
  author_identifiers <- list()

  # 从原始数据中提取标识符
  for (author in authors) {
    # 提取ORCID
    orcid_pattern <- "\\d{4}-\\d{4}-\\d{4}-\\d{3}[0-9X]"
    orcid <- str_extract(author, orcid_pattern)

    # 提取ResearcherID
    rid_pattern <- "R-\\d{4}-\\d{4}"
    researcher_id <- str_extract(author, rid_pattern)

    # 提取Scopus ID
    scopus_pattern <- "\\d{10}"
    scopus_id <- str_extract(author, scopus_pattern)

    # 提取机构ID
    institution_pattern <- "\\[.*?\\]"
    institution_id <- str_extract(author, institution_pattern)
    if (!is.na(institution_id)) {
      institution_id <- gsub("\\[|\\]", "", institution_id)
    }

    # 清理作者名
    clean_name <- gsub(paste0(orcid_pattern, "|", rid_pattern, "|", scopus_pattern, "|", institution_pattern), "", author)
    clean_name <- trimws(clean_name)

    # 处理姓名全称缩写对
    author_full_name <- NA_character_
    # 尝试从姓名中提取缩写和全名
    if (grepl(" ", clean_name)) {
      name_parts <- strsplit(clean_name, " ")[[1]]
      if (length(name_parts) == 2) {
        last_name <- name_parts[1]
        first_name <- name_parts[2]
        # 生成缩写
        author_abbr <- paste0(last_name, " ", substr(first_name, 1, 1))
      } else {
        author_abbr <- clean_name
      }
    } else {
      author_abbr <- clean_name
    }

    author_identifiers[[clean_name]] <- list(
      name = clean_name,
      name_abbr = author_abbr,
      orcid = orcid,
      researcher_id = researcher_id,
      scopus_id = scopus_id,
      institution_id = institution_id,
      full_name = author_full_name
    )
  }

  # 从OpenAlex获取作者标识符
  if (!is.null(openalex_authors) && !is.na(openalex_authors) && openalex_authors != "") {
    # 解析OpenAlex作者信息
    oa_authors <- try({
      jsonlite::fromJSON(openalex_authors)
    }, silent = TRUE)
    
    if (!inherits(oa_authors, "try-error") && is.list(oa_authors)) {
      # 从OpenAlex获取作者标识符
      for (i in seq_along(oa_authors)) {
        if (is.list(oa_authors[[i]])) {
          oa_author <- oa_authors[[i]]
          if (is.null(oa_author$display_name) || is.null(oa_author$orcid)) next
          
          oa_name <- oa_author$display_name
          oa_orcid <- oa_author$orcid
          
          # 尝试匹配到现有作者
          best_match <- NULL
          best_score <- 0
          
          for (name in names(author_identifiers)) {
            score <- stringdist::stringsim(tolower(name), tolower(oa_name), method = "jw")
            if (score > 0.8 && score > best_score) {
              best_match <- name
              best_score <- score
            }
          }
          
          if (!is.null(best_match)) {
            # 更新作者信息
            if (is.na(author_identifiers[[best_match]]$orcid) && !is.null(oa_orcid) && oa_orcid != "") {
              author_identifiers[[best_match]]$orcid <- oa_orcid
            }
            # 如果有全名，但原始数据没有，则添加
            if (is.na(author_identifiers[[best_match]]$full_name) && oa_name != "") {
              author_identifiers[[best_match]]$full_name <- oa_name
            }
          }
        }
      }
    }
  }
  
  # 从Crossref获取作者标识符
  if (!is.null(crossref_authors) && !is.na(crossref_authors) && crossref_authors != "") {
    # 解析Crossref作者信息
    cr_authors <- try({
      jsonlite::fromJSON(crossref_authors)
    }, silent = TRUE)
    
    if (!inherits(cr_authors, "try-error") && is.list(cr_authors)) {
      # 从Crossref获取作者标识符
      for (i in seq_along(cr_authors)) {
        if (is.list(cr_authors[[i]])) {
          cr_author <- cr_authors[[i]]
          cr_name <- paste(cr_author$given, cr_author$family)
          cr_orcid <- cr_author$ORCID
          
          # 尝试匹配到现有作者
          best_match <- NULL
          best_score <- 0
          
          for (name in names(author_identifiers)) {
            score <- stringdist::stringsim(tolower(name), tolower(cr_name), method = "jw")
            if (score > 0.8 && score > best_score) {
              best_match <- name
              best_score <- score
            }
          }
          
          if (!is.null(best_match)) {
            # 更新作者信息
            if (is.na(author_identifiers[[best_match]]$orcid) && !is.null(cr_orcid) && cr_orcid != "") {
              author_identifiers[[best_match]]$orcid <- cr_orcid
            }
            # 如果有全名，但原始数据没有，则添加
            if (is.na(author_identifiers[[best_match]]$full_name) && cr_name != "") {
              author_identifiers[[best_match]]$full_name <- cr_name
            }
          }
        }
      }
    }
  }

  # 如果提供了参考文献，也从中提取作者标识符
  if (!is.null(references_string) && !is.na(references_string) && references_string != "") {
    ref_authors <- unlist(strsplit(references_string, ";"))
    ref_authors <- trimws(ref_authors)

    for (ref_author in ref_authors) {
      # 提取ORCID
      orcid <- str_extract(ref_author, orcid_pattern)
      if (!is.na(orcid)) {
        # 查找匹配的作者名
        for (name in names(author_identifiers)) {
          if (grepl(name, ref_author, ignore.case = TRUE)) {
            if (is.na(author_identifiers[[name]]$orcid)) {
              author_identifiers[[name]]$orcid <- orcid
            }
          }
        }
      }
    }
  }

  return(author_identifiers)
}

# 生成作者标识符映射表
generate_author_identifiers_map <- function(data) {
  all_authors <- list()

  # 处理每篇文章的作者
  for (i in 1:nrow(data)) {
    # 从原始数据和API数据中提取作者信息
    authors <- extract_author_identifiers(
      data$AU[i],
      data$CR[i],
      if("authors_oa" %in% colnames(data)) data$authors_oa[i] else NULL,
      if("authors_cr" %in% colnames(data)) data$authors_cr[i] else NULL
    )

    for (author_name in names(authors)) {
      if (!author_name %in% names(all_authors)) {
        all_authors[[author_name]] <- authors[[author_name]]
      } else {
        # 合并标识符
        existing <- all_authors[[author_name]]
        new <- authors[[author_name]]

        if (is.na(existing$orcid) && !is.na(new$orcid)) {
          existing$orcid <- new$orcid
        }
        if (is.na(existing$researcher_id) && !is.na(new$researcher_id)) {
          existing$researcher_id <- new$researcher_id
        }
        if (is.na(existing$scopus_id) && !is.na(new$scopus_id)) {
          existing$scopus_id <- new$scopus_id
        }
        if (is.na(existing$institution_id) && !is.na(new$institution_id)) {
          existing$institution_id <- new$institution_id
        }
        if (is.na(existing$full_name) && !is.na(new$full_name)) {
          existing$full_name <- new$full_name
        }

        all_authors[[author_name]] <- existing
      }
    }
  }

  # 转换为数据框
  author_map_df <- bind_rows(all_authors, .id = "author_name") %>%
    arrange(author_name)

  return(author_map_df)
}

# 保存作者标识符映射
save_author_identifiers_map <- function(author_map_df, output_file) {
  # 重命名为更清晰的列名
  author_map_renamed <- author_map_df %>%
    rename(
      作者姓名 = author_name,
      作者姓名缩写 = name_abbr,
      作者全名 = full_name,
      ORCID = orcid,
      ResearcherID = researcher_id,
      ScopusID = scopus_id,
      机构ID = institution_id
    )
  
  write_xlsx(author_map_renamed, output_file)
  cat("作者标识符映射已保存至:", output_file, "\n")

  # 统计标识符覆盖率
  coverage <- tibble(
    标识符类型 = c("ORCID", "ResearcherID", "Scopus ID", "机构ID", "全名"),
    总数 = nrow(author_map_df),
    有标识符 = c(
      sum(!is.na(author_map_df$orcid)),
      sum(!is.na(author_map_df$researcher_id)),
      sum(!is.na(author_map_df$scopus_id)),
      sum(!is.na(author_map_df$institution_id)),
      sum(!is.na(author_map_df$full_name))
    )
  ) %>%
    mutate(
      覆盖率 = sprintf("%.1f%%", 有标识符 / 总数 * 100)
    )

  cat("\n作者标识符覆盖率统计:\n")
  print(coverage)

  return(coverage)
}

# --- 在数据处理流程中添加作者标识符处理 ---
cat("\n--- 处理作者标识符 ---\n")

# 生成作者标识符映射 - 需要先确保API数据已合并到wos_df中
cat("正在从原始数据和API数据中提取作者标识符...\n")
author_map_df <- generate_author_identifiers_map(wos_df_final)

# 保存映射结果
author_map_coverage <- save_author_identifiers_map(
  author_map_df,
  author_mapping_file
)

# 将覆盖率统计添加到日志
log_summary_list[["作者标识符"]] <- author_map_coverage %>%
  mutate(步骤 = "作者标识符映射") %>%
  select(步骤, 标识符类型, 总数, 有标识符, 覆盖率)

# 将作者标识符信息添加到最终数据集
wos_df_final <- wos_df_final %>%
  mutate(
    author_identifiers_map = map_chr(AU, function(authors) {
      if (is.na(authors)) return(NA_character_)
      author_list <- unlist(strsplit(authors, ";"))
      author_list <- trimws(author_list)

      identifiers <- map_chr(author_list, function(author) {
        if (author %in% author_map_df$author_name) {
          info <- author_map_df[author_map_df$author_name == author,]
          id_strings <- c()

          if (!is.na(info$orcid)) {
            id_strings <- c(id_strings, paste0("ORCID:", info$orcid))
          }
          if (!is.na(info$researcher_id)) {
            id_strings <- c(id_strings, paste0("ResearcherID:", info$researcher_id))
          }
          if (!is.na(info$scopus_id)) {
            id_strings <- c(id_strings, paste0("ScopusID:", info$scopus_id))
          }
          if (!is.na(info$institution_id)) {
            id_strings <- c(id_strings, paste0("InstitutionID:", info$institution_id))
          }
          
          # 添加全名信息
          author_display <- author
          if (!is.na(info$full_name) && info$full_name != author) {
            author_display <- paste0(author, " (", info$full_name, ")")
          }

          if (length(id_strings) > 0) {
            return(paste0(author_display, " [", paste(id_strings, collapse=", "), "]"))
          }
          return(author_display)
        }
        return(author)
      })

      paste(identifiers, collapse="; ")
    })
  )

# 更新最终数据集的列名
final_columns <- c(final_columns, "author_identifiers_map")

# --- 5. 机构和国家名称标准化 ---
cat("\n--- 阶段 5: 机构和国家名称标准化 ---\n")

# --- 5.1 机构名称标准化 ---
cat("5.1 正在进行机构名称标准化...\n")

# 检查数据框结构
cat("数据集列名:\n")
print(head(colnames(wos_df)))
cat("...等共", length(colnames(wos_df)), "列\n")

# 确定机构列名 - 可能是C1、AF_1、机构列等
check_affiliation_columns <- function(data) {
  potential_aff_cols <- c("C1", "AF_1", "机构", "单位", "机构列", "AFFILIATION", "ADDRESS")
  found_cols <- intersect(potential_aff_cols, colnames(data))
  
  if (length(found_cols) > 0) {
    cat("发现可能的机构信息列:", paste(found_cols, collapse=", "), "\n")
    return(found_cols[1])  # 返回第一个找到的列
  } else {
    # 尝试查找列名中包含"机构"或"单位"的列
    pattern_cols <- colnames(data)[grepl("机构|单位|aff|address", tolower(colnames(data)))]
    if (length(pattern_cols) > 0) {
      cat("通过模式匹配发现可能的机构信息列:", paste(pattern_cols, collapse=", "), "\n")
      return(pattern_cols[1])
    } else {
      cat("警告: 未找到任何机构信息列\n")
      return(NULL)
    }
  }
}

# 机构标准化函数
standardize_institutions <- function(data) {
  # 从合适的列解析机构信息
  aff_col <- check_affiliation_columns(data)
  
  if (is.null(aff_col)) {
    cat("警告: 未找到机构信息列，跳过机构标准化\n")
    return(data)
  }
  
  cat("正在使用列 '", aff_col, "' 解析和标准化机构名称...\n")
  
  # 初始化机构映射表存储
  institution_mapping <- list()
  
  # 提取所有机构名称
  all_institutions <- data %>%
    filter(!is.na(!!sym(aff_col)) & !!sym(aff_col) != "") %>%
    pull(!!sym(aff_col)) %>%
    paste(collapse = "; ") %>%
    str_split(pattern = ";") %>%
    unlist() %>%
    str_split(pattern = ",") %>%
    lapply(function(x) x[1]) %>%
    unlist() %>%
    trimws() %>%
    unique()
  
  cat("共提取", length(all_institutions), "个独特机构名称\n")
  
  # 创建机构标准化规则
  # 这里可以应用Ringgold或ISNI标准名称映射
  institution_std_rules <- c(
    # 大学标准化规则
    "Univ" = "University",
    "Univ\\." = "University",
    "U \\w+" = "University",
    "U\\. \\w+" = "University",
    
    # 研究院/所标准化
    "Inst\\." = "Institute",
    "Inst " = "Institute ",
    "Res\\." = "Research",
    "Res " = "Research ",
    
    # 医院标准化
    "Hosp\\." = "Hospital",
    "Hosp " = "Hospital ",
    
    # 中国机构标准化
    "Peking Univ" = "Peking University",
    "Tsinghua Univ" = "Tsinghua University",
    "Zhejiang Univ" = "Zhejiang University",
    "Shanghai Jiao Tong Univ" = "Shanghai Jiao Tong University",
    "Fudan Univ" = "Fudan University",
    "Beijing Normal Univ" = "Beijing Normal University",
    "Chinese Acad Sci" = "Chinese Academy of Sciences",
    "Chinese Acad\\. Sci\\." = "Chinese Academy of Sciences",
    
    # 美国机构标准化
    "Harvard Univ" = "Harvard University",
    "MIT" = "Massachusetts Institute of Technology",
    "Stanford Univ" = "Stanford University",
    "Univ California" = "University of California",
    "Caltech" = "California Institute of Technology",
    "Princeton Univ" = "Princeton University",
    "Yale Univ" = "Yale University",
    
    # 欧洲机构标准化
    "Univ Oxford" = "University of Oxford",
    "Univ Cambridge" = "University of Cambridge",
    "ETH Zurich" = "ETH Zürich"
  )
  
  # 应用标准化规则
  cat("正在应用", length(institution_std_rules), "条机构标准化规则...\n")
  
  # 创建一个新的机构标准化列
  data <- data %>%
    mutate(
      Std_Institution = !!sym(aff_col),
      Primary_Institution = NA_character_
    )
  
  # 提取主要机构 (第一作者机构)
  data <- data %>%
    mutate(
      Primary_Institution = map_chr(!!sym(aff_col), function(c1) {
        if (is.na(c1) || c1 == "") return(NA_character_)
        
        institutions <- str_split(c1, ";")[[1]]
        if (length(institutions) == 0) return(NA_character_)
        
        primary_inst <- trimws(institutions[1])
        # 应用标准化规则
        for (pattern in names(institution_std_rules)) {
          primary_inst <- str_replace_all(primary_inst, pattern, institution_std_rules[pattern])
        }
        return(primary_inst)
      })
    )
  
  cat("已完成机构名称标准化，已添加标准化机构列\n")
  
  # 创建主要国家提取列，稍后由国家标准化模块使用
  data <- data %>%
    mutate(
      Raw_Country = map_chr(!!sym(aff_col), function(c1) {
        if (is.na(c1) || c1 == "") return(NA_character_)
        
        parts <- str_split(c1, ",")[[1]]
        if (length(parts) < 2) return(NA_character_)
        
        # 通常国家名在地址的最后部分
        country_part <- trimws(parts[length(parts)])
        return(country_part)
      })
    )
  
  return(data)
}

# 应用机构标准化
wos_df <- standardize_institutions(wos_df)

# --- 5.2 国家名称标准化 ---
cat("\n5.2 正在进行国家名称标准化...\n")

# 定义常见国家列表
common_countries <- c(
  "USA", "JAPAN", "CHINA", "ITALY", "CANADA", "UNITED KINGDOM", "GERMANY", 
  "BRAZIL", "AUSTRALIA", "DENMARK", "NETHERLANDS", "SWEDEN", "FRANCE", 
  "INDIA", "TURKEY", "SOUTH KOREA", "SPAIN", "SWITZERLAND", "ARGENTINA", 
  "BELGIUM", "POLAND", "RUSSIA", "GREECE", "CHILE", "SOUTH AFRICA", 
  "ISRAEL", "SAUDI ARABIA", "AUSTRIA", "FINLAND", "NEW ZEALAND", "IRAN", 
  "EGYPT", "ROMANIA", "THAILAND", "CZECH REPUBLIC", "CROATIA", "SINGAPORE", 
  "GRENADA", "SLOVAKIA", "MEXICO", "NORWAY", "PORTUGAL", "SERBIA", 
  "INDONESIA", "BULGARIA", "COLOMBIA", "MALAYSIA", "UKRAINE", "ECUADOR", 
  "UNITED ARAB EMIRATES", "VIETNAM", "JORDAN", "BANGLADESH", "BOLIVIA", 
  "HUNGARY", "IRAQ", "LITHUANIA", "SLOVENIA", "ALBANIA", "PERU", "SYRIA", 
  "URUGUAY", "CUBA", "NEPAL", "PAKISTAN", "PANAMA", "BAHRAIN", "ICELAND", 
  "IRELAND", "KOSOVO", "KUWAIT", "MOROCCO", "PAPUA NEW GUINEA", "SRI LANKA", 
  "VENEZUELA", "ALGERIA", "ARMENIA", "BOSNIA & HERZEGOVINA", "CAYMAN ISLANDS", 
  "COSTA RICA", "CYPRUS", "DOMINICAN REP", "ETHIOPIA", "FIJI", "FRENCH GUIANA", 
  "GEORGIA", "KENYA", "LAOS", "LATVIA", "LEBANON", "LIBYA", "LUXEMBOURG", 
  "MADAGASCAR", "MALI", "MOLDOVA", "MONGOLIA", "MONTENEGRO", "MOZAMBIQUE", 
  "MYANMAR", "NAMIBIA", "NETH ANTILLES", "NIGERIA", "NORTH MACEDONIA", 
  "QATAR", "SENEGAL", "SUDAN", "TRINIDAD AND TOBAGO", "YEMEN", "YUGOSLAVIA"
)

cat("采用现有国家列表，包含", length(common_countries), "个国家/地区\n")

# 定义国家名称标准化映射表（地缘政治规则）
country_mapping <- c(
  # 美国标准化
  "UNITED STATES" = "USA", 
  "UNITED STATES OF AMERICA" = "USA",
  "U.S.A." = "USA", 
  "U S A" = "USA",
  
  # 中国统一处理 (包括台湾)
  "CHINA" = "CHINA",
  "P R CHINA" = "CHINA", 
  "PR CHINA" = "CHINA",
  "PEOPLES REPUBLIC OF CHINA" = "CHINA",
  "PEOPLES R CHINA" = "CHINA",
  "PEOPLE'S REPUBLIC OF CHINA" = "CHINA",
  "TAIWAN" = "CHINA", # 台湾归入中国
  "HONG KONG" = "CHINA", # 香港归入中国
  "MACAU" = "CHINA", # 澳门归入中国
  "MACAO" = "CHINA", # 澳门归入中国
  
  # 英国统一处理
  "UK" = "UNITED KINGDOM", 
  "UNITED KINGDOM" = "UNITED KINGDOM", 
  "U KINGDOM" = "UNITED KINGDOM",
  "GREAT BRITAIN" = "UNITED KINGDOM",
  "BRITAIN" = "UNITED KINGDOM",
  "ENGLAND" = "UNITED KINGDOM", # 英格兰归入英国
  "SCOTLAND" = "UNITED KINGDOM", # 苏格兰归入英国
  "WALES" = "UNITED KINGDOM", # 威尔士归入英国
  "NORTH IRELAND" = "UNITED KINGDOM", # 北爱尔兰归入英国
  "NORTHERN IRELAND" = "UNITED KINGDOM",
  
  # 德国统一处理
  "GERMANY" = "GERMANY",
  "WEST GERMANY" = "GERMANY",
  "EAST GERMANY" = "GERMANY",
  "FED REP GER" = "GERMANY",
  "GER DEM REP" = "GERMANY",
  "BRD" = "GERMANY",
  "DDR" = "GERMANY",
  
  # 俄罗斯相关
  "RUSSIAN FEDERATION" = "RUSSIA",
  "RUSSIA" = "RUSSIA",
  "SOVIET UNION" = "RUSSIA",
  "USSR" = "RUSSIA",
  
  # 其他常见标准化
  "BRASIL" = "BRAZIL",
  "REP OF KOREA" = "SOUTH KOREA", 
  "REPUBLIC OF KOREA" = "SOUTH KOREA", 
  "KOREA" = "SOUTH KOREA",
  "ISLAMIC REPUBLIC OF IRAN" = "IRAN",
  "UAE" = "UNITED ARAB EMIRATES", 
  "U ARAB EMIRATES" = "UNITED ARAB EMIRATES",
  "CZECHIA" = "CZECH REPUBLIC",
  "BOSNIA HERZEGOVINA" = "BOSNIA & HERZEGOVINA",
  "BOSNIA & HERCEG" = "BOSNIA & HERZEGOVINA",
  "TRINIDAD & TOBAGO" = "TRINIDAD AND TOBAGO",
  "PAPUA N GUINEA" = "PAPUA NEW GUINEA",
  "MACEDONIA" = "NORTH MACEDONIA",
  "TURKEY" = "TURKEY",
  "TURKIYE" = "TURKEY"
)

cat("创建国家名称标准化映射表，包含", length(country_mapping), "条规则\n")

# 美国州名列表（用于识别美国地址）
us_state_codes <- c("AL", "AK", "AZ", "AR", "CA", "CO", "CT", "DE", "FL", "GA", 
                   "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MD", 
                   "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ", 
                   "NM", "NY", "NC", "ND", "OH", "OK", "OR", "PA", "RI", "SC", 
                   "SD", "TN", "TX", "UT", "VT", "VA", "WA", "WV", "WI", "WY", "DC")

cat("创建美国州代码列表，包含", length(us_state_codes), "个州代码\n")

# 美国州名统一映射为USA
us_state_to_usa <- setNames(rep("USA", length(us_state_codes)), us_state_codes)

# 国家标准化函数
standardize_countries <- function(data) {
  # 检查是否有国家列或者原始国家信息列
  if (!"Raw_Country" %in% colnames(data)) {
    cat("警告: 未找到原始国家列，尝试从机构信息中提取国家...\n")
    
    # 尝试从机构列直接提取国家
    aff_col <- check_affiliation_columns(data)
    
    if (!is.null(aff_col)) {
      cat("使用", aff_col, "列直接提取国家信息...\n")
      
      # 创建临时Raw_Country列
      data <- data %>%
        mutate(
          Raw_Country = map_chr(!!sym(aff_col), function(c1) {
            if (is.na(c1) || c1 == "") return(NA_character_)
            
            parts <- str_split(c1, ",")[[1]]
            if (length(parts) < 2) return(NA_character_)
            
            # 通常国家名在地址的最后部分
            country_part <- trimws(parts[length(parts)])
            return(country_part)
          })
        )
      
      cat("已从机构信息中提取国家信息\n")
    } else {
      cat("无法找到合适的机构列提取国家信息，跳过国家标准化\n")
      return(data)
    }
  }
  
  cat("正在标准化国家名称...\n")
  
  # 创建标准化国家列
  data <- data %>%
    mutate(
      Primary_Country = map_chr(Raw_Country, function(country) {
        if (is.na(country) || country == "") return(NA_character_)
        
        # 转换为大写并去除前后空格
        country_upper <- toupper(trimws(country))
        
        # 检查是否存在于映射表中
        if (country_upper %in% names(country_mapping)) {
          return(country_mapping[country_upper])
        }
        
        # 检查是否包含美国州缩写
        for (state in us_state_codes) {
          if (grepl(paste0("\\b", state, "\\b"), country_upper)) {
            return("USA")
          }
        }
        
        # 检查是否包含常见国家关键词
        for (std_country in common_countries) {
          if (grepl(std_country, country_upper, fixed = TRUE)) {
            return(std_country)
          }
        }
        
        # 如果无法匹配，返回原始值
        return(country_upper)
      })
    )
  
  # 统计国家分布
  country_stats <- data %>%
    filter(!is.na(Primary_Country)) %>%
    count(Primary_Country, sort = TRUE) %>%
    mutate(percentage = sprintf("%.2f%%", n / sum(n) * 100))
  
  cat("国家标准化完成。共标准化", nrow(filter(data, !is.na(Primary_Country))), "条记录\n")
  cat("发现", n_distinct(data$Primary_Country, na.rm = TRUE), "个独特国家\n")
  
  # 输出前10个国家分布
  cat("前10个国家分布:\n")
  print(head(country_stats, 10))
  
  # 记录国家分布到日志
  log_summary_list[["国家分布"]] <- country_stats %>%
    mutate(步骤 = "国家标准化") %>%
    rename(国家 = Primary_Country, 数量 = n, 百分比 = percentage)
  
  # 检测异常国家名称
  potential_issues <- data %>%
    filter(!is.na(Primary_Country)) %>%
    filter(!Primary_Country %in% common_countries) %>%
    count(Primary_Country, sort = TRUE)
  
  if (nrow(potential_issues) > 0) {
    cat("警告: 发现", nrow(potential_issues), "个非标准国家名称:\n")
    print(potential_issues)
    
    log_summary_list[["非标准国家名称"]] <- potential_issues %>%
      mutate(步骤 = "国家标准化-异常检测") %>%
      rename(非标准国家名称 = Primary_Country, 出现次数 = n)
  }
  
  return(data)
}

# 应用国家标准化
wos_df <- standardize_countries(wos_df)

# --- 机构和国家汇总统计 ---
cat("\n5.3 生成机构和国家汇总统计...\n")

# 机构统计
institution_stats <- wos_df %>%
  filter(!is.na(Primary_Institution)) %>%
  count(Primary_Institution, sort = TRUE) %>%
  mutate(percentage = sprintf("%.2f%%", n / sum(n) * 100))

cat("发现", n_distinct(wos_df$Primary_Institution, na.rm = TRUE), "个独特机构\n")
cat("前10个机构分布:\n")
print(head(institution_stats, 10))

# 记录机构分布到日志
log_summary_list[["机构分布"]] <- institution_stats %>%
  head(50) %>%
  mutate(步骤 = "机构标准化-前50") %>%
  rename(机构 = Primary_Institution, 数量 = n, 百分比 = percentage)

# 导出机构和国家分布
output_inst_country_file <- file.path(output_location, "institution_country_distribution_v5.xlsx")
write_xlsx(
  list(
    "机构分布" = institution_stats,
    "国家分布" = wos_df %>% 
      filter(!is.na(Primary_Country)) %>%
      count(Primary_Country, sort = TRUE) %>%
      mutate(percentage = sprintf("%.2f%%", n / sum(n) * 100))
  ),
  output_inst_country_file
)
cat("机构和国家分布已保存至:", output_inst_country_file, "\n")

# --- 继续下一阶段处理 ---
cat("\n机构和国家标准化完成，继续下一阶段处理...\n")

# 处理并整合日志摘要
cat("正在处理日志摘要...\n")

# 确保所有日志元素都是有效的数据框
valid_log_entries <- list()
for (name in names(log_summary_list)) {
  entry <- log_summary_list[[name]]
  if (!is.null(entry) && (is.data.frame(entry) || is_tibble(entry)) && nrow(entry) > 0) {
    # 确保所有列都转换为字符型，以便于合并
    entry <- as.data.frame(entry)
    for (col in names(entry)) {
      # 安全地转换为字符型，处理所有类型包括pillar_num
      tryCatch({
        if (is.numeric(entry[[col]])) {
          entry[[col]] <- as.character(as.numeric(entry[[col]]))
        } else {
          entry[[col]] <- as.character(entry[[col]])
        }
      }, error = function(e) {
        # 如果转换失败，尝试使用toString()或其他方法
        tryCatch({
          entry[[col]] <- sapply(entry[[col]], function(x) if(is.null(x)) NA_character_ else toString(x))
        }, error = function(e2) {
          # 如果仍然失败，设置为NA
          entry[[col]] <- NA_character_
          cat("警告: 无法转换列 '", col, "' 为字符型: ", conditionMessage(e2), "\n")
        })
      })
    }
    valid_log_entries[[name]] <- entry
  } else {
    cat("警告: 跳过无效的日志项 '", name, "'\n")
  }
}

if (length(valid_log_entries) > 0) {
  tryCatch({
    # 使用do.call和rbind来合并数据框，这种方式更适合处理不同结构的数据框
    combined_dfs <- list()
    for (name in names(valid_log_entries)) {
      df <- valid_log_entries[[name]]
      df$日志组 <- name
      combined_dfs[[name]] <- df
    }
    
    # 尝试合并所有数据框
    if (length(combined_dfs) > 0) {
      log_summary_df <- do.call(rbind, combined_dfs)
      write_xlsx(log_summary_df, log_file_excel)
      cat("日志摘要已保存至:", log_file_excel, "\n")
    }
  }, error = function(e) {
    cat("警告: 合并日志摘要时出错:", conditionMessage(e), "\n")
    cat("将尝试单独保存每个日志项...\n")
    
    # 如果合并失败，单独保存每个有效的日志项
    log_file_excel_base <- gsub(".xlsx$", "", log_file_excel)
    for (name in names(valid_log_entries)) {
      try({
        item_file <- paste0(log_file_excel_base, "_", name, ".xlsx")
        write_xlsx(valid_log_entries[[name]], item_file)
        cat("已保存日志项 '", name, "' 至:", item_file, "\n")
      }, silent = TRUE)
    }
  })
} else {
  cat("警告: 没有有效的日志项可以保存\n")
}

# --- 3.1 数据清洗与规范化 ---
cat("3.1 正在进行数据清洗与规范化...\n")

# 识别并处理DOI字段
cat("正在清理DOI字段...\n")
if("DI" %in% colnames(wos_df)) {
  cat("使用DI字段作为DOI...\n")
  wos_df$doi_cleaned <- wos_df$DI
} else if("DOI" %in% colnames(wos_df)) {
  cat("使用DOI字段作为DOI...\n")
  wos_df$doi_cleaned <- wos_df$DOI
} else {
  cat("警告：未找到DOI字段，将创建空字段...\n")
  wos_df$doi_cleaned <- NA_character_
}

# 清理DOI字段并统计覆盖率
wos_df <- wos_df %>%
  mutate(doi_cleaned = tolower(trimws(doi_cleaned)))

doi_coverage <- sum(!is.na(wos_df$doi_cleaned) & wos_df$doi_cleaned != "")
doi_coverage_percentage <- sprintf("%.2f%%", 100 * doi_coverage / nrow(wos_df))
cat("DOI字段覆盖率:", doi_coverage, "/", nrow(wos_df), "(", doi_coverage_percentage, ")\n")

# 将DOI覆盖率添加到日志
log_summary_list[["DOI覆盖率"]] <- tibble(
  步骤 = "DOI覆盖率统计",
  总记录数 = nrow(wos_df),
  有DOI记录数 = doi_coverage,
  无DOI记录数 = nrow(wos_df) - doi_coverage,
  DOI覆盖率 = doi_coverage_percentage
)

# 识别并处理UT标识符字段
cat("正在清理WOS UT字段...\n")
if("UT" %in% colnames(wos_df)) {
  cat("使用UT字段作为WOS ID...\n")
  wos_df$UT_cleaned <- wos_df$UT
} else if("WOS_ID" %in% colnames(wos_df)) {
  cat("使用WOS_ID字段作为WOS ID...\n")
  wos_df$UT_cleaned <- wos_df$WOS_ID
} else if(any(grepl("^WOS:", colnames(wos_df)))) {
  # 查找以WOS:开头的列名
  wos_col <- grep("^WOS:", colnames(wos_df), value = TRUE)[1]
  cat("使用", wos_col, "字段作为WOS ID...\n")
  wos_df$UT_cleaned <- wos_df[[wos_col]]
} else {
  cat("警告：未找到WOS UT字段，将创建空字段...\n")
  wos_df$UT_cleaned <- NA_character_
}

# 清理UT字段并统计覆盖率
wos_df <- wos_df %>%
  mutate(UT_cleaned = trimws(UT_cleaned))

ut_coverage <- sum(!is.na(wos_df$UT_cleaned) & wos_df$UT_cleaned != "")
ut_coverage_percentage <- sprintf("%.2f%%", 100 * ut_coverage / nrow(wos_df))
cat("WOS UT字段覆盖率:", ut_coverage, "/", nrow(wos_df), "(", ut_coverage_percentage, ")\n")

# 将UT覆盖率添加到日志
log_summary_list[["UT覆盖率"]] <- tibble(
  步骤 = "WOS UT覆盖率统计",
  总记录数 = nrow(wos_df),
  有WOS记录数 = ut_coverage,
  无WOS记录数 = nrow(wos_df) - ut_coverage,
  WOS覆盖率 = ut_coverage_percentage
)

# 统计无标识符记录数量
no_id_records <- sum(is.na(wos_df$doi_cleaned) & is.na(wos_df$UT_cleaned) | 
                    (wos_df$doi_cleaned == "" & wos_df$UT_cleaned == ""))
no_id_percentage <- sprintf("%.2f%%", 100 * no_id_records / nrow(wos_df))
cat("无标识符记录数量:", no_id_records, "/", nrow(wos_df), "(", no_id_percentage, ")\n")

# 将无标识符覆盖率添加到日志
log_summary_list[["无标识符记录"]] <- tibble(
  步骤 = "无标识符记录统计",
  总记录数 = nrow(wos_df),
  无标识符记录数 = no_id_records,
  无标识符百分比 = no_id_percentage,
  计算方法 = "总数减去有DOI或有WOS号的记录数"
)

# 继续处理年份字段...
# ... 现有代码 ...

# --- 3.3 无DOI/UT的记录模糊匹配去重处理 ---
cat("3.3 正在处理无DOI和无UT的记录...\n")

# 计算无标识符记录数量（无DOI且无WOS UT）
no_id_records <- wos_df %>%
  filter(is.na(doi_cleaned) | doi_cleaned == "", 
         is.na(UT_cleaned) | UT_cleaned == "")

cat("无标识符记录数量:", nrow(no_id_records), "/", nrow(wos_df), 
    sprintf("(%.2f%%)", 100 * nrow(no_id_records) / nrow(wos_df)), "\n")
cat("计算方法: 总数", nrow(wos_df), "减去有DOI数", doi_coverage, 
    "减去无DOI但有WOS号的记录数", ut_coverage - sum(!is.na(wos_df$doi_cleaned) & !is.na(wos_df$UT_cleaned) & 
                                           wos_df$doi_cleaned != "" & wos_df$UT_cleaned != ""), "\n")

if(nrow(no_id_records) == 0) {
  cat("没有需要处理的无标识记录，跳过模糊匹配去重。\n")
} else {
  cat("开始对无标识符记录进行模糊匹配去重...\n")
  cat("注意: 模糊匹配将与整个数据库中的记录比较，而不仅仅是无标识符记录之间\n")
  
  # 创建用于模糊匹配的字段组合
  wos_df <- wos_df %>%
    mutate(
      fuzzy_match_key = paste0(
        tolower(trimws(TI)), "|",  # 标题
        tolower(trimws(AU)), "|",  # 作者
        tolower(trimws(SO)), "|",  # 期刊
        as.character(PY_numeric)    # 年份
      )
    )
  
  # 找出潜在的重复项
  potential_duplicates <- wos_df %>%
    group_by(fuzzy_match_key) %>%
    filter(n() > 1) %>%
    ungroup()
  
  # 筛选出包含无标识符记录的重复组
  dup_groups_with_no_id <- potential_duplicates %>%
    filter(is.na(doi_cleaned) | doi_cleaned == "", 
           is.na(UT_cleaned) | UT_cleaned == "") %>%
    pull(fuzzy_match_key) %>%
    unique()
  
  cat("发现", length(dup_groups_with_no_id), "个包含无标识符记录的重复组\n")
  
  if(length(dup_groups_with_no_id) > 0) {
    # 找出这些组中的所有记录
    records_in_dup_groups <- wos_df %>%
      filter(fuzzy_match_key %in% dup_groups_with_no_id)
    
    # 对每组记录进行排序，优先保留有标识符的记录
    wos_df_deduped <- wos_df %>%
      group_by(fuzzy_match_key) %>%
      mutate(
        has_identifier = (!is.na(doi_cleaned) & doi_cleaned != "") | 
                         (!is.na(UT_cleaned) & UT_cleaned != ""),
        keep_record = row_number() == 1 | has_identifier # 保留第一条记录和所有有标识符的记录
      ) %>%
      ungroup() %>%
      filter(!fuzzy_match_key %in% dup_groups_with_no_id | keep_record) %>%
      select(-fuzzy_match_key, -has_identifier, -keep_record)
    
    # 计算去重结果
    removed_count <- nrow(wos_df) - nrow(wos_df_deduped)
    cat("通过模糊匹配去重移除了", removed_count, "条记录\n")
    cat("去重后的记录数量:", nrow(wos_df_deduped), "\n")
    
    # 更新工作数据框
    wos_df <- wos_df_deduped
    
    # 记录去重结果到日志
    log_summary_list[["模糊匹配去重"]] <- tibble(
      步骤 = "无标识符记录模糊匹配去重",
      去重前记录数 = nrow(wos_df) + removed_count,
      无标识符记录数 = nrow(no_id_records),
      识别出的重复组数 = length(dup_groups_with_no_id),
      移除的记录数 = removed_count,
      去重后记录数 = nrow(wos_df),
      计算方法说明 = "无标识记录(无DOI且无WOS号)与整个数据库中的记录进行模糊匹配比较"
    )
  } else {
    cat("未发现需要去重的无标识符记录组，保持原始数据不变。\n")
    
    # 记录去重结果到日志
    log_summary_list[["模糊匹配去重"]] <- tibble(
      步骤 = "无标识符记录模糊匹配去重",
      去重前记录数 = nrow(wos_df),
      无标识符记录数 = nrow(no_id_records),
      识别出的重复组数 = 0,
      移除的记录数 = 0,
      去重后记录数 = nrow(wos_df),
      计算方法说明 = "未发现需要去重的无标识符记录组"
    )
  }
}

