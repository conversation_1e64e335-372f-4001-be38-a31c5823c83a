% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/networkPlot.R
\name{networkPlot}
\alias{networkPlot}
\title{Plotting Bibliographic networks}
\usage{
networkPlot(
  NetMatrix,
  normalize = NULL,
  n = NULL,
  degree = NULL,
  Title = "Plot",
  type = "auto",
  label = TRUE,
  labelsize = 1,
  label.cex = FALSE,
  label.color = FALSE,
  label.n = NULL,
  halo = FALSE,
  cluster = "walktrap",
  community.repulsion = 0.1,
  vos.path = NULL,
  size = 3,
  size.cex = FALSE,
  curved = FALSE,
  noloops = TRUE,
  remove.multiple = TRUE,
  remove.isolates = FALSE,
  weighted = NULL,
  edgesize = 1,
  edges.min = 0,
  alpha = 0.5,
  verbose = TRUE
)
}
\arguments{
\item{NetMatrix}{is a network matrix obtained by the function \code{\link{biblioNetwork}}.}

\item{normalize}{is a character. It can be "association", "jaccard", "inclusion","salton" or "equivalence" to obtain Association Strength, J<PERSON><PERSON>, 
Inclusion, Salton or Equivalence similarity index respectively. The default is type = NULL.}

\item{n}{is an integer. It indicates the number of vertices to plot.}

\item{degree}{is an integer. It indicates the min frequency of a vertex. If degree is not NULL, n is ignored.}

\item{Title}{is a character indicating the plot title.}

\item{type}{is a character object. It indicates the network map layout: 

\tabular{lll}{
\code{type="auto"}\tab   \tab Automatic layout selection\cr
\code{type="circle"}\tab   \tab Circle layout\cr
\code{type="sphere"}\tab   \tab Sphere layout\cr
\code{type="mds"}\tab   \tab Multidimensional Scaling layout\cr
\code{type="fruchterman"}\tab   \tab Fruchterman-Reingold layout\cr
\code{type="kamada"}\tab   \tab  Kamada-Kawai layout}}

\item{label}{is logical. If TRUE vertex labels are plotted.}

\item{labelsize}{is an integer. It indicates the label size in the plot. Default is \code{labelsize=1}}

\item{label.cex}{is logical. If TRUE the label size of each vertex is proportional to its degree.}

\item{label.color}{is logical. If TRUE, for each vertex, the label color is the same as its cluster.}

\item{label.n}{is an integer. It indicates the number of vertex labels to draw.}

\item{halo}{is logical. If TRUE communities are plotted using different colors. Default is \code{halo=FALSE}}

\item{cluster}{is a character. It indicates the type of cluster to perform among ("none", "optimal", "louvain","leiden", "infomap","edge_betweenness","walktrap", "spinglass", "leading_eigen", "fast_greedy").}

\item{community.repulsion}{is a real. It indicates the repulsion force among network communities. It is a real number between 0 and 1. Default is \code{community.repulsion = 0.1}.}

\item{vos.path}{is a character indicating the full path where VOSviewer.jar is located.}

\item{size}{is integer. It defines the size of each vertex. Default is \code{size=3}.}

\item{size.cex}{is logical. If TRUE the size of each vertex is proportional to its degree.}

\item{curved}{is a logical or a number. If TRUE edges are plotted with an optimal curvature. Default is \code{curved=FALSE}. Curved values are any numbers from 0 to 1.}

\item{noloops}{is logical. If TRUE loops in the network are deleted.}

\item{remove.multiple}{is logical. If TRUE multiple links are plotted using just one edge.}

\item{remove.isolates}{is logical. If TRUE isolates vertices are not plotted.}

\item{weighted}{This argument specifies whether to create a weighted graph from an adjacency matrix. 
If it is NULL then an unweighted graph is created and the elements of the adjacency matrix gives the number of edges between the vertices. 
If it is a character constant then for every non-zero matrix entry an edge is created and the value of the entry is added as an edge attribute 
named by the weighted argument. If it is TRUE then a weighted graph is created and the name of the edge attribute will be weight.}

\item{edgesize}{is an integer. It indicates the network edge size.}

\item{edges.min}{is an integer. It indicates the min frequency of edges between two vertices. If edge.min=0, all edges are plotted.}

\item{alpha}{is a number. Legal alpha values are any numbers from 0 (transparent) to 1 (opaque). The default alpha value usually is 0.5.}

\item{verbose}{is a logical. If TRUE, network will be plotted. Default is \code{verbose = TRUE}.}
}
\value{
It is a list containing the following elements:
\tabular{lll}{
\code{graph} \tab  \tab a network object of the class \code{igraph}\cr
\code{cluster_obj} \tab  \tab a \code{communities} object of the package \code{igraph}\cr
\code{cluster_res} \tab  \tab a data frame with main results of clustering procedure.\cr}
}
\description{
\code{networkPlot} plots a bibliographic network.
}
\details{
The function \code{\link{networkPlot}} can plot a bibliographic network previously created by \code{\link{biblioNetwork}}.
}
\examples{
# EXAMPLE Keywordd co-occurrence network

data(management, package = "bibliometrixData")

NetMatrix <- biblioNetwork(management, analysis = "co-occurrences", 
network = "keywords", sep = ";")

net <- networkPlot(NetMatrix, n = 30, type = "auto", Title = "Co-occurrence Network",labelsize=1) 

}
\seealso{
\code{\link{biblioNetwork}} to compute a bibliographic network.

\code{\link{net2VOSviewer}} to export and plot the network with VOSviewer software.

\code{\link{cocMatrix}} to compute a co-occurrence matrix.

\code{\link{biblioAnalysis}} to perform a bibliometric analysis.
}
