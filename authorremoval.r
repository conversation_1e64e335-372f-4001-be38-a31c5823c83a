# 设置工作目录
setwd("C:/Users/<USER>/Desktop/google数据处相关/作者消岐")
cat("工作目录设置为:", getwd(), "\n")

# 加载必要的包
cat("正在加载所需R包...\n")
libraries_to_load <- c("bibliometrix", "tidyverse", "rcrossref", "openalexR", "stringr", "R.utils", 
                       "DBI", "RSQLite", "jsonlite") # 添加数据库和JSON处理包

for (lib in libraries_to_load) {
  if (!require(lib, character.only = TRUE, quietly = TRUE)) {
    cat(sprintf("尝试安装并加载包: %s\n", lib))
    tryCatch({
      install.packages(lib)
      library(lib, character.only = TRUE)
    }, error = function(e) {
      stop(sprintf("无法安装或加载必要的包 '%s': %s\n", lib, e$message))
    })
  }
}
cat("所有包加载完成\n")

# 创建本地SQLite数据库
cat("创建本地数据库...\n")
db_path <- "bibliometrix_api_data.sqlite"
db_conn <- dbConnect(SQLite(), db_path)

# 创建必要的表结构
if(!dbExistsTable(db_conn, "api_responses")) {
  dbExecute(db_conn, "CREATE TABLE api_responses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    source TEXT NOT NULL,
    query_type TEXT NOT NULL,
    query_value TEXT NOT NULL,
    response_data TEXT NOT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
  )")
  cat("创建API响应表成功\n")
}

if(!dbExistsTable(db_conn, "author_identifiers")) {
  dbExecute(db_conn, "CREATE TABLE author_identifiers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    author_name TEXT NOT NULL,
    identifier_type TEXT NOT NULL,
    identifier_value TEXT NOT NULL,
    source TEXT NOT NULL,
    verification_status TEXT,
    document_id TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
  )")
  cat("创建作者标识符表成功\n")
}

# 用于保存API响应到数据库的函数
save_api_response <- function(source, query_type, query_value, response_data) {
  # 将响应数据转换为JSON字符串
  json_data <- toJSON(response_data, auto_unbox = TRUE)
  
  # 保存到数据库
  query <- "INSERT INTO api_responses (source, query_type, query_value, response_data) VALUES (?, ?, ?, ?)"
  dbExecute(db_conn, query, params = list(source, query_type, query_value, json_data))
  
  # 返回插入的ID
  return(dbGetQuery(db_conn, "SELECT last_insert_rowid() as id")$id)
}

# 保存作者标识符到数据库的函数
save_author_identifier <- function(author_name, identifier_type, identifier_value, source, verification_status = NULL, document_id = NULL) {
  query <- "INSERT INTO author_identifiers (author_name, identifier_type, identifier_value, source, verification_status, document_id) 
            VALUES (?, ?, ?, ?, ?, ?)"
  dbExecute(db_conn, query, params = list(author_name, identifier_type, identifier_value, source, verification_status, document_id))
}

#=========================
# 1. 导入WoS数据并检查标识符覆盖情况
#=========================

# 导入WoS数据 - 使用指定的文件路径
wos_file <- "C:/Users/<USER>/Desktop/google数据处相关/作者消岐/download1-500.txt"

# 检查文件是否存在
if(!file.exists(wos_file)) {
  stop("指定的数据文件不存在: ", wos_file)
}

cat("正在导入文件:", wos_file, "\n")
cat("文件大小:", file.info(wos_file)$size, "字节\n")

tryCatch({
  cat("开始转换WoS数据...\n")
  # 使用bibliometrix的convert2df函数，遵循bibliometrix标准
  wos_data <- convert2df(wos_file, dbsource = "wos", format = "plaintext")
  cat("WoS数据转换完成\n")
  
  # 检查数据结构
  cat("数据维度:", paste(dim(wos_data), collapse="x"), "\n")
  cat("数据列名:", paste(names(wos_data), collapse=", "), "\n")
  
  # 检查标识符覆盖情况
  cat("计算标识符覆盖情况...\n")
  identifiers_coverage <- data.frame(
    文章总数 = nrow(wos_data),
    有DOI文章数 = sum(!is.na(wos_data$DI) & wos_data$DI != ""),
    有DOI百分比 = round(sum(!is.na(wos_data$DI) & wos_data$DI != "")/nrow(wos_data)*100, 2),
    有UT文章数 = sum(!is.na(wos_data$UT) & wos_data$UT != ""),
    有UT百分比 = round(sum(!is.na(wos_data$UT) & wos_data$UT != "")/nrow(wos_data)*100, 2)
  )
  
  print(identifiers_coverage)
  
  # 将导入的WoS数据存储到数据库中的表
  if(!dbExistsTable(db_conn, "wos_documents")) {
    # 创建表结构
    dbWriteTable(db_conn, "wos_documents", wos_data[1:0,], row.names = FALSE)
    cat("创建WoS文档表成功\n")
  }
  
  # 分批写入数据以避免内存问题
  batch_size <- 100
  n_batches <- ceiling(nrow(wos_data) / batch_size)
  
  for(i in 1:n_batches) {
    start_idx <- (i-1) * batch_size + 1
    end_idx <- min(i * batch_size, nrow(wos_data))
    batch <- wos_data[start_idx:end_idx, ]
    
    # 使用dbAppendTable添加数据
    dbAppendTable(db_conn, "wos_documents", batch)
    cat(sprintf("已保存WoS数据批次 %d/%d 到数据库\n", i, n_batches))
  }
}, error = function(e) {
  cat("导入数据时出错:\n")
  print(e)
  stop("数据导入失败，脚本终止")
})

#=========================
# 2. 提取并标准化作者和机构信息（严格遵照bibliometrix规范）
#=========================

# 使用bibliometrix的原生函数提取作者-机构信息
tryCatch({
  cat("提取作者-机构信息...\n")
  # 使用bibliometrix的metaTagExtraction函数处理作者和机构关系
  author_affiliation <- metaTagExtraction(wos_data, Field = "AU_UN", sep = ";")
  cat("作者-机构信息提取完成\n")
  
  # 保存作者-机构关系到数据库
  if(!dbExistsTable(db_conn, "author_affiliations")) {
    dbWriteTable(db_conn, "author_affiliations", author_affiliation, row.names = FALSE)
    cat("作者-机构关系保存到数据库\n")
  } else {
    # 如果表已存在，先清空再插入
    dbExecute(db_conn, "DELETE FROM author_affiliations")
    dbAppendTable(db_conn, "author_affiliations", author_affiliation)
    cat("作者-机构关系更新到数据库\n")
  }
}, error = function(e) {
  warning("提取作者-机构信息时出错: ", e$message)
  author_affiliation <- data.frame()
})

# 使用bibliometrix的authors函数获取作者信息 - 明确指定包名
tryCatch({
  cat("获取标准化作者信息...\n")
  # 使用bibliometrix::biblioAnalysis进行分析
  biblio_results <- bibliometrix::biblioAnalysis(wos_data)
  # 然后使用bibliometrix::authors提取作者信息
  authors_data <- bibliometrix::authors(biblio_results)
  cat("获取到", length(authors_data), "位不同作者\n")
  
  # 将作者信息保存到数据库
  if(!is.null(authors_data) && length(authors_data) > 0) {
    authors_df <- data.frame(
      author = names(authors_data),
      frequency = as.numeric(authors_data),
      stringsAsFactors = FALSE
    )
    
    if(!dbExistsTable(db_conn, "bibliometrix_authors")) {
      dbWriteTable(db_conn, "bibliometrix_authors", authors_df, row.names = FALSE)
    } else {
      dbExecute(db_conn, "DELETE FROM bibliometrix_authors")
      dbAppendTable(db_conn, "bibliometrix_authors", authors_df)
    }
    cat("作者信息保存到数据库\n")
  }
}, error = function(e) {
  warning("获取作者信息时出错: ", e$message)
})

# 严格遵照bibliometrix作者名称标准化
# bibliometrix要求所有字符大写，使用分号分隔
wos_data$AU_NORM <- NA
tryCatch({
  cat("标准化作者名称...\n")
  # 使用bibliometrix的标准化方法，保留原始格式
  # 不自定义处理，确保与bibliometrix处理逻辑一致
  wos_data$AU_NORM <- wos_data$AU
  cat("作者名称标准化完成\n")
}, error = function(e) {
  warning("作者名称标准化时出错: ", e$message)
})

#=========================
# 3. 使用外部API获取ORCID等标识符，严格按bibliometrix格式处理返回数据
#=========================

# 安全获取DOI列表
dois_to_check <- character(0)
if("DI" %in% names(wos_data)) {
  dois_to_check <- wos_data$DI[!is.na(wos_data$DI) & wos_data$DI != ""]
}

# 避免空DOI列表
if(length(dois_to_check) == 0) {
  cat("警告: 未找到有效的DOI，无法从外部API获取作者标识符\n")
} else {
  # 限制请求数量避免API限制
  if(length(dois_to_check) > 50) {
    set.seed(123) # 确保可重复性
    dois_to_check <- sample(dois_to_check, 50)
    cat("注意: 仅抽样处理了50个DOI以避免API限制\n")
  }
}

# 从Crossref获取ORCID信息
get_crossref_orcids <- function(dois) {
  if(length(dois) == 0) return(data.frame())
  
  orcid_data <- list()
  for(i in seq_along(dois)) {
    cat(sprintf("处理DOI %d/%d: %s\n", i, length(dois), dois[i]))
    
    # 添加重试机制
    max_retries <- 3
    retry_count <- 0
    success <- FALSE
    
    while(!success && retry_count < max_retries) {
      tryCatch({
        # 增加超时时间
        result <- cr_works(dois = dois[i], .progress = FALSE, verbose = FALSE, timeout = 30)
        
        # 更健壮的数据处理
        if(is.list(result) && "data" %in% names(result)) {
          # 模拟保存API响应
          save_api_response("Crossref", "DOI", dois[i], result)
          
          # 更严格的数据结构检查
          if(is.data.frame(result$data) && nrow(result$data) > 0) {
            # 检查author列是否存在
            if("author" %in% names(result$data)) {
              # 获取author列的内容，可能是列表
              authors <- result$data$author
              
              # 确保authors是列表并且不为空
              if(is.list(authors) && length(authors) > 0) {
                # 逐个处理作者
                for(j in seq_along(authors[[1]])) {
                  # 确保能够安全访问作者数据
                  if(j <= length(authors[[1]])) {
                    current_author <- authors[[1]][j]
                    
                    # 提取作者姓名
                    author_name <- NA_character_
                    
                    # 安全访问family和given
                    if(is.list(current_author) && 
                       "family" %in% names(current_author[[1]]) && 
                       "given" %in% names(current_author[[1]])) {
                      
                      family <- toupper(current_author[[1]]$family)
                      given <- current_author[[1]]$given
                      author_name <- paste(family, given, sep=", ")
                    }
                    
                    # 安全提取ORCID
                    if(is.list(current_author) && 
                       "ORCID" %in% names(current_author[[1]]) && 
                       !is.null(current_author[[1]]$ORCID)) {
                      
                      # 确保ORCID是字符串
                      if(is.character(current_author[[1]]$ORCID)) {
                        orcid <- gsub("http[s]?://orcid.org/", "", current_author[[1]]$ORCID)
                        
                        # 添加到结果
                        if(!is.na(author_name) && nchar(orcid) > 0) {
                          orcid_data[[length(orcid_data) + 1]] <- list(
                            DOI = dois[i],
                            author_name = author_name,
                            orcid = orcid
                          )
                          
                          # 模拟保存
                          save_author_identifier(
                            author_name = author_name,
                            identifier_type = "ORCID",
                            identifier_value = orcid,
                            source = "Crossref",
                            verification_status = "Crossref验证",
                            document_id = dois[i]
                          )
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
        
        # 标记成功
        success <- TRUE
        
      }, error = function(e) {
        retry_count <- retry_count + 1
        cat(sprintf("处理DOI时出错(尝试 %d/%d): %s - 错误: %s\n", 
                   retry_count, max_retries, dois[i], e$message))
        
        # 记录详细错误信息以便调试
        cat(sprintf("错误详情: %s\n", as.character(e)))
        
        # 如果是超时错误，增加等待时间
        if(grepl("Timeout", e$message)) {
          Sys.sleep(5) # 超时错误时等待更长时间
        } else {
          Sys.sleep(2) # 其他错误等待标准时间
        }
      })
      
      # 即使成功也添加延迟避免API速率限制
      Sys.sleep(1)
    }
    
    # 如果所有重试都失败，记录最终失败
    if(!success) {
      cat(sprintf("处理DOI最终失败: %s - 已尝试 %d 次\n", dois[i], max_retries))
    }
  }
  
  # 安全合并结果
  if(length(orcid_data) > 0) {
    # 将列表转换为数据框
    result_df <- do.call(rbind, lapply(orcid_data, function(x) {
      data.frame(
        DOI = x$DOI,
        author_name = x$author_name,
        orcid = x$orcid,
        stringsAsFactors = FALSE
      )
    }))
    return(result_df)
  }
  
  return(data.frame()) # 返回空数据框
}

# 获取ORCID数据
cat("从Crossref获取ORCID数据...\n")
orcid_info <- data.frame()
if(length(dois_to_check) > 0) {
  orcid_info <- get_crossref_orcids(dois_to_check)
}

# 使用httr直接调用OpenAlex API，并按照bibliometrix格式处理返回数据
api_request_direct <- function(url, max_retries = 3, delay_seconds = 1) {
  for (i in 1:max_retries) {
    tryCatch({
      # 明确使用httr::GET和httr::timeout
      response <- httr::GET(url, httr::timeout(30)) 
      
      # 检查HTTP状态
      status <- httr::status_code(response)
      if (status == 200) {
        return(response)
      } else if (status == 429) {
        wait_time <- delay_seconds * (2^i)
        cat(sprintf("  -> API速率限制(429)，等待 %.1f 秒后重试...\n", wait_time))
        Sys.sleep(wait_time)
      } else if (status == 404) {
        cat(sprintf("  -> 资源未找到(404)，跳过重试\n"))
        return(NULL) # 404 不重试
      } else {
        cat(sprintf("  -> API请求失败，状态码: %d, 尝试 %d/%d\n", status, i, max_retries))
        Sys.sleep(delay_seconds)
      }
    }, error = function(e) {
      cat(sprintf("  -> API请求错误: %s, 尝试 %d/%d\n", e$message, i, max_retries))
      Sys.sleep(delay_seconds)
    })
  }
  return(NULL)  # 所有重试都失败
}

# 直接查询OpenAlex并按bibliometrix格式处理返回数据
query_openalex_direct <- function(doi, email = "<EMAIL>") {
  if (is.na(doi) || doi == "") return(NULL)
  doi <- str_trim(doi)
  url <- paste0("https://api.openalex.org/works/https://doi.org/", doi, "?mailto=", email)
  
  response <- api_request_direct(url)
  if (is.null(response)) {
    cat(sprintf("  -> OpenAlex DOI %s 请求最终失败\n", doi))
    return(NULL)
  }
  
  tryCatch({
    # 将返回内容解析为文本并转换为JSON
    content_text <- httr::content(response, "text", encoding = "UTF-8")
    content <- fromJSON(content_text, flatten = TRUE)
    
    # 保存原始API响应到数据库
    response_id <- save_api_response("OpenAlex", "DOI", doi, content)
    
    if (!is.null(content$error)) {
      cat(sprintf("  -> OpenAlex API 返回错误: %s\n", content$error))
      return(NULL)
    }
    
    # 处理作者信息，按照bibliometrix格式
    if("authorships" %in% names(content) && is.list(content$authorships) && length(content$authorships) > 0) {
      # 创建结果列表
      result <- list(
        doi = doi,
        authors = list()
      )
      
      # 处理每位作者
      for(i in seq_along(content$authorships)) {
        authorship <- content$authorships[[i]]
        
        if(is.list(authorship) && "author" %in% names(authorship) && is.list(authorship$author)) {
          author <- authorship$author
          
          if("display_name" %in% names(author)) {
            # 格式化作者姓名为bibliometrix标准格式
            display_name <- author$display_name
            name_parts <- strsplit(display_name, " ")[[1]]
            
            # 取姓氏（假设最后一个部分是姓氏）
            if(length(name_parts) > 1) {
              last_name <- toupper(name_parts[length(name_parts)])  # 姓氏大写
              first_name <- paste(name_parts[-length(name_parts)], collapse=" ")
              author_name <- paste(last_name, first_name, sep=", ")  # bibliometrix格式
            } else {
              author_name <- toupper(display_name)
            }
            
            # 提取ORCID
            orcid <- NA_character_
            if("orcid" %in% names(author) && !is.null(author$orcid)) {
              orcid <- gsub("http[s]?://orcid.org/", "", author$orcid)
            }
            
            # 添加到结果
            result$authors[[length(result$authors) + 1]] <- list(
              name = author_name,
              orcid = orcid,
              openalex_id = author$id
            )
            
            # 保存到数据库
            if(!is.na(orcid) && nchar(orcid) > 0) {
              save_author_identifier(
                author_name = author_name,
                identifier_type = "ORCID",
                identifier_value = orcid,
                source = "OpenAlex",
                verification_status = "OpenAlex验证",
                document_id = doi
              )
            }
            
            if(!is.null(author$id) && !is.na(author$id)) {
              save_author_identifier(
                author_name = author_name,
                identifier_type = "OpenAlex_ID",
                identifier_value = author$id,
                source = "OpenAlex",
                verification_status = "OpenAlex验证",
                document_id = doi
              )
            }
          }
        }
      }
      
      cat(sprintf("  -> OpenAlex DOI %s 请求成功，处理了 %d 位作者\n", 
                 doi, length(result$authors)))
      return(result)
    }
    
    cat(sprintf("  -> OpenAlex DOI %s 请求成功，但未找到作者信息\n", doi))
    return(NULL)
    
  }, error = function(e) {
    cat(sprintf("  -> 解析OpenAlex JSON时出错: %s\n", e$message))
    return(NULL)
  })
}

# 从OpenAlex获取作者信息
cat("从OpenAlex获取作者信息（直接httr调用, 批次=1, 等待5s）...\n")
oa_authors_list <- list()
if(length(dois_to_check) > 0) {
  for (i in seq_along(dois_to_check)) {
    current_doi <- dois_to_check[i]
    cat(sprintf("OpenAlex处理 %d/%d (DOI: %s)...\n", i, length(dois_to_check), current_doi))
    
    # 使用新的按bibliometrix格式处理的查询函数
    res <- query_openalex_direct(current_doi)
    
    if(!is.null(res)) {
      oa_authors_list[[length(oa_authors_list) + 1]] <- res
    }
    
    cat(sprintf("OpenAlex DOI %s 处理完成, 等待5秒...\n", current_doi))
    Sys.sleep(5) 
  }
}

# 合并所有结果 - 数据已经按bibliometrix格式处理并存入数据库
cat("合并所有OpenAlex批次结果...\n")
if(length(oa_authors_list) > 0) {
  cat(sprintf("成功获取 %d 个OpenAlex文献的详细信息\n", length(oa_authors_list)))
} else {
  cat("所有OpenAlex DOI均未能成功获取数据\n")
}

#=========================
# 4. 整合标识符信息到原始数据，遵循bibliometrix数据格式
#=========================

# 从数据库中获取所有作者标识符信息
cat("从数据库获取作者标识符信息...\n")
author_id_mapping <- dbGetQuery(db_conn, "SELECT * FROM author_identifiers")

if(nrow(author_id_mapping) > 0) {
  # 创建符合bibliometrix格式的映射表
  author_id_mapping_bib <- data.frame(
    author_name = author_id_mapping$author_name,
    orcid = ifelse(author_id_mapping$identifier_type == "ORCID", 
                  author_id_mapping$identifier_value, NA),
    source = author_id_mapping$source,
    verification_status = author_id_mapping$verification_status,
    stringsAsFactors = FALSE
  )
  
  # 保存作者标识符映射
  write.csv(author_id_mapping_bib, "author_identifier_mapping.csv", row.names = FALSE, fileEncoding = "UTF-8")
}

# 汇总标识符覆盖统计
if(nrow(author_id_mapping) > 0) {
  identifier_stats <- data.frame(
    标识符来源 = c("Crossref ORCID", "OpenAlex作者ID"),
    作者数量 = c(
      sum(author_id_mapping$source == "Crossref", na.rm = TRUE),
      sum(author_id_mapping$source == "OpenAlex", na.rm = TRUE)
    )
  )
  
  print(identifier_stats)
}

#=========================
# 5. 应用作者消歧，保持bibliometrix数据格式
#=========================

# 应用标准化作者函数(包含标识符验证)，严格遵循bibliometrix格式
bibliometrix_disambiguate_authors <- function(authors, author_mapping) {
  # 初始检查
  if(is.null(authors) || is.na(authors) || authors == "") {
    return(list())
  }
  
  if(!is.data.frame(author_mapping) || nrow(author_mapping) == 0) {
    # 如果没有映射数据，也返回基本标准化结果
    result <- list()
    authors_list <- strsplit(authors, ";")[[1]]
    
    for(author in authors_list) {
      if(!is.na(author) && author != "") {
        # 按照bibliometrix格式保持原格式
        result[[length(result) + 1]] <- list(
          original = author,
          normalized = author,  # 保持原格式
          orcid = NA_character_,
          source = NA_character_,
          verification = "未经标识符验证"
        )
      }
    }
    return(result)
  }
  
  # 正常处理
  result <- list()
  authors_list <- strsplit(authors, ";")[[1]]
  
  for(author in authors_list) {
    if(is.na(author) || author == "") next
    
    # 查找匹配的标识符
    match_found <- FALSE
    
    # 直接比较
    exact_match_idx <- which(tolower(author_mapping$author_name) == tolower(author))
    if(length(exact_match_idx) > 0) {
      match_found <- TRUE
      id_info <- author_mapping[exact_match_idx[1], ]
    } else {
      # 模糊匹配，尝试去除标点和空格后比较
      clean_author <- gsub("[[:punct:][:space:]]", "", tolower(author))
      clean_mapping_names <- gsub("[[:punct:][:space:]]", "", tolower(author_mapping$author_name))
      fuzzy_match_idx <- which(clean_mapping_names == clean_author)
      
      if(length(fuzzy_match_idx) > 0) {
        match_found <- TRUE
        id_info <- author_mapping[fuzzy_match_idx[1], ]
      }
    }
    
    if(match_found) {
      # 找到匹配标识符
      orcid_val <- ifelse(is.na(id_info$orcid), NA_character_, as.character(id_info$orcid))
      source_val <- ifelse(is.na(id_info$source), NA_character_, as.character(id_info$source))
      verif_val <- ifelse(is.na(id_info$verification_status), "未经标识符验证", 
                         as.character(id_info$verification_status))
      
      result[[length(result) + 1]] <- list(
        original = author,
        normalized = author,  # 保持原格式，与bibliometrix一致
        orcid = orcid_val,
        source = source_val,
        verification = verif_val
      )
    } else {
      # 无标识符，保持原格式
      result[[length(result) + 1]] <- list(
        original = author,
        normalized = author,  # 保持原格式
        orcid = NA_character_,
        source = NA_character_,
        verification = "未经标识符验证"
      )
    }
  }
  
  return(result)
}

# 应用到样本文章以展示功能
sample_size <- min(5, nrow(wos_data))
if(sample_size > 0) {
  cat("对样本文章应用作者消歧...\n")
  sample_idx <- 1:sample_size
  sample_authors <- wos_data$AU[sample_idx]
  
  # 确保sample_authors不包含NA值
  sample_authors <- ifelse(is.na(sample_authors), "", sample_authors)
  
  disambiguated_samples <- lapply(sample_authors, function(authors) {
    bibliometrix_disambiguate_authors(authors, author_id_mapping_bib)
  })
  
  # 保存消歧样本结果
  sample_results <- data.frame(
    article_idx = integer(),
    original = character(),
    normalized = character(),
    orcid = character(),
    source = character(),
    verification = character(),
    stringsAsFactors = FALSE
  )
  
  for(i in seq_along(disambiguated_samples)) {
    author_set <- disambiguated_samples[[i]]
    if(length(author_set) > 0) {
      for(j in seq_along(author_set)) {
        author <- author_set[[j]]
        # 确保所有字段都有值
        sample_results <- rbind(sample_results, data.frame(
          article_idx = sample_idx[i],
          original = ifelse(is.null(author$original) || is.na(author$original), 
                          "", author$original),
          normalized = ifelse(is.null(author$normalized) || is.na(author$normalized), 
                            "", author$normalized),
          orcid = ifelse(is.null(author$orcid) || is.na(author$orcid), 
                        NA_character_, author$orcid),
          source = ifelse(is.null(author$source) || is.na(author$source), 
                         NA_character_, author$source),
          verification = ifelse(is.null(author$verification) || is.na(author$verification), 
                              "未经标识符验证", author$verification),
          stringsAsFactors = FALSE
        ))
      }
    }
  }
  
  if(nrow(sample_results) > 0) {
    write.csv(sample_results, "disambiguated_authors_sample.csv", row.names = FALSE, fileEncoding = "UTF-8")
    
    # 汇总状态统计
    if(length(unique(sample_results$verification)) > 0) {
      verification_stats <- table(sample_results$verification)
      cat("\n验证状态统计:\n")
      print(verification_stats)
    }
    
    # 将消歧结果保存到数据库
    if(!dbExistsTable(db_conn, "disambiguated_authors")) {
      dbWriteTable(db_conn, "disambiguated_authors", sample_results, row.names = FALSE)
    } else {
      dbExecute(db_conn, "DELETE FROM disambiguated_authors")
      dbAppendTable(db_conn, "disambiguated_authors", sample_results)
    }
    cat("作者消歧样本结果保存到数据库\n")
  }
}

# 尝试使用bibliometrix的原生作者分析功能
tryCatch({
  cat("\n执行bibliometrix原生作者分析...\n")
  authors_results <- biblioAnalysis(wos_data)
  auth_stats <- bibliometrix::authors(authors_results)
  cat("成功完成bibliometrix作者分析\n")
  
  # 保存bibliometrix标准分析结果
  write.csv(auth_stats, "bibliometrix_authors_stats.csv", row.names = FALSE, fileEncoding = "UTF-8")
  
  # 将bibliometrix标准分析结果保存到数据库
  auth_stats_df <- data.frame(
    author = names(auth_stats),
    frequency = as.numeric(auth_stats),
    stringsAsFactors = FALSE
  )
  
  if(!dbExistsTable(db_conn, "bibliometrix_author_stats")) {
    dbWriteTable(db_conn, "bibliometrix_author_stats", auth_stats_df, row.names = FALSE)
  } else {
    dbExecute(db_conn, "DELETE FROM bibliometrix_author_stats")
    dbAppendTable(db_conn, "bibliometrix_author_stats", auth_stats_df)
  }
  cat("Bibliometrix作者分析结果保存到数据库\n")
}, error = function(e) {
  cat("执行bibliometrix作者分析时出错:", e$message, "\n")
})

# 导出数据库中的所有表格为CSV文件
export_tables_to_csv <- function() {
  cat("导出所有数据库表为CSV文件...\n")
  tables <- dbListTables(db_conn)
  for(table in tables) {
    data <- dbReadTable(db_conn, table)
    if(nrow(data) > 0) {
      filename <- paste0(table, ".csv")
      write.csv(data, filename, row.names = FALSE, fileEncoding = "UTF-8")
      cat(sprintf("已导出表 %s 到文件 %s\n", table, filename))
    }
  }
}

# 执行导出
export_tables_to_csv()

# 关闭数据库连接
dbDisconnect(db_conn)
cat("数据库连接已关闭\n")

# 输出汇总信息
cat("\n作者消歧工作流程完成!\n")
cat("生成的文件:\n")
if(exists("author_id_mapping_bib") && is.data.frame(author_id_mapping_bib) && nrow(author_id_mapping_bib) > 0) {
  cat("1. author_identifier_mapping.csv - 作者标识符映射表\n")
}
if(exists("sample_results") && is.data.frame(sample_results) && nrow(sample_results) > 0) {
  cat("2. disambiguated_authors_sample.csv - 消歧处理样本结果\n")
}
if(file.exists("bibliometrix_authors_stats.csv")) {
  cat("3. bibliometrix_authors_stats.csv - bibliometrix原生作者分析结果\n")
}
cat("4. 本地数据库:", db_path, "- 包含所有API响应和处理结果\n")
cat("5. 已将所有数据库表导出为CSV文件\n")
