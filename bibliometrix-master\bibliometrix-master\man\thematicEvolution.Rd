% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/thematicEvolution.R
\name{thematicEvolution}
\alias{thematicEvolution}
\title{Perform a Thematic Evolution Analysis}
\usage{
thematicEvolution(
  M,
  field = "ID",
  years,
  n = 250,
  minFreq = 2,
  size = 0.5,
  ngrams = 1,
  stemming = FALSE,
  n.labels = 1,
  repel = TRUE,
  remove.terms = NULL,
  synonyms = NULL,
  cluster = "walktrap"
)
}
\arguments{
\item{M}{is a bibliographic data frame obtained by the converting function \code{\link{convert2df}}.}

\item{field}{is a character object. It indicates the content field to use. Field can be one of c=("ID","DE","TI","AB"). Default value is \code{field="ID"}.}

\item{years}{is a numeric vector of one or more unique cut points.}

\item{n}{is numerical. It indicates the number of words to use in the network analysis}

\item{minFreq}{is numerical. It indicates the min frequency of words included in to a cluster.}

\item{size}{is numerical. It indicates del size of the cluster circles and is a number in the range (0.01,1).}

\item{ngrams}{is an integer between 1 and 4. It indicates the type of n-gram to extract from texts. 
An n-gram is a contiguous sequence of n terms. The function can extract n-grams composed by 1, 2, 3 or 4 terms. Default value is \code{ngrams=1}.}

\item{stemming}{is logical. If it is TRUE the word (from titles or abstracts) will be stemmed (using the Porter's algorithm).}

\item{n.labels}{is integer. It indicates how many labels associate to each cluster. Default is \code{n.labels = 1}.}

\item{repel}{is logical. If it is TRUE ggplot uses geom_label_repel instead of geom_label.}

\item{remove.terms}{is a character vector. It contains a list of additional terms to delete from the documents before term extraction. The default is \code{remove.terms = NULL}.}

\item{synonyms}{is a character vector. Each element contains a list of synonyms, separated by ";",  that will be merged into a single term (the first word contained in the vector element). The default is \code{synonyms = NULL}.}

\item{cluster}{is a character. It indicates the type of cluster to perform among ("optimal", "louvain","leiden", "infomap","edge_betweenness","walktrap", "spinglass", "leading_eigen", "fast_greedy").}
}
\value{
a list containing:
\tabular{lll}{
\code{nets}\tab   \tab The thematic nexus graph for each comparison\cr
\code{incMatrix}\tab   \tab Some useful statistics about the thematic nexus}
}
\description{
It performs a Thematic Evolution Analysis based on co-word network analysis and clustering.
The methodology is inspired by the proposal of Cobo et al. (2011).
}
\details{
\code{\link{thematicEvolution}} starts from two or more thematic maps created by \code{\link{thematicMap}} function.\cr\cr

Reference:\cr
Cobo, M. J., Lopez-Herrera, A. G., Herrera-Viedma, E., & Herrera, F. (2011). An approach for detecting, quantifying, 
and visualizing the evolution of a research field: A practical application to the fuzzy sets theory field. Journal of Informetrics, 5(1), 146-166.\cr
}
\examples{

\dontrun{
data(managemeent, package = "bibliometrixData")
years=c(2004,2015)

nexus <- thematicEvolution(management,field="ID",years=years,n=100,minFreq=2)
}

}
\seealso{
\code{\link{thematicMap}} function to create a thematic map based on co-word network analysis and clustering.

\code{\link{cocMatrix}} to compute a bibliographic bipartite network.

\code{\link{networkPlot}} to plot a bibliographic network.
}
