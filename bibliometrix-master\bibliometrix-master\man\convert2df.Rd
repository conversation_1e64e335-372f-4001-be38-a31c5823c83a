% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/convert2df.R
\name{convert2df}
\alias{convert2df}
\title{Import and Convert bibliographic export files and API objects.}
\usage{
convert2df(
  file,
  dbsource = "wos",
  format = "plaintext",
  remove.duplicates = TRUE
)
}
\arguments{
\item{file}{a character array containing a sequence of filenames coming from WoS, Scopus, Dimensions, Lens.org, and Pubmed. Alternatively, \code{file} can be 
an object resulting from an API query fetched from Dimensions, PubMed or OpenAlex databases: 
\tabular{lll}{
a)\tab 'wos' \tab Clarivate Analytics WoS (in plaintext '.txt', Endnote Desktop '.ciw', or bibtex formats '.bib');\cr
b)\tab 'scopus' \tab SCOPUS (exclusively in bibtex format '.bib');\cr
c)\tab 'dimensions' \tab Digital Science Dimensions (in csv '.csv' or excel '.xlsx' formats);\cr
d)\tab 'lens' \tab Lens.org (in csv '.csv');\cr
e)\tab 'pubmed' \tab an object of the class \code{pubmedR (package pubmedR)} containing a collection obtained from a query performed with pubmedR package;\cr
f)\tab 'dimensions' \tab an object of the class \code{dimensionsR (package dimensionsR)} containing a collection obtained from a query performed with dimensionsR package;\cr
g)\tab 'openalex' \tab OpenAlex .csv file;\cr
h)\tab 'openalex_api' \tab a data frame object returned by openalexR package, containing a collection of works resulting from a query fetched from OpenAlex database.}}

\item{dbsource}{is a character indicating the bibliographic database. \code{dbsource} can be \code{dbsource = c('cochrane','dimensions','generic','isi','openalex', 'pubmed','scopus','wos', 'lens')} . Default is \code{dbsource = "isi"}.}

\item{format}{is a character indicating the SCOPUS, Clarivate Analytics WoS, and other databases export file format. \code{format} can be \code{c('api', 'bibtex', 'csv', 'endnote','excel','plaintext', 'pubmed')}. Default is \code{format = "plaintext"}.}

\item{remove.duplicates}{is logical. If TRUE, the function will remove duplicated items checking by DOI and database ID.}
}
\value{
a data frame with cases corresponding to articles and variables to Field Tags in the original export file.

I.e We have three files download from Web of Science in plaintext format, file will be:

file <- c("filename1.txt", "filename2.txt", "filename3.txt") 

data frame columns are named using the standard Clarivate Analytics WoS Field Tag codify. The main field tags are:

\tabular{lll}{
\code{AU}\tab   \tab Authors\cr
\code{TI}\tab   \tab Document Title\cr
\code{SO}\tab   \tab Publication Name (or Source)\cr
\code{JI}\tab   \tab ISO Source Abbreviation\cr
\code{DT}\tab   \tab Document Type\cr
\code{DE}\tab   \tab Authors' Keywords\cr
\code{ID}\tab   \tab Keywords associated by SCOPUS or WoS database \cr
\code{AB}\tab   \tab Abstract\cr
\code{C1}\tab   \tab Author Address\cr
\code{RP}\tab   \tab Reprint Address\cr
\code{CR}\tab   \tab Cited References\cr
\code{TC}\tab   \tab Times Cited\cr
\code{PY}\tab   \tab Year\cr
\code{SC}\tab   \tab Subject Category\cr
\code{UT}\tab   \tab Unique Article Identifier\cr
\code{DB}\tab   \tab Database\cr}

for a complete list of field tags see: \href{https://www.bibliometrix.org/documents/Field_Tags_bibliometrix.pdf}{Field Tags used in bibliometrix}
}
\description{
It converts a SCOPUS, Clarivate Analytics WoS, Dimensions, Lens.org, PubMed and COCHRANE Database export files or pubmedR and dimensionsR JSON/XML 
objects into a data frame, with cases corresponding to articles and variables to Field Tags as used in WoS.
}
\examples{

# Example:
# Import and convert a Web of Science collection form an export file in plaintext format:

\dontrun{
files <- 'https://www.bibliometrix.org/datasets/wos_plaintext.txt'

M <- convert2df(file = files, dbsource = 'wos', format = "plaintext")
}


}
