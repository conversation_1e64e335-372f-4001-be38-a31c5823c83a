# 研究内容

## A. 基础描述性与统计分析 (Summarization & Basic Metrics)

### 研究问题: 该领域文献产出的基本概况如何？ (Overall Publication Output Overview)

- **采用字段:** PY (出版年份), DT (文献类型), LA (语言), SO (来源期刊/书籍), DI (DOI，用于统计覆盖率)
- **如何计算:**
  - 统计不同年份 (PY) 的发文量，绘制年度发文趋势图。
  - 统计不同文献类型 (DT) 的分布（如期刊文章、会议论文、综述等）。
  - 统计文献的主要语言 (LA) 分布。
  - 统计发文量最多的期刊 (SO) Top N。
  - 统计具有DOI (DI) 的文献比例。
- **其意义:** 快速了解该领域的研究活跃度、主要成果形式、国际化程度（语言）、核心发表平台以及数据规范性（DOI覆盖率）。

### 研究问题: 该领域最高产的核心作者是谁？ (Most Productive Authors)

- **采用字段:** AU (作者), PY (出版年份 - 可选，用于动态展示)
- **如何计算:**
  - 统计每个作者 (AU) 在整个数据集中的出现频次（即发文量）。
  - 按发文量降序排列，识别Top N作者。
  - （可选）使用 bibliometrix::authorProdOverTime 可视化高产作者的年度发文量和累计发文量。
- **其意义:** 识别该领域在发文数量上的核心贡献者，了解研究人员的活跃度。注意：需要严格的作者姓名标准化来保证准确性。

### 研究问题: 该领域最具影响力的核心文献有哪些？ (Most Cited Documents)

- **采用字段:** TI (标题), AU (作者), PY (出版年份), TC (总被引次数 - 基于下载快照)
- **如何计算:**
  - 按 TC 字段降序排列文献。
  - 列出Top N篇高被引文献及其作者、年份、期刊等信息。
- **其意义:** 识别该领域内被广泛引用的奠基性或突破性研究成果，了解哪些工作对后续研究产生了重大影响（以数据下载时状态为准）。

## B. 概念结构分析 (Conceptual Structure)

### 研究问题: 该领域的核心研究主题是什么？它们之间如何关联？ (Core Research Themes & Relationships - Keyword Co-occurrence)

- **采用字段:** DE (作者关键词), ID (Keywords Plus)
- **如何计算:**
  - 提取并清洗关键词。
  - 构建关键词共现矩阵（统计多少文献同时包含某两个关键词）。
  - 使用网络分析算法（如Louvain聚类）对共现网络进行聚类，识别主题簇。
  - 可视化网络和聚类结果（如使用VOSviewer或 bibliometrix 内置绘图）。
- **其意义:** 揭示该领域的研究热点、主题结构、不同主题间的联系，理解领域的知识图景。

### 研究问题: 该领域的研究主题是如何随时间演变的？ (Thematic Evolution)

- **采用字段:** DE, ID, PY (出版年份)
- **如何计算:**
  - 将文献按时间 (PY) 分割成不同时期（如每3-5年一个窗口）。
  - 对每个时期的关键词共现网络进行聚类，识别该时期的主要主题。
  - 分析主题在不同时期之间的联系和流变（如使用 bibliometrix 的主题演化图或Sankey图）。
- **其意义:** 动态地展示领域研究焦点的变迁、新兴主题的出现和衰落主题的演变路径。

## C. 智力结构分析 (Intellectual Structure)

### 研究问题: 哪些文献构成了该领域的知识基础？（被共同引用的文献） (Intellectual Base - Document Co-citation Analysis, DCA)

- **采用字段:** CR (引用参考文献)
- **如何计算:**
  - 解析 CR 字段，提取被引文献信息（作者、年份、期刊等），并进行标准化和消歧。
  - 构建文献共被引矩阵（统计多少文献同时引用了某两篇参考文献）。
  - 对共被引网络进行聚类和可视化。
- **其意义:** 识别那些经常被研究者们一同引用的经典文献或文献群，它们代表了该领域公认的知识基础、理论来源或重要方法。高度依赖 CR 字段的解析质量。

### 研究问题: 哪些作者构成了该领域的知识基础？（被共同引用的作者） (Intellectual Base - Author Co-citation Analysis, ACA)

- **采用字段:** CR (引用参考文献)
- **如何计算:**
  - 从解析后的 CR 字段中提取被引作者。
  - 构建作者共被引矩阵（统计多少文献同时引用了某两位作者）。
  - 对作者共被引网络进行聚类和可视化。
- **其意义:** 识别在领域内具有奠基性影响力的学者，即使他们本人并未直接发表在当前的数据集中。

## D. 社会结构分析 (Social Structure)

### 研究问题: 该领域的核心合作网络和团队是怎样的？ (Author Collaboration Network)

- **采用字段:** AU (作者)
- **如何计算:**
  - 构建作者合作矩阵（统计某两位作者共同发表了多少篇文献）。
  - 对合作网络进行分析（如中心性计算、社区检测）和可视化。
- **其意义:** 揭示研究者之间的合作模式、识别核心合作团队、发现关键的连接者（桥梁作者）。依赖作者姓名标准化。

### 研究问题: 哪些机构之间存在紧密的合作关系？ (Institution Collaboration Network)

- **采用字段:** C1 (作者地址 - 需要解析并标准化机构)
- **如何计算:**
  - 对 C1 字段进行机构标准化。
  - 构建机构合作矩阵（统计某两个机构共同署名了多少篇文献）。
  - 对合作网络进行分析和可视化。
- **其意义:** 识别主要的机构间合作关系、区域性或国际性的合作中心。高度依赖机构标准化质量。

## E. 来源文献分析 (Source Analysis)

### 研究问题: 该领域的核心期刊是如何相互关联的？（期刊共被引分析） (Journal Co-citation Analysis)

- **采用字段:** CR (引用参考文献)
- **如何计算:**
  - 从解析后的 CR 字段中提取被引期刊名称。
  - 对提取的期刊名称进行标准化。
  - 构建期刊共被引矩阵（统计多少文献同时引用了某两种期刊）。
  - 对期刊共被引网络进行聚类和可视化。
- **其意义:** 揭示期刊之间的知识关联强度，识别出在领域内扮演相似知识来源角色的期刊群落，反映学科交叉和期刊间的知识流动。严重依赖 CR 字段的准确解析和期刊名称的标准化质量。

### 研究问题: 该领域文献的来源分布是否符合布拉德福定律？ (Bradford's Law Analysis)

- **采用字段:** SO (来源期刊/书籍)
- **如何计算:**
  - 统计每个来源 (SO) 的发文量。
  - 将来源按发文量降序排列。
  - 应用布拉德福定律的计算方法，将期刊划分为核心区、关联区和边缘区。
- **其意义:** 检验该领域文献是否集中发表在少数核心期刊上，评估文献的分散程度，识别出最重要的核心期刊集合。

## F. 作者影响力与合作动态分析 (Author Impact & Collaboration Dynamics)

### 研究问题: 高产作者的影响力如何？（例如，h指数、g指数） (Highly Productive Authors' Impact)

- **采用字段:** AU (作者), TC (总被引次数 - 基于下载快照), PY (出版年份 - 可能需要)
- **如何计算:**
  - 对作者姓名 (AU) 进行严格的标准化和消歧。
  - 对于每个作者，获取其发表的所有文献列表及其对应的 TC 值。
  - 计算该作者的h指数和g指数。
- **其意义:** 评估作者的学术影响力，结合发文量和被引次数，提供比单纯发文量更全面的评价。注意：h/g指数的计算非常依赖作者消歧的准确性和 TC 数据的可靠性。

### 研究问题: 该领域的合作模式（作者、机构、国家）是如何随时间变化的？ (Collaboration Dynamics Over Time)

- **采用字段:** AU, C1 (需解析机构和国家), PY (出版年份)
- **如何计算:**
  - 对作者、机构、国家进行标准化。
  - 将数据集按时间 (PY) 分割成多个时期。
  - 分别计算每个时期内的合作指标：
    - 平均每篇论文作者数。
    - 合作论文（作者数>1）的比例。
    - 国际合作论文（涉及多个国家）的比例。
    - 主要合作国家/机构对的变化。
  - 绘制这些指标随时间变化的趋势图。
- **其意义:** 了解该领域科研合作的发展趋势，是趋向于更广泛的合作还是更独立的研究，国际合作程度的变化等。

## G. 知识演化与前沿探测 (Knowledge Evolution & Frontier Detection)

### 研究问题: 该领域的研究前沿和热点是如何演变的？ (Research Frontiers & Hotspots Evolution)

- **采用字段:** DE, ID, PY, CR
- **如何计算:**
  - 分析不同时间段内的关键词和被引文献变化。
  - 识别新兴主题和热点。
  - 追踪热点主题的演变路径。
- **其意义:** 识别领域内的研究前沿和热点，了解研究方向的变化趋势。

### 研究问题: 该领域的知识流动和交叉是如何体现的？ (Knowledge Flow & Interdisciplinary Integration)

- **采用字段:** CR, SO, AU
- **如何计算:**
  - 分析文献的引用关系和期刊间的交叉引用。
  - 识别知识流动的路径和交叉学科的融合点。
- **其意义:** 了解领域内的知识流动和交叉，识别学科间的融合和创新点。