# =================================================================================================
# === 数据处理模块 ===
# =================================================================================================
# 版本: 1.0.0
# 描述: 负责文献数据的处理和标准化

# 加载必要的包
required_packages <- c(
  "tidyverse",
  "bibliometrix",
  "stringdist",
  "text2vec",
  "Matrix"
)

# 检查并安装缺失的包
missing_packages <- required_packages[!required_packages %in% installed.packages()[,"Package"]]
if (length(missing_packages) > 0) {
  install.packages(missing_packages)
}

# 加载所有必要的包
for (pkg in required_packages) {
  suppressPackageStartupMessages(library(pkg, character.only = TRUE))
}

# 数据处理主函数
process_data <- function(raw_data, source_type) {
  # 1. 数据标准化
  standardized_data <- standardize_data(raw_data)
  
  # 2. 数据清理
  cleaned_data <- clean_data(standardized_data)
  
  # 3. 数据验证
  validation_results <- validate_data(cleaned_data)
  
  # 4. 数据转换
  converted_data <- convert_data(cleaned_data, source_type)
  
  return(list(
    processed_data = converted_data,
    validation_results = validation_results
  ))
}

# 数据标准化
standardize_data <- function(data) {
  # 1. 字段标准化
  data <- standardize_fields(data)
  
  # 2. 数据类型转换
  data <- convert_data_types(data)
  
  # 3. 特殊字段处理
  data <- process_special_fields(data)
  
  return(data)
}

# 数据清理
clean_data <- function(data) {
  data %>%
    # 1. 处理缺失值
    mutate(across(where(is.character), ~ifelse(. == "null", NA, .))) %>%
    # 2. 统一分隔符
    mutate(across(where(is.character), ~gsub(";;", ";", .))) %>%
    # 3. 处理特殊字符
    mutate(across(where(is.character), ~gsub("[^[:alnum:][:space:];]", "", .)))
}

# 数据验证
validate_data <- function(data) {
  # 1. 检查必要字段
  required_fields <- c("AU", "TI", "SO", "PY", "TC", "CR")
  missing_fields <- setdiff(required_fields, names(data))
  
  # 2. 检查数据类型
  type_check <- sapply(data, class)
  
  # 3. 检查格式一致性
  format_check <- check_format_consistency(data)
  
  return(list(
    missing_fields = missing_fields,
    type_check = type_check,
    format_check = format_check
  ))
}

# 数据转换
convert_data <- function(data, source_type) {
  # 根据数据源类型进行相应的转换
  switch(source_type,
    "wos" = convert_wos_data(data),
    "scopus" = convert_scopus_data(data),
    "openalex" = convert_openalex_data(data),
    stop("Unsupported source type")
  )
}

# 导出函数
export_functions <- c(
  "process_data",
  "standardize_data",
  "clean_data",
  "validate_data",
  "convert_data"
) 