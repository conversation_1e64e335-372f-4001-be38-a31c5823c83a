#' 构建基线数据集的主脚本
#' @description 执行基线数据集的构建过程

# 加载必要的库
library(here)
library(yaml)

# 加载数据存储模块
source(here("R", "data", "storage.R"))

# 读取配置
config <- read_yaml(here("config", "config.yaml"))

# 构建基线数据集
cat("开始构建基线数据集...\n")

# 设置原始数据目录
raw_data_dir <- config$paths$data$raw

# 构建基线数据集
M <- build_baseline_dataset(raw_data_dir)

# 输出数据集信息
cat("\n基线数据集构建完成\n")
cat("记录数:", nrow(M), "\n")
cat("字段数:", ncol(M), "\n")
cat("字段名:", paste(names(M), collapse = ", "), "\n")

# 数据质量检查
cat("\n--- 数据质量检查 ---\n")

# 检查关键字段的填充情况
key_fields <- c("AU", "TI", "PY", "SO", "DE", "ID", "AB")
for (field in key_fields) {
  if (field %in% names(M)) {
    non_empty <- sum(!is.na(M[[field]]) & M[[field]] != "")
    fill_rate <- non_empty / nrow(M) * 100
    cat(sprintf("%s字段填充率: %.2f%% (%d/%d)\n", 
                field, fill_rate, non_empty, nrow(M)))
  }
}

# 检查年份分布
if ("PY" %in% names(M)) {
  year_dist <- table(M$PY)
  cat("\n年份分布:\n")
  print(year_dist)
}

# 检查重复记录
if ("UT" %in% names(M)) {
  duplicate_uts <- M$UT[duplicated(M$UT)]
  if (length(duplicate_uts) > 0) {
    cat("\n发现重复的UT:\n")
    print(duplicate_uts)
  } else {
    cat("\n未发现重复的UT\n")
  }
}

cat("\n基线数据集构建和信息保存完成\n") 