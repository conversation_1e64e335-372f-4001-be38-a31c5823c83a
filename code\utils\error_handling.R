# =================================================================================================
# === 错误处理工具函数 ===
# =================================================================================================
# 版本: 1.0.0
# 描述: 提供错误处理相关的工具函数

# 加载必要的包
required_packages <- c(
  "tidyverse",
  "futile.logger"
)

# 检查并安装缺失的包
missing_packages <- required_packages[!required_packages %in% installed.packages()[,"Package"]]
if (length(missing_packages) > 0) {
  install.packages(missing_packages)
}

# 加载所有必要的包
for (pkg in required_packages) {
  suppressPackageStartupMessages(library(pkg, character.only = TRUE))
}

# 初始化日志记录器
setup_logger <- function(log_file) {
  # 配置日志记录器
  flog.appender(appender.file(log_file))
  flog.threshold(INFO)
}

# 错误处理函数
handle_error <- function(expr, error_msg = "发生错误") {
  tryCatch({
    expr
  }, error = function(e) {
    # 记录错误
    flog.error("%s: %s", error_msg, e$message)
    # 返回错误信息
    return(list(
      success = FALSE,
      error = e$message
    ))
  })
}

# API错误处理
handle_api_error <- function(expr, api_name) {
  tryCatch({
    expr
  }, error = function(e) {
    # 记录API错误
    flog.error("API错误 (%s): %s", api_name, e$message)
    # 返回错误信息
    return(list(
      success = FALSE,
      error = e$message,
      api = api_name
    ))
  })
}

# 数据验证错误处理
handle_validation_error <- function(expr, validation_type) {
  tryCatch({
    expr
  }, error = function(e) {
    # 记录验证错误
    flog.error("验证错误 (%s): %s", validation_type, e$message)
    # 返回错误信息
    return(list(
      success = FALSE,
      error = e$message,
      validation_type = validation_type
    ))
  })
}

# 重试机制
retry_operation <- function(expr, max_retries = 3, delay = 1) {
  for (i in 1:max_retries) {
    result <- tryCatch({
      expr
      return(list(success = TRUE))
    }, error = function(e) {
      # 记录重试信息
      flog.warn("重试 %d/%d: %s", i, max_retries, e$message)
      # 等待指定时间
      Sys.sleep(delay)
      return(list(success = FALSE, error = e$message))
    })
    
    if (result$success) {
      return(result)
    }
  }
  
  # 所有重试都失败
  return(list(
    success = FALSE,
    error = "达到最大重试次数"
  ))
}

# 导出函数
export_functions <- c(
  "setup_logger",
  "handle_error",
  "handle_api_error",
  "handle_validation_error",
  "retry_operation"
) 