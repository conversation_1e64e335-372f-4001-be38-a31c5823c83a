% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/citations.R
\name{citations}
\alias{citations}
\title{Citation frequency distribution}
\usage{
citations(M, field = "article", sep = ";")
}
\arguments{
\item{M}{is a bibliographic data frame obtained by the converting function \code{\link{convert2df}}.
It is a data matrix with cases corresponding to manuscripts and variables to Field Tag in the original SCOPUS and Clarivate Analytics Web of Science file.}

\item{field}{is a character. It can be "article" or "author" to obtain frequency distribution of cited citations or cited authors (only first authors for WoS database) respectively. The default is \code{field = "article"}.}

\item{sep}{is the field separator character. This character separates citations in each string of CR column of the bibliographic data frame. The default is \code{sep = ";"}.}
}
\value{
an object of \code{class} "list"  containing the following components:

\tabular{lll}{
Cited \tab  \tab the most frequent cited manuscripts or authors\cr
Year \tab       \tab the publication year (only for cited article analysis)\cr
Source \tab      \tab the journal (only for cited article analysis)}
}
\description{
It calculates frequency distribution of citations.
}
\examples{
## EXAMPLE 1: Cited articles

data(scientometrics,package = "bibliometrixData")

CR <- citations(scientometrics, field = "article", sep = ";")

CR$Cited[1:10]
CR$Year[1:10]
CR$Source[1:10]

## EXAMPLE 2: Cited first authors

data(scientometrics)

CR <- citations(scientometrics, field = "author", sep = ";")

CR$Cited[1:10]

}
\seealso{
\code{\link{biblioAnalysis}} function for bibliometric analysis.

\code{\link{summary}} to obtain a summary of the results.

\code{\link{plot}} to draw some useful plots of the results.
}
