# 加载必要的包
if (!require("stringr")) install.packages("stringr")
if (!require("readr")) install.packages("readr")
if (!require("dplyr")) install.packages("dplyr")
if (!require("purrr")) install.packages("purrr")
if (!require("fs")) install.packages("fs")
if (!require("openxlsx")) install.packages("openxlsx")

library(stringr)
library(readr)
library(dplyr)
library(purrr)
library(fs)
library(openxlsx)

# 定义文件路径
wos_original_path <- "C:/Users/<USER>/Desktop/实验用数据/原始txt文件"
citespace_path <- "C:/Users/<USER>/Desktop/实验用数据/CiteSpace预处理文件"
output_path_with_new_fields <- "C:/Users/<USER>/Desktop/实验用数据/整合后文件_包含新增字段"
output_path_original_fields <- "C:/Users/<USER>/Desktop/实验用数据/整合后文件_仅原始字段"
log_dir <- "C:/Users/<USER>/Desktop/实验用数据/整合日志"

# 创建输出目录
dir.create(output_path_with_new_fields, recursive = TRUE, showWarnings = FALSE)
dir.create(output_path_original_fields, recursive = TRUE, showWarnings = FALSE)
dir.create(log_dir, recursive = TRUE, showWarnings = FALSE)

# 创建日志文件路径
log_file <- file.path(log_dir, "integration_log.txt")
excel_log_file <- file.path(log_dir, "integration_log.xlsx")
detailed_log_file <- file.path(log_dir, "detailed_operations_log.xlsx")
cat("", file = log_file) # 清空日志文件

# 初始化Excel日志
log_data <- data.frame(
  WosFile = character(),
  RecordID = character(),
  CitespaceFile = character(),
  ReplacedFields = character(),
  AddedFields = character(),
  Status = character(),
  ErrorMessage = character(),
  stringsAsFactors = FALSE
)

# 初始化详细操作日志
detailed_operations <- data.frame(
  FileID = integer(),          # 文件序号
  FileName = character(),      # 文件名
  RecordID = character(),      # 记录ID
  Operation = character(),     # 操作类型
  Field = character(),         # 字段
  OriginalContent = character(), # 原始内容（摘录）
  NewContent = character(),    # 新内容（摘录）
  Status = character(),        # 状态
  Notes = character(),         # 备注
  stringsAsFactors = FALSE
)

# 日志函数
log_message <- function(message, log_file) {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  log_entry <- paste0(timestamp, " - ", message)
  cat(log_entry, "\n", file = log_file, append = TRUE)
  cat(log_entry, "\n")
}

log_message("开始WoS与CiteSpace预处理文件整合过程", log_file)

#-------------------------------------------------------------------------------
# 辅助函数: 解析WoS格式文件为结构化数据 - 修复版本
#-------------------------------------------------------------------------------
parse_wos_file <- function(file_path) {
  # 读取文件内容
  tryCatch({
    content <- readLines(file_path, warn = FALSE, encoding = "UTF-8")
  }, error = function(e) {
    # 尝试使用不同编码
    content <- readLines(file_path, warn = FALSE, encoding = "latin1")
  })
  
  # 初始化变量
  records <- list()
  record_lines <- list()
  current_record <- list()
  in_record <- FALSE
  start_line <- NULL
  
  # 逐行处理文件
  for (i in 1:length(content)) {
    line <- content[i]
    
    # 记录开始标志
    if (startsWith(line, "PT ")) {
      if (in_record && !is.null(start_line)) {
        # 保存之前的记录
        records <- c(records, list(current_record))
        record_lines <- c(record_lines, list(c(start_line, i-1)))
      }
      # 开始新记录
      current_record <- list()
      in_record <- TRUE
      start_line <- i
      
      # 添加PT字段
      field_content <- substr(line, 4, nchar(line))
      current_record[["PT"]] <- field_content
    }
    # 记录结束标志
    else if (line == "ER" && in_record) {
      # 结束当前记录并保存
      records <- c(records, list(current_record))
      record_lines <- c(record_lines, list(c(start_line, i)))
      in_record <- FALSE
      start_line <- NULL
    }
    # 处理字段行
    else if (in_record) {
      # 如果是新字段标识行
      if (grepl("^[A-Z][A-Z0-9] ", line)) {
        field_tag <- substr(line, 1, 2)
        field_content <- substr(line, 4, nchar(line))
        
        # 如果字段已存在，说明是多行字段（如CR）
        if (field_tag %in% names(current_record)) {
          if (is.character(current_record[[field_tag]])) {
            # 如果当前是单个字符串，转为向量
            current_record[[field_tag]] <- c(current_record[[field_tag]], field_content)
          } else {
            # 已经是向量，直接添加
            current_record[[field_tag]] <- c(current_record[[field_tag]], field_content)
          }
        } else {
          # 新字段
          current_record[[field_tag]] <- field_content
        }
      } else if (length(names(current_record)) > 0) {
        # 继续前一个字段的内容
        last_field <- names(current_record)[length(names(current_record))]
        if (!is.null(last_field)) {
          # 更新最后一个字段的内容
          if (is.character(current_record[[last_field]])) {
            if (length(current_record[[last_field]]) == 1) {
              # 单行字段，添加换行符
              current_record[[last_field]] <- paste0(current_record[[last_field]], "\n", line)
            } else {
              # 多行字段，更新最后一行
              last_idx <- length(current_record[[last_field]])
              current_record[[last_field]][last_idx] <- paste0(
                current_record[[last_field]][last_idx], "\n", line)
            }
          }
        }
      }
    }
  }
  
  # 检查最后一个记录是否已添加（如果文件不是以ER结尾）
  if (in_record && !is.null(start_line) && length(current_record) > 0) {
    records <- c(records, list(current_record))
    record_lines <- c(record_lines, list(c(start_line, length(content))))
  }
  
  # 返回解析结果和记录行范围
  return(list(records = records, record_lines = record_lines, content = content))
}

#-------------------------------------------------------------------------------
# 简化的示例测试函数 - 用于测试文件解析
#-------------------------------------------------------------------------------
test_file_parsing <- function(file_path) {
  tryCatch({
    parsed <- parse_wos_file(file_path)
    return(list(
      success = TRUE, 
      record_count = length(parsed$records),
      first_record_fields = if(length(parsed$records) > 0) names(parsed$records[[1]]) else NULL
    ))
  }, error = function(e) {
    return(list(
      success = FALSE,
      error = e$message
    ))
  })
}

#-------------------------------------------------------------------------------
# 辅助函数: 提取唯一标识以匹配记录
#-------------------------------------------------------------------------------
extract_record_id <- function(record) {
  # 创建多个可能的标识符
  identifiers <- list()
  
  # 尝试使用UT字段（WoS唯一标识符）
  if ("UT" %in% names(record) && length(record$UT) > 0) {
    # 提取WOS:后面的部分
    ut_value <- record$UT[1]
    ut_clean <- gsub(".*WOS:", "", ut_value)
    ut_clean <- gsub(".*:", "", ut_clean)  # 更宽松的匹配
    identifiers$ut <- ut_clean
  }
  
  # 尝试使用TI+PY组合
  if (all(c("TI", "PY") %in% names(record)) && 
      length(record$TI) > 0 && length(record$PY) > 0) {
    # 标题清理 - 移除所有非字母数字字符，转为小写
    title_clean <- tolower(gsub("[^a-zA-Z0-9]", "", record$TI[1]))
    # 只使用前30个字符
    title_part <- substr(title_clean, 1, min(30, nchar(title_clean)))
    identifiers$ti_py <- paste0(title_part, "_", record$PY[1])
  }
  
  # 尝试使用AU+PY组合
  if (all(c("AU", "PY") %in% names(record)) && 
      length(record$AU) > 0 && length(record$PY) > 0) {
    # 作者清理 - 仅使用第一个作者，移除所有非字母字符
    author_clean <- tolower(gsub("[^a-zA-Z]", "", record$AU[1]))
    identifiers$au_py <- paste0(author_clean, "_", record$PY[1])
  }
  
  # 尝试使用AU+SO+PY组合
  if (all(c("AU", "SO", "PY") %in% names(record)) && 
      length(record$AU) > 0 && length(record$SO) > 0 && length(record$PY) > 0) {
    # 作者和期刊清理
    author_clean <- tolower(gsub("[^a-zA-Z]", "", record$AU[1]))
    journal_clean <- tolower(gsub("[^a-zA-Z]", "", record$SO[1]))
    journal_part <- substr(journal_clean, 1, min(15, nchar(journal_clean)))
    identifiers$au_so_py <- paste0(author_clean, "_", journal_part, "_", record$PY[1])
  }
  
  # 返回所有标识符
  return(identifiers)
}

#-------------------------------------------------------------------------------
# 辅助函数: 比较两个记录ID对象，判断是否匹配
#-------------------------------------------------------------------------------
match_record_ids <- function(id1, id2) {
  # 如果两者都有UT字段且匹配，这是最准确的
  if (!is.null(id1$ut) && !is.null(id2$ut) && id1$ut == id2$ut) {
    return(TRUE)
  }
  
  # 检查标题+年份
  if (!is.null(id1$ti_py) && !is.null(id2$ti_py) && id1$ti_py == id2$ti_py) {
    return(TRUE)
  }
  
  # 检查作者+年份
  if (!is.null(id1$au_py) && !is.null(id2$au_py) && id1$au_py == id2$au_py) {
    return(TRUE)
  }
  
  # 检查作者+期刊+年份
  if (!is.null(id1$au_so_py) && !is.null(id2$au_so_py) && id1$au_so_py == id2$au_so_py) {
    return(TRUE)
  }
  
  # 没有找到匹配
  return(FALSE)
}

#-------------------------------------------------------------------------------
# 辅助函数: 获取字段在原始记录中的行范围
#-------------------------------------------------------------------------------
get_field_lines <- function(content, record_start, record_end, field_tag) {
  field_lines <- c()
  field_open <- FALSE
  
  for (i in record_start:record_end) {
    line <- content[i]
    
    # 检查字段开始
    if (startsWith(line, paste0(field_tag, " "))) {
      field_open <- TRUE
      field_lines <- c(field_lines, i)
    } 
    # 检查字段结束 (新字段开始或记录结束)
    else if (field_open && (grepl("^[A-Z][A-Z0-9] ", line) || line == "ER")) {
      field_open <- FALSE
      break
    }
    # 继续当前字段
    else if (field_open) {
      field_lines <- c(field_lines, i)
    }
  }
  
  return(field_lines)
}

#-------------------------------------------------------------------------------
# 辅助函数: 安全地截取内容摘录
#-------------------------------------------------------------------------------
safe_excerpt <- function(content, max_length = 50) {
  if (is.null(content) || length(content) == 0) {
    return("[空]")
  }
  
  if (is.character(content)) {
    if (length(content) == 1) {
      # 单行内容
      if (nchar(content) > max_length) {
        return(paste0(substr(content, 1, max_length), "..."))
      } else {
        return(content)
      }
    } else {
      # 多行内容
      return(paste0(substr(content[1], 1, max_length), "... [", length(content), "行]"))
    }
  }
  
  return("[无效内容]")
}

#-------------------------------------------------------------------------------
# 辅助函数: 处理单个WoS文件整合
#-------------------------------------------------------------------------------
integrate_file <- function(wos_file, citespace_parsed_files, output_dir_with_new, output_dir_original, 
                        log_file, log_data, operations_log, file_id) {
  wos_filename <- basename(wos_file)
  log_message(paste("处理WoS文件:", wos_filename), log_file)
  
  # 用于跟踪本文件的操作
  file_operations <- data.frame(
    FileID = integer(),
    FileName = character(),
    RecordID = character(),
    Operation = character(),
    Field = character(),
    OriginalContent = character(),
    NewContent = character(),
    Status = character(),
    Notes = character(),
    stringsAsFactors = FALSE
  )
  
  # 先测试解析
  test_result <- test_file_parsing(wos_file)
  
  if (!test_result$success) {
    log_message(paste("  错误解析WoS文件:", wos_filename, "-", test_result$error), log_file)
    
    # 添加到日志
    log_data <- rbind(log_data, data.frame(
      WosFile = wos_filename,
      RecordID = "N/A",
      CitespaceFile = "N/A",
      ReplacedFields = "N/A",
      AddedFields = "N/A",
      Status = "错误",
      ErrorMessage = paste("解析错误:", test_result$error),
      stringsAsFactors = FALSE
    ))
    
    # 添加到操作日志
    file_operations <- rbind(file_operations, data.frame(
      FileID = file_id,
      FileName = wos_filename,
      RecordID = "N/A",
      Operation = "解析",
      Field = "全部",
      OriginalContent = "N/A",
      NewContent = "N/A",
      Status = "失败",
      Notes = test_result$error,
      stringsAsFactors = FALSE
    ))
    
    # 简单复制原始文件到两个输出目录
    file.copy(wos_file, file.path(output_dir_with_new, wos_filename), overwrite = TRUE)
    file.copy(wos_file, file.path(output_dir_original, wos_filename), overwrite = TRUE)
    log_message(paste("  已复制原始文件到输出目录:", wos_filename), log_file)
    
    return(list(log_data = log_data, operations_log = rbind(operations_log, file_operations)))
  }
  
  # 解析WoS文件
  wos_parsed <- parse_wos_file(wos_file)
  
  if (length(wos_parsed$records) == 0) {
    log_message(paste("  无法找到记录:", wos_filename), log_file)
    
    # 添加到日志
    log_data <- rbind(log_data, data.frame(
      WosFile = wos_filename,
      RecordID = "N/A",
      CitespaceFile = "N/A",
      ReplacedFields = "N/A",
      AddedFields = "N/A",
      Status = "错误",
      ErrorMessage = "文件中没有有效记录",
      stringsAsFactors = FALSE
    ))
    
    # 添加到操作日志
    file_operations <- rbind(file_operations, data.frame(
      FileID = file_id,
      FileName = wos_filename,
      RecordID = "N/A",
      Operation = "解析",
      Field = "全部",
      OriginalContent = "N/A",
      NewContent = "N/A",
      Status = "失败",
      Notes = "文件中没有有效记录",
      stringsAsFactors = FALSE
    ))
    
    # 简单复制原始文件到两个输出目录
    file.copy(wos_file, file.path(output_dir_with_new, wos_filename), overwrite = TRUE)
    file.copy(wos_file, file.path(output_dir_original, wos_filename), overwrite = TRUE)
    log_message(paste("  已复制原始文件到输出目录:", wos_filename), log_file)
    
    return(list(log_data = log_data, operations_log = rbind(operations_log, file_operations)))
  }
  
  log_message(paste("  成功解析WoS文件，找到", length(wos_parsed$records), "条记录"), log_file)
  
  # 添加到操作日志
  file_operations <- rbind(file_operations, data.frame(
    FileID = file_id,
    FileName = wos_filename,
    RecordID = "N/A",
    Operation = "解析",
    Field = "全部",
    OriginalContent = "N/A",
    NewContent = "N/A",
    Status = "成功",
    Notes = paste("找到", length(wos_parsed$records), "条记录"),
    stringsAsFactors = FALSE
  ))
  
  # 准备两个版本的新内容
  new_content_with_new_fields <- wos_parsed$content
  new_content_original_fields <- wos_parsed$content
  modified_records <- 0
  
  # 对原文件中的每条记录进行处理
  for (i in 1:length(wos_parsed$records)) {
    wos_record <- wos_parsed$records[[i]]
    record_ids <- extract_record_id(wos_record)
    
    # 如果无法提取有效ID，跳过此记录
    if (length(record_ids) == 0) {
      log_message(paste("  无法为记录", i, "提取有效ID，跳过"), log_file)
      
      # 添加到操作日志
      file_operations <- rbind(file_operations, data.frame(
        FileID = file_id,
        FileName = wos_filename,
        RecordID = paste("记录", i),
        Operation = "ID提取",
        Field = "多字段",
        OriginalContent = "N/A",
        NewContent = "N/A",
        Status = "失败",
        Notes = "无法提取有效ID",
        stringsAsFactors = FALSE
      ))
      
      next
    }
    
    record_lines <- wos_parsed$record_lines[[i]]
    
    # 检查record_lines是否有效
    if (length(record_lines) != 2) {
      log_message(paste("  记录", i, "的行范围无效，跳过"), log_file)
      
      # 添加到操作日志
      file_operations <- rbind(file_operations, data.frame(
        FileID = file_id,
        FileName = wos_filename,
        RecordID = ifelse(!is.null(record_ids$ut), record_ids$ut, 
                     ifelse(!is.null(record_ids$ti_py), record_ids$ti_py, 
                       ifelse(!is.null(record_ids$au_py), record_ids$au_py, "记录ID未知"))),
        Operation = "位置确定",
        Field = "全部",
        OriginalContent = "N/A",
        NewContent = "N/A",
        Status = "失败",
        Notes = "行范围无效",
        stringsAsFactors = FALSE
      ))
      
      next
    }
    
    record_start <- record_lines[1]
    record_end <- record_lines[2]
    
    # 准备日志条目
    record_id_str <- ifelse(!is.null(record_ids$ut), record_ids$ut, 
                      ifelse(!is.null(record_ids$ti_py), record_ids$ti_py, 
                        ifelse(!is.null(record_ids$au_py), record_ids$au_py, "记录ID未知")))
    
    log_entry <- list(
      WosFile = wos_filename,
      RecordID = record_id_str,
      CitespaceFile = "未找到匹配",
      ReplacedFields = "",
      AddedFields = "",
      Status = "未处理",
      ErrorMessage = ""
    )
    
    # 在所有CiteSpace文件中查找匹配记录
    found_match <- FALSE
    matching_citespace_file <- NULL
    matching_citespace_record <- NULL
    
    for (cs_file_info in citespace_parsed_files) {
      if (is.null(cs_file_info) || !cs_file_info$success) {
        next
      }
      
      cs_filename <- cs_file_info$filename
      cs_parsed <- cs_file_info$parsed
      
      for (j in 1:length(cs_parsed$records)) {
        cs_record <- cs_parsed$records[[j]]
        cs_record_ids <- extract_record_id(cs_record)
        
        if (length(cs_record_ids) > 0 && match_record_ids(record_ids, cs_record_ids)) {
          found_match <- TRUE
          matching_citespace_file <- cs_filename
          matching_citespace_record <- cs_record
          break
        }
      }
      
      if (found_match) break
    }
    
    # 更新日志条目
    log_entry$CitespaceFile <- ifelse(found_match, matching_citespace_file, "未找到匹配")
    
    if (!found_match) {
      log_message(paste("  未找到匹配的CiteSpace记录: Record", i, "in", wos_filename), log_file)
      log_entry$Status <- "跳过"
      log_entry$ErrorMessage <- "未找到匹配的CiteSpace记录"
      
      # 添加到日志数据框
      log_data <- rbind(log_data, as.data.frame(log_entry, stringsAsFactors = FALSE))
      
      # 添加到操作日志
      file_operations <- rbind(file_operations, data.frame(
        FileID = file_id,
        FileName = wos_filename,
        RecordID = record_id_str,
        Operation = "匹配CiteSpace记录",
        Field = "全部",
        OriginalContent = "N/A",
        NewContent = "N/A",
        Status = "失败",
        Notes = "未找到匹配的CiteSpace记录",
        stringsAsFactors = FALSE
      ))
      
      next
    }
    
    # 开始处理匹配的记录
    log_message(paste("  找到匹配: WoS记录", i, "匹配到", matching_citespace_file), log_file)
    
    # 添加到操作日志
    file_operations <- rbind(file_operations, data.frame(
      FileID = file_id,
      FileName = wos_filename,
      RecordID = record_id_str,
      Operation = "匹配CiteSpace记录",
      Field = "全部",
      OriginalContent = "N/A",
      NewContent = "N/A",
      Status = "成功",
      Notes = paste("匹配到", matching_citespace_file),
      stringsAsFactors = FALSE
    ))
    
    replaced_fields <- c()
    added_fields <- c()
    
    tryCatch({
      #-------------------------------------------------------------------------
      # 替换高优先级字段 (必须使用CiteSpace预处理的字段)
      #-------------------------------------------------------------------------
      high_priority_fields <- c("CR", "AB", "TI")
      
      for (field in high_priority_fields) {
        if (field %in% names(matching_citespace_record)) {
          # 找到字段在原始记录中的行位置
          field_lines <- get_field_lines(wos_parsed$content, record_start, record_end, field)
          
          if (length(field_lines) > 0) {
            # 获取原始内容摘录
            original_content <- ifelse(length(field_lines) > 0, 
                                   safe_excerpt(wos_parsed$content[field_lines[1]]), 
                                   "[空]")
            
            # 准备新的字段内容
            citespace_content <- matching_citespace_record[[field]]
            new_field_lines <- character(0)
            
            # 处理字段内容
            if (is.character(citespace_content)) {
              if (length(citespace_content) == 1) {
                # 单行内容
                new_field_lines <- c(new_field_lines, paste0(field, " ", citespace_content))
              } else {
                # 多行内容
                new_field_lines <- c(new_field_lines, paste0(field, " ", citespace_content[1]))
                if (length(citespace_content) > 1) {
                  new_field_lines <- c(new_field_lines, citespace_content[-1])
                }
              }
            }
            
            # 获取新内容摘录
            new_content_excerpt <- safe_excerpt(new_field_lines[1])
            
            # 替换原始内容 - 两个版本都要替换
            for (j in 1:length(field_lines)) {
              line_index <- field_lines[j]
              if (j <= length(new_field_lines)) {
                new_content_with_new_fields[line_index] <- new_field_lines[j]
                new_content_original_fields[line_index] <- new_field_lines[j]
              } else {
                # 如果原始字段有更多行，标记为删除
                new_content_with_new_fields[line_index] <- "<<DELETE_LINE>>"
                new_content_original_fields[line_index] <- "<<DELETE_LINE>>"
              }
            }
            
            # 如果有更多新内容行，需要插入
            if (length(new_field_lines) > length(field_lines)) {
              last_line <- field_lines[length(field_lines)]
              insert_position <- last_line
              
              for (j in (length(field_lines) + 1):length(new_field_lines)) {
                insert_position <- insert_position + 1
                # 在当前位置插入新行 - 两个版本都插入
                new_content_with_new_fields <- c(
                  new_content_with_new_fields[1:insert_position-1],
                  new_field_lines[j],
                  new_content_with_new_fields[insert_position:length(new_content_with_new_fields)]
                )
                
                new_content_original_fields <- c(
                  new_content_original_fields[1:insert_position-1],
                  new_field_lines[j],
                  new_content_original_fields[insert_position:length(new_content_original_fields)]
                )
                
                # 更新后续行的索引
                record_end <- record_end + 1
              }
            }
            
            replaced_fields <- c(replaced_fields, field)
            log_message(paste("    替换字段:", field), log_file)
            
            # 添加到操作日志
            file_operations <- rbind(file_operations, data.frame(
              FileID = file_id,
              FileName = wos_filename,
              RecordID = record_id_str,
              Operation = "替换",
              Field = field,
              OriginalContent = original_content,
              NewContent = new_content_excerpt,
              Status = "成功",
              Notes = ifelse(length(new_field_lines) > length(field_lines), 
                         paste("新内容有", length(new_field_lines), "行，原内容有", length(field_lines), "行"),
                         ""),
              stringsAsFactors = FALSE
            ))
            
          } else {
            # 如果原始记录中不存在此字段，在记录结束前添加
            new_field_lines <- character(0)
            
            # 准备字段内容
            if (is.character(matching_citespace_record[[field]])) {
              if (length(matching_citespace_record[[field]]) == 1) {
                # 单行内容
                new_field_lines <- c(new_field_lines, 
                                    paste0(field, " ", matching_citespace_record[[field]]))
              } else {
                # 多行内容
                new_field_lines <- c(new_field_lines, 
                                    paste0(field, " ", matching_citespace_record[[field]][1]))
                if (length(matching_citespace_record[[field]]) > 1) {
                  new_field_lines <- c(new_field_lines, matching_citespace_record[[field]][-1])
                }
              }
            }
            
            # 获取新内容摘录
            new_content_excerpt <- safe_excerpt(new_field_lines[1])
            
            # 在ER行前插入 - 仅第一个版本插入新字段
            er_position <- record_end
            
            # 插入新字段 - 仅新字段版本插入
            new_content_with_new_fields <- c(
              new_content_with_new_fields[1:(er_position-1)],
              new_field_lines,
              new_content_with_new_fields[er_position:length(new_content_with_new_fields)]
            )
            
            # 更新记录结束位置
            record_end <- record_end + length(new_field_lines)
            
            added_fields <- c(added_fields, field)
            log_message(paste("    添加新字段:", field), log_file)
            
            # 添加到操作日志
            file_operations <- rbind(file_operations, data.frame(
              FileID = file_id,
              FileName = wos_filename,
              RecordID = record_id_str,
              Operation = "添加",
              Field = field,
              OriginalContent = "[不存在]",
              NewContent = new_content_excerpt,
              Status = "成功",
              Notes = paste("添加了", length(new_field_lines), "行"),
              stringsAsFactors = FALSE
            ))
          }
        }
      }
      
      #-------------------------------------------------------------------------
      # 替换中优先级字段 (优先使用CiteSpace内容的字段)
      #-------------------------------------------------------------------------
      medium_priority_fields <- c("DE", "ID", "WC", "SC")
      
      for (field in medium_priority_fields) {
        if (field %in% names(matching_citespace_record)) {
          # 处理逻辑与高优先级字段相似
          field_lines <- get_field_lines(wos_parsed$content, record_start, record_end, field)
          
          if (length(field_lines) > 0) {
            # 获取原始内容摘录
            original_content <- ifelse(length(field_lines) > 0, 
                                   safe_excerpt(wos_parsed$content[field_lines[1]]), 
                                   "[空]")
            
            # 准备新的字段内容
            citespace_content <- matching_citespace_record[[field]]
            new_field_lines <- character(0)
            
            # 处理字段内容
            if (is.character(citespace_content)) {
              if (length(citespace_content) == 1) {
                # 单行内容
                new_field_lines <- c(new_field_lines, paste0(field, " ", citespace_content))
              } else {
                # 多行内容
                new_field_lines <- c(new_field_lines, paste0(field, " ", citespace_content[1]))
                if (length(citespace_content) > 1) {
                  new_field_lines <- c(new_field_lines, citespace_content[-1])
                }
              }
            }
            
            # 获取新内容摘录
            new_content_excerpt <- safe_excerpt(new_field_lines[1])
            
            # 替换原始内容 - 两个版本都要替换
            for (j in 1:length(field_lines)) {
              line_index <- field_lines[j]
              if (j <= length(new_field_lines)) {
                new_content_with_new_fields[line_index] <- new_field_lines[j]
                new_content_original_fields[line_index] <- new_field_lines[j]
              } else {
                # 如果原始字段有更多行，标记为删除
                new_content_with_new_fields[line_index] <- "<<DELETE_LINE>>"
                new_content_original_fields[line_index] <- "<<DELETE_LINE>>"
              }
            }
            
            # 如果有更多新内容行，需要插入
            if (length(new_field_lines) > length(field_lines)) {
              last_line <- field_lines[length(field_lines)]
              insert_position <- last_line
              
              for (j in (length(field_lines) + 1):length(new_field_lines)) {
                insert_position <- insert_position + 1
                # 在当前位置插入新行 - 两个版本都插入
                new_content_with_new_fields <- c(
                  new_content_with_new_fields[1:insert_position-1],
                  new_field_lines[j],
                  new_content_with_new_fields[insert_position:length(new_content_with_new_fields)]
                )
                
                new_content_original_fields <- c(
                  new_content_original_fields[1:insert_position-1],
                  new_field_lines[j],
                  new_content_original_fields[insert_position:length(new_content_original_fields)]
                )
                
                # 更新后续行的索引
                record_end <- record_end + 1
              }
            }
            
            replaced_fields <- c(replaced_fields, field)
            log_message(paste("    替换字段:", field), log_file)
            
            # 添加到操作日志
            file_operations <- rbind(file_operations, data.frame(
              FileID = file_id,
              FileName = wos_filename,
              RecordID = record_id_str,
              Operation = "替换",
              Field = field,
              OriginalContent = original_content,
              NewContent = new_content_excerpt,
              Status = "成功",
              Notes = ifelse(length(new_field_lines) > length(field_lines), 
                         paste("新内容有", length(new_field_lines), "行，原内容有", length(field_lines), "行"),
                         ""),
              stringsAsFactors = FALSE
            ))
            
          } else {
            # 如果原始记录中不存在此字段，在记录结束前添加
            new_field_lines <- character(0)
            
            # 准备字段内容
            if (is.character(matching_citespace_record[[field]])) {
              if (length(matching_citespace_record[[field]]) == 1) {
                # 单行内容
                new_field_lines <- c(new_field_lines, 
                                    paste0(field, " ", matching_citespace_record[[field]]))
              } else {
                # 多行内容
                new_field_lines <- c(new_field_lines, 
                                    paste0(field, " ", matching_citespace_record[[field]][1]))
                if (length(matching_citespace_record[[field]]) > 1) {
                  new_field_lines <- c(new_field_lines, matching_citespace_record[[field]][-1])
                }
              }
            }
            
            # 获取新内容摘录
            new_content_excerpt <- safe_excerpt(new_field_lines[1])
            
            # 在ER行前插入 - 仅第一个版本插入新字段
            er_position <- record_end
            
            # 插入新字段 - 仅新字段版本插入
            new_content_with_new_fields <- c(
              new_content_with_new_fields[1:(er_position-1)],
              new_field_lines,
              new_content_with_new_fields[er_position:length(new_content_with_new_fields)]
            )
            
            # 更新记录结束位置
            record_end <- record_end + length(new_field_lines)
            
            added_fields <- c(added_fields, field)
            log_message(paste("    添加新字段:", field), log_file)
            
            # 添加到操作日志
            file_operations <- rbind(file_operations, data.frame(
              FileID = file_id,
              FileName = wos_filename,
              RecordID = record_id_str,
              Operation = "添加",
              Field = field,
              OriginalContent = "[不存在]",
              NewContent = new_content_excerpt,
              Status = "成功",
              Notes = paste("添加了", length(new_field_lines), "行"),
              stringsAsFactors = FALSE
            ))
          }
        }
      }
      
      #-------------------------------------------------------------------------
      # 添加CiteSpace独有字段 - 仅第一个版本添加
      #-------------------------------------------------------------------------
      # 获取CiteSpace记录中的所有字段
      citespace_fields <- names(matching_citespace_record)
      
      # 获取WoS记录中的所有字段
      wos_fields <- names(wos_record)
      
      # 确定CiteSpace独有的字段
      unique_fields <- setdiff(citespace_fields, wos_fields)
      
      # 排除上面已处理的高优先级和中优先级字段
      unique_fields <- setdiff(unique_fields, c(high_priority_fields, medium_priority_fields))
      
      if (length(unique_fields) > 0) {
        # 记录需要添加的字段
        log_message(paste("    添加CiteSpace独有字段:", paste(unique_fields, collapse=", ")), log_file)
        
        # 为每个独有字段创建新行
        new_field_lines <- character(0)
        
        for (field in unique_fields) {
          field_content <- matching_citespace_record[[field]]
          
          if (is.character(field_content)) {
            if (length(field_content) == 1) {
              # 单行内容
              field_line <- paste0(field, " ", field_content)
              new_field_lines <- c(new_field_lines, field_line)
              
              # 添加到操作日志
              file_operations <- rbind(file_operations, data.frame(
                FileID = file_id,
                FileName = wos_filename,
                RecordID = record_id_str,
                Operation = "添加",
                Field = field,
                OriginalContent = "[不存在]",
                NewContent = safe_excerpt(field_line),
                Status = "成功",
                Notes = "CiteSpace独有字段",
                stringsAsFactors = FALSE
              ))
              
            } else {
              # 多行内容
              first_line <- paste0(field, " ", field_content[1])
              new_field_lines <- c(new_field_lines, first_line)
              
              if (length(field_content) > 1) {
                new_field_lines <- c(new_field_lines, field_content[-1])
              }
              
              # 添加到操作日志
              file_operations <- rbind(file_operations, data.frame(
                FileID = file_id,
                FileName = wos_filename,
                RecordID = record_id_str,
                Operation = "添加",
                Field = field,
                OriginalContent = "[不存在]",
                NewContent = safe_excerpt(first_line),
                Status = "成功",
                Notes = paste("CiteSpace独有字段，添加了", length(field_content), "行"),
                stringsAsFactors = FALSE
              ))
            }
          }
        }
        
        # 在ER行前插入 - 仅新字段版本插入
        er_position <- record_end
        
        # 插入新字段
        if (length(new_field_lines) > 0) {
          new_content_with_new_fields <- c(
            new_content_with_new_fields[1:(er_position-1)],
            new_field_lines,
            new_content_with_new_fields[er_position:length(new_content_with_new_fields)]
          )
        }
        
        added_fields <- c(added_fields, unique_fields)
      }
      
      # 更新日志条目
      log_entry$ReplacedFields <- paste(replaced_fields, collapse=", ")
      log_entry$AddedFields <- paste(added_fields, collapse=", ")
      log_entry$Status <- "成功"
      
      # 计数修改的记录
      if (length(replaced_fields) > 0 || length(added_fields) > 0) {
        modified_records <- modified_records + 1
      }
      
    }, error = function(e) {
      log_message(paste("  处理记录时出错:", e$message), log_file)
      log_entry$Status <- "错误"
      log_entry$ErrorMessage <- e$message
      
      # 添加到操作日志
      file_operations <- rbind(file_operations, data.frame(
        FileID = file_id,
        FileName = wos_filename,
        RecordID = record_id_str,
        Operation = "处理",
        Field = "多字段",
        OriginalContent = "N/A",
        NewContent = "N/A",
        Status = "失败",
        Notes = e$message,
        stringsAsFactors = FALSE
      ))
    })
    
    # 添加到日志数据框
    log_data <- rbind(log_data, as.data.frame(log_entry, stringsAsFactors = FALSE))
  }
  
  # 移除标记为删除的行 - 两个版本都需要处理
  new_content_with_new_fields <- new_content_with_new_fields[new_content_with_new_fields != "<<DELETE_LINE>>"]
  new_content_original_fields <- new_content_original_fields[new_content_original_fields != "<<DELETE_LINE>>"]
  
  # 保存整合后的两个版本文件
  output_file_with_new <- file.path(output_dir_with_new, wos_filename)
  output_file_original <- file.path(output_dir_original, wos_filename)
  
  writeLines(new_content_with_new_fields, output_file_with_new)
  writeLines(new_content_original_fields, output_file_original)
  
  log_message(paste("  已保存两个版本的整合文件:", basename(output_file_with_new), 
                   "- 修改了", modified_records, "条记录"), log_file)
  
  # 添加到操作日志
  file_operations <- rbind(file_operations, data.frame(
    FileID = file_id,
    FileName = wos_filename,
    RecordID = "全部",
    Operation = "保存文件",
    Field = "全部",
    OriginalContent = "N/A",
    NewContent = "N/A",
    Status = "成功",
    Notes = paste("生成两个版本的文件，共修改了", modified_records, "条记录"),
    stringsAsFactors = FALSE
  ))
  
  return(list(log_data = log_data, operations_log = rbind(operations_log, file_operations)))
}

#-------------------------------------------------------------------------------
# 主函数：处理所有文件
#-------------------------------------------------------------------------------
process_all_files <- function(wos_path, citespace_path, output_path_with_new, 
                           output_path_original, log_dir, log_file) {
  # 列出所有WoS原始文件
  wos_files <- list.files(wos_path, pattern = "\\.txt$", full.names = TRUE)
  log_message(paste("找到", length(wos_files), "个WoS原始文件"), log_file)
  
  # 列出所有CiteSpace预处理文件
  citespace_files <- list.files(citespace_path, pattern = "\\.txt$", full.names = TRUE)
  log_message(paste("找到", length(citespace_files), "个CiteSpace预处理文件"), log_file)
  
  # 预先解析所有CiteSpace文件以加速处理
  log_message("开始预解析CiteSpace文件...", log_file)
  citespace_parsed_files <- list()
  successful_cs_files <- 0
  
  for (i in 1:length(citespace_files)) {
    cs_file <- citespace_files[i]
    cs_filename <- basename(cs_file)
    log_message(paste("  预解析CiteSpace文件", i, "共", length(citespace_files), ":", cs_filename), log_file)
    
    # 先测试解析
    test_result <- test_file_parsing(cs_file)
    
    if (!test_result$success) {
      log_message(paste("  错误解析CiteSpace文件:", cs_filename, "-", test_result$error), log_file)
      citespace_parsed_files[[i]] <- list(
        filename = cs_filename,
        filepath = cs_file,
        success = FALSE,
        error = test_result$error
      )
      next
    }
    
    # 如果测试成功，进行完整解析
    tryCatch({
      cs_parsed <- parse_wos_file(cs_file)
      if (length(cs_parsed$records) == 0) {
        log_message(paste("  CiteSpace文件中没有记录:", cs_filename), log_file)
        citespace_parsed_files[[i]] <- list(
          filename = cs_filename,
          filepath = cs_file,
          success = FALSE,
          error = "没有找到记录"
        )
      } else {
        citespace_parsed_files[[i]] <- list(
          filename = cs_filename,
          filepath = cs_file,
          parsed = cs_parsed,
          success = TRUE
        )
        successful_cs_files <- successful_cs_files + 1
        log_message(paste("  成功解析:", cs_filename, "-", length(cs_parsed$records), "条记录"), log_file)
      }
    }, error = function(e) {
      log_message(paste("  错误解析CiteSpace文件:", cs_filename, "-", e$message), log_file)
      citespace_parsed_files[[i]] <- list(
        filename = cs_filename,
        filepath = cs_file,
        success = FALSE,
        error = e$message
      )
    })
  }
  
  log_message(paste("完成预解析", successful_cs_files, "个CiteSpace文件(共", length(citespace_files), "个)"), log_file)
  
  # 如果没有成功解析的CiteSpace文件，提前结束
  if (successful_cs_files == 0) {
    log_message("没有成功解析的CiteSpace文件，无法进行整合处理", log_file)
    
    # 初始化详细操作日志
    detailed_operations <- data.frame(
      FileID = integer(),
      FileName = character(),
      RecordID = character(),
      Operation = character(),
      Field = character(),
      OriginalContent = character(),
      NewContent = character(),
      Status = character(),
      Notes = character(),
      stringsAsFactors = FALSE
    )
    
    # 简单复制所有WoS文件到输出目录
    for (wos_file in wos_files) {
      wos_filename <- basename(wos_file)
      file.copy(wos_file, file.path(output_path_with_new, wos_filename), overwrite = TRUE)
      file.copy(wos_file, file.path(output_path_original, wos_filename), overwrite = TRUE)
      
      # 添加到操作日志
      detailed_operations <- rbind(detailed_operations, data.frame(
        FileID = which(wos_files == wos_file),
        FileName = wos_filename,
        RecordID = "全部",
        Operation = "复制",
        Field = "全部",
        OriginalContent = "N/A",
        NewContent = "N/A",
        Status = "成功",
        Notes = "无可用CiteSpace文件，直接复制",
        stringsAsFactors = FALSE
      ))
    }
    
    log_message("已将所有原始WoS文件复制到输出目录", log_file)
    
    # 初始化Excel日志数据
    log_data <- data.frame(
      WosFile = basename(wos_files),
      RecordID = rep("N/A", length(wos_files)),
      CitespaceFile = rep("N/A", length(wos_files)),
      ReplacedFields = rep("N/A", length(wos_files)),
      AddedFields = rep("N/A", length(wos_files)),
      Status = rep("跳过", length(wos_files)),
      ErrorMessage = rep("没有可用的CiteSpace文件", length(wos_files)),
      stringsAsFactors = FALSE
    )
    
    # 保存两个日志文件
    wb1 <- createWorkbook()
    addWorksheet(wb1, "整合日志")
    writeData(wb1, "整合日志", log_data)
    saveWorkbook(wb1, excel_log_file, overwrite = TRUE)
    
    wb2 <- createWorkbook()
    addWorksheet(wb2, "详细操作")
    writeData(wb2, "详细操作", detailed_operations)
    saveWorkbook(wb2, detailed_log_file, overwrite = TRUE)
    
    log_message("已保存日志文件", log_file)
    
    return(list(log_data = log_data, operations_log = detailed_operations))
  }
  
  # 初始化Excel日志数据
  log_data <- data.frame(
    WosFile = character(),
    RecordID = character(),
    CitespaceFile = character(),
    ReplacedFields = character(),
    AddedFields = character(),
    Status = character(),
    ErrorMessage = character(),
    stringsAsFactors = FALSE
  )
  
  # 初始化详细操作日志
  detailed_operations <- data.frame(
    FileID = integer(),
    FileName = character(),
    RecordID = character(),
    Operation = character(),
    Field = character(),
    OriginalContent = character(),
    NewContent = character(),
    Status = character(),
    Notes = character(),
    stringsAsFactors = FALSE
  )
  
  # 处理每个WoS文件
  successful_wos_files <- 0
  
  for (i in 1:length(wos_files)) {
    wos_file <- wos_files[i]
    log_message(paste("处理WoS文件", i, "共", length(wos_files), ":", basename(wos_file)), log_file)
    
    tryCatch({
      # 处理文件并获取更新后的日志
      result <- integrate_file(wos_file, citespace_parsed_files, output_path_with_new, 
                           output_path_original, log_file, log_data, detailed_operations, i)
      
      log_data <- result$log_data
      detailed_operations <- result$operations_log
      
      successful_wos_files <- successful_wos_files + 1
    }, error = function(e) {
      log_message(paste("  错误处理文件:", basename(wos_file), "-", e$message), log_file)
      
      # 简单复制原始文件到两个输出目录
      wos_filename <- basename(wos_file)
      file.copy(wos_file, file.path(output_path_with_new, wos_filename), overwrite = TRUE)
      file.copy(wos_file, file.path(output_path_original, wos_filename), overwrite = TRUE)
      log_message(paste("  已复制原始文件到输出目录:", wos_filename), log_file)
      
      # 添加到日志数据框
      log_data <- rbind(log_data, data.frame(
        WosFile = wos_filename,
        RecordID = "N/A",
        CitespaceFile = "N/A",
        ReplacedFields = "N/A",
        AddedFields = "N/A",
        Status = "错误",
        ErrorMessage = e$message,
        stringsAsFactors = FALSE
      ))
      
      # 添加到操作日志
      detailed_operations <- rbind(detailed_operations, data.frame(
        FileID = i,
        FileName = wos_filename,
        RecordID = "全部",
        Operation = "处理",
        Field = "全部",
        OriginalContent = "N/A",
        NewContent = "N/A",
        Status = "失败",
        Notes = e$message,
        stringsAsFactors = FALSE
      ))
    })
  }
  
  log_message(paste("完成处理", successful_wos_files, "个WoS文件(共", length(wos_files), "个)"), log_file)
  
  # 保存Excel日志
  wb1 <- createWorkbook()
  addWorksheet(wb1, "整合日志")
  writeData(wb1, "整合日志", log_data)
  
  # 添加过滤和格式
  setColWidths(wb1, "整合日志", cols = 1:ncol(log_data), widths = "auto")
  freezePane(wb1, "整合日志", firstRow = TRUE)
  
  # 设置条件格式
  conditionalFormatting(wb1, "整合日志", cols = which(names(log_data) == "Status"),
                       rows = 2:(nrow(log_data) + 1), 
                       rule = "成功", 
                       style = createStyle(bgFill = "#E2EFDA"))
  
  conditionalFormatting(wb1, "整合日志", cols = which(names(log_data) == "Status"),
                       rows = 2:(nrow(log_data) + 1), 
                       rule = "错误", 
                       style = createStyle(bgFill = "#F8CBAD"))
  
  conditionalFormatting(wb1, "整合日志", cols = which(names(log_data) == "Status"),
                       rows = 2:(nrow(log_data) + 1), 
                       rule = "跳过", 
                       style = createStyle(bgFill = "#FCE4D6"))
  
  saveWorkbook(wb1, excel_log_file, overwrite = TRUE)
  log_message(paste("已保存整合日志:", excel_log_file), log_file)
  
  # 保存详细操作日志
  wb2 <- createWorkbook()
  addWorksheet(wb2, "详细操作")
  writeData(wb2, "详细操作", detailed_operations)
  
  # 添加过滤和格式
  setColWidths(wb2, "详细操作", cols = 1:ncol(detailed_operations), widths = "auto")
  freezePane(wb2, "详细操作", firstRow = TRUE)
  
  # 设置条件格式
  conditionalFormatting(wb2, "详细操作", cols = which(names(detailed_operations) == "Status"),
                       rows = 2:(nrow(detailed_operations) + 1), 
                       rule = "成功", 
                       style = createStyle(bgFill = "#E2EFDA"))
  
  conditionalFormatting(wb2, "详细操作", cols = which(names(detailed_operations) == "Status"),
                       rows = 2:(nrow(detailed_operations) + 1), 
                       rule = "失败", 
                       style = createStyle(bgFill = "#F8CBAD"))
  
  saveWorkbook(wb2, detailed_log_file, overwrite = TRUE)
  log_message(paste("已保存详细操作日志:", detailed_log_file), log_file)
  
  log_message("所有文件处理完成！", log_file)
  
  return(list(log_data = log_data, operations_log = detailed_operations))
}

# 执行主函数
result <- process_all_files(wos_original_path, citespace_path, output_path_with_new_fields, 
                        output_path_original_fields, log_dir, log_file)

log_data <- result$log_data
detailed_operations <- result$operations_log

# 创建处理摘要
summary_file <- file.path(log_dir, "integration_summary.txt")

# 提取关键统计信息
total_wos_files <- length(list.files(wos_original_path, pattern = "\\.txt$"))
processed_files_with_new <- length(list.files(output_path_with_new_fields, pattern = "\\.txt$"))
processed_files_original <- length(list.files(output_path_original_fields, pattern = "\\.txt$"))

# 从日志数据中提取统计信息
successful_records <- sum(log_data$Status == "成功")
error_records <- sum(log_data$Status == "错误")
skipped_records <- sum(log_data$Status == "跳过")
total_records <- nrow(log_data)

# 处理可能的零除错误
success_percent <- ifelse(total_records > 0, round(successful_records/total_records*100, 1), 0)
error_percent <- ifelse(total_records > 0, round(error_records/total_records*100, 1), 0)
skipped_percent <- ifelse(total_records > 0, round(skipped_records/total_records*100, 1), 0)

# 统计替换和添加操作
replacement_operations <- detailed_operations[detailed_operations$Operation == "替换", ]
addition_operations <- detailed_operations[detailed_operations$Operation == "添加", ]

replacement_by_field <- table(replacement_operations$Field)
addition_by_field <- table(addition_operations$Field)

# 写入摘要文件
summary_content <- c(
  "===============================================",
  "     WoS与CiteSpace预处理文件整合摘要",
  "===============================================",
  "",
  paste("处理时间:", format(Sys.time(), "%Y-%m-%d %H:%M:%S")),
  "",
  "处理统计:",
  paste("  总WoS文件数:", total_wos_files),
  paste("  生成的整合文件数(包含新增字段):", processed_files_with_new),
  paste("  生成的整合文件数(仅原始字段):", processed_files_original),
  paste("  总记录数:", total_records),
  paste("  成功处理记录数:", successful_records, "(", success_percent, "%)"),
  paste("  失败记录数:", error_records, "(", error_percent, "%)"),
  paste("  跳过记录数:", skipped_records, "(", skipped_percent, "%)"),
  "",
  "字段替换统计:"
)

# 添加替换字段统计
if (length(replacement_by_field) > 0) {
  for (field in names(replacement_by_field)) {
    if (!is.na(field) && field != "") {
      summary_content <- c(summary_content, 
                          paste("  ", field, "字段: 替换了", replacement_by_field[field], "次"))
    }
  }
} else {
  summary_content <- c(summary_content, "  没有字段被替换")
}

summary_content <- c(summary_content, "", "字段添加统计:")

# 添加新增字段统计
if (length(addition_by_field) > 0) {
  for (field in names(addition_by_field)) {
    if (!is.na(field) && field != "") {
      summary_content <- c(summary_content, 
                          paste("  ", field, "字段: 添加了", addition_by_field[field], "次"))
    }
  }
} else {
  summary_content <- c(summary_content, "  没有字段被添加")
}

# 处理细节总结
operation_summary <- table(detailed_operations$Operation, detailed_operations$Status)
file_summary <- length(unique(detailed_operations$FileName))
record_summary <- length(unique(paste(detailed_operations$FileName, detailed_operations$RecordID)))

summary_content <- c(summary_content,
  "",
  "操作统计:",
  paste("  处理的文件数:", file_summary),
  paste("  涉及的记录数:", record_summary),
  ifelse(is.null(operation_summary["解析", "成功"]), "  成功解析: 0", paste("  成功解析:", operation_summary["解析", "成功"])),
  ifelse(is.null(operation_summary["替换", "成功"]), "  成功替换: 0", paste("  成功替换:", operation_summary["替换", "成功"])),
  ifelse(is.null(operation_summary["添加", "成功"]), "  成功添加: 0", paste("  成功添加:", operation_summary["添加", "成功"])),
  "",
  "处理策略:",
  "  1. 高优先级字段 (必须使用CiteSpace预处理): CR, AB, TI",
  "  2. 中优先级字段 (优先使用CiteSpace内容): DE, ID, WC, SC",
  "  3. 创建两个版本的输出:",
  "     - 包含新增字段的版本: 添加CiteSpace独有字段",
  "     - 仅原始字段的版本: 保持原始字段结构不变",
  "",
  "输出目录:",
  paste("  包含新增字段版本:", normalizePath(output_path_with_new_fields)),
  paste("  仅原始字段版本:", normalizePath(output_path_original_fields)),
  paste("  日志文件:", normalizePath(log_dir)),
  "",
  "详细日志文件:",
  paste("  1. 整合日志:", basename(excel_log_file)),
  paste("  2. 详细操作日志:", basename(detailed_log_file)),
  paste("  3. 处理过程日志:", basename(log_file)),
  "",
  "注意事项:",
  "  - 整合后的文件保留原始WoS格式结构",
  "  - 主要替换了内容，而非格式",
  "  - 建议使用bibliometrix测试整合后的文件",
  "  - 两个版本可用于比较分析新增字段的影响",
  "",
  "==============================================="
)

writeLines(summary_content, summary_file)
log_message(paste("已生成处理摘要:", summary_file), log_file)

# 完成处理
log_message("WoS与CiteSpace预处理文件整合过程完成！", log_file)