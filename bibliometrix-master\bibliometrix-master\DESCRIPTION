Package: bibliometrix
Type: Package
Title: Comprehensive Science Mapping Analysis
Version: 4.3.1
Authors@R: c(
    person(given = "Mass<PERSON>",
           family = "Aria",
           role = c("cre", "aut", "cph"),
           email = "<EMAIL>",
           comment = c(ORCID = "0000-0002-8517-9411")),
    person(given = "Corrado", 
           family = "<PERSON><PERSON><PERSON><PERSON><PERSON>", 
           role = "aut",
           email = "<EMAIL>",
           comment = c(ORCID = "0000-0002-7401-8575")))
Description: Tool for quantitative research in scientometrics and bibliometrics.
    It implements the comprehensive workflow for science mapping analysis proposed in Aria M. and 
    Cuccurullo C. (2017) <doi:10.1016/j.joi.2017.08.007>.
    'bibliometrix' provides various routines for importing bibliographic data from 'SCOPUS',
    'Clarivate Analytics Web of Science' (<https://www.webofknowledge.com/>), 'Digital Science Dimensions' 
	(<https://www.dimensions.ai/>), 'OpenAlex' (<https://openalex.org/>), 'Cochrane Library' (<https://www.cochranelibrary.com/>),  'Lens' (<https://lens.org>), 
	and 'PubMed' (<https://pubmed.ncbi.nlm.nih.gov/>) databases, performing bibliometric analysis 
    and building networks for co-citation, coupling, scientific collaboration and co-word analysis.
License: GPL-3
URL: https://www.bibliometrix.org, https://github.com/massimoaria/bibliometrix, https://www.k-synth.com
BugReports: https://github.com/massimoaria/bibliometrix/issues
LazyData: true
Encoding: UTF-8
Depends: R (>= 3.3.0)
Imports: stats,
     grDevices,
		 bibliometrixData,
		 dimensionsR, 
		 dplyr,
         DT,
		 ca,
		 forcats,
		 ggplot2,
		 ggrepel,
		 igraph,
		 Matrix, 
		 plotly,
		 openalexR,
		 openxlsx,
		 pubmedR,
		 purrr,
		 readr,
		 readxl,
	     rscopus,
		 shiny,
		 SnowballC,
		 stringdist,
		 stringi,
		 stringr,
		 tidyr,
		 tidytext
Suggests: 
    knitr,
    rmarkdown,
    testthat (>= 3.0.0),
    shinycssloaders,
    visNetwork,
    wordcloud2
RoxygenNote: 7.3.2
NeedsCompilation: no
Config/testthat/edition: 3
