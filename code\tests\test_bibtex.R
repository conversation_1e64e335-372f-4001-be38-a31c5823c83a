# =================================================================================================
# === BibTeX转换测试 ===
# =================================================================================================

# 加载必要的包
library(testthat)

# 加载被测试的模块
source("code/data/bibtex.R")

# 测试数据
test_work_data <- list(
  id = "https://openalex.org/W2741809807",
  display_name = "Bibliometric methods in business and management",
  publication_date = "2017-08-08",
  authorships = list(
    list(author = list(display_name = "<PERSON><PERSON>")),
    list(author = list(display_name = "<PERSON>"))
  ),
  locations = list(
    list(
      source = list(
        display_name = "Scientometrics",
        issn_l = "0138-9130"
      ),
      landing_page_url = "https://doi.org/10.1007/s11192-017-2317-y",
      pdf_url = "https://link.springer.com/content/pdf/10.1007/s11192-017-2317-y.pdf"
    )
  ),
  abstract_inverted_index = list(
    "bibliometric" = c(1),
    "methods" = c(2),
    "in" = c(3),
    "business" = c(4),
    "and" = c(5),
    "management" = c(6)
  ),
  keywords = list(
    list(display_name = "bibliometrics"),
    list(display_name = "business"),
    list(display_name = "management")
  ),
  topics = list(
    list(display_name = "Business and Management"),
    list(display_name = "Information Science")
  ),
  cited_by_count = 100,
  referenced_works = list(
    "https://openalex.org/W1234567890",
    "https://openalex.org/W0987654321"
  )
)

# 测试作者处理
test_that("process_authors正确处理作者信息", {
  authors <- process_authors(test_work_data$authorships)
  expect_type(authors, "character")
  expect_true(grepl("and", authors))
  expect_true(grepl("Bornmann", authors))
  expect_true(grepl("Haunschild", authors))
})

# 测试摘要处理
test_that("process_abstract正确处理摘要", {
  abstract <- process_abstract(test_work_data$abstract_inverted_index)
  expect_type(abstract, "character")
  expect_true(grepl("bibliometric", abstract))
  expect_true(grepl("methods", abstract))
})

# 测试关键词处理
test_that("process_keywords正确处理关键词", {
  keywords <- process_keywords(test_work_data$keywords, test_work_data$topics)
  expect_type(keywords, "character")
  expect_true(grepl("bibliometrics", keywords))
  expect_true(grepl("business", keywords))
  expect_true(grepl("management", keywords))
})

# 测试参考文献处理
test_that("process_references正确处理参考文献", {
  refs <- process_references(test_work_data$referenced_works)
  expect_type(refs, "character")
  expect_true(grepl("W1234567890", refs))
  expect_true(grepl("W0987654321", refs))
})

# 测试BibTeX转换
test_that("convert_to_bibtex正确转换数据", {
  bib_entry <- convert_to_bibtex(test_work_data)
  
  # 检查基本字段
  expect_equal(bib_entry$type, "article")
  expect_equal(bib_entry$key, "W2741809807")
  expect_equal(bib_entry$title, test_work_data$display_name)
  expect_equal(bib_entry$year, "2017")
  
  # 检查作者
  expect_true(grepl("Bornmann", bib_entry$author))
  expect_true(grepl("Haunschild", bib_entry$author))
  
  # 检查期刊信息
  expect_equal(bib_entry$journal, "Scientometrics")
  expect_equal(bib_entry$issn, "0138-9130")
  expect_true(grepl("doi.org", bib_entry$url))
  expect_true(grepl("springer.com", bib_entry$pdf))
  
  # 检查其他字段
  expect_true(grepl("bibliometric", bib_entry$abstract))
  expect_true(grepl("bibliometrics", bib_entry$keywords))
  expect_equal(bib_entry$citation_count, 100)
  expect_true(grepl("W1234567890", bib_entry$references))
})

# 测试BibTeX字符串转换
test_that("bibtex_to_string正确生成BibTeX格式", {
  bib_entry <- convert_to_bibtex(test_work_data)
  bib_str <- bibtex_to_string(bib_entry)
  
  # 检查BibTeX格式
  expect_true(grepl("^@article\\{W2741809807,", bib_str))
  expect_true(grepl("title = \\{Bibliometric methods in business and management\\},", bib_str))
  expect_true(grepl("year = \\{2017\\},", bib_str))
  expect_true(grepl("\\}$", bib_str))
})

# 测试文件保存
test_that("save_bibtex正确保存文件", {
  bib_entry <- convert_to_bibtex(test_work_data)
  filepath <- save_bibtex(list(bib_entry), "test.bib")
  
  # 检查文件是否存在
  expect_true(file.exists(filepath))
  
  # 检查文件内容
  content <- readLines(filepath)
  expect_true(grepl("^@article\\{W2741809807,", content[1]))
  
  # 清理测试文件
  unlink(filepath)
})

# 测试批量转换
test_that("batch_convert_to_bibtex正确处理多个条目", {
  work_data_list <- list(test_work_data, test_work_data)
  bib_entries <- batch_convert_to_bibtex(work_data_list)
  
  # 检查结果
  expect_type(bib_entries, "list")
  expect_length(bib_entries, 2)
  expect_equal(bib_entries[[1]]$key, "W2741809807")
  expect_equal(bib_entries[[2]]$key, "W2741809807")
}) 