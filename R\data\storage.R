#' 数据存储模块
#' @description 处理WoS导出的txt文件，构建基线数据集

library(bibliometrix)
library(tidyverse)
library(here)

#' 读取并转换WoS数据文件
#' @param file_paths WoS导出的txt文件路径向量
#' @return 转换后的数据框
convert_wos_data <- function(file_paths) {
  # 检查文件是否存在
  for (file in file_paths) {
    if (!file.exists(file)) {
      stop(sprintf("文件不存在: %s", file))
    }
  }
  
  # 使用bibliometrix进行转换
  M <- convert2df(file = file_paths, dbsource = "wos", format = "plaintext")
  
  return(M)
}

#' 构建基线数据集
#' @param raw_data_dir 原始数据目录
#' @return 基线数据集
build_baseline_dataset <- function(raw_data_dir) {
  # 获取所有txt文件
  wos_files <- list.files(
    path = raw_data_dir,
    pattern = "\\.txt$",
    full.names = TRUE,
    ignore.case = TRUE
  )
  
  if (length(wos_files) == 0) {
    stop(sprintf("在目录 %s 中没有找到txt文件", raw_data_dir))
  }
  
  # 转换数据
  M <- convert_wos_data(wos_files)
  
  # 保存基线数据集
  saveRDS(M, here("data", "raw", "baseline_dataset.rds"))
  
  # 保存数据集信息
  dataset_info <- list(
    source_files = basename(wos_files),
    record_count = nrow(M),
    field_count = ncol(M),
    fields = names(M),
    creation_date = Sys.time()
  )
  
  saveRDS(dataset_info, here("data", "raw", "baseline_dataset_info.rds"))
  
  return(M)
}

#' 获取原始记录和来源文件
#' @param target_ut 目标UT（唯一标识符）
#' @param wos_files_list WoS文件列表
#' @return 包含原始记录和来源文件的列表
get_raw_record_and_source_file <- function(target_ut, wos_files_list) {
  for (file_path in wos_files_list) {
    tryCatch({
      raw_lines <- readLines(file_path, warn = FALSE)
      
      # 查找包含target_ut的UT标签行
      ut_line_indices <- which(startsWith(raw_lines, "UT "))
      found_ut_line_idx <- -1
      
      if (length(ut_line_indices) > 0) {
        for(idx in ut_line_indices) {
          if(grepl(target_ut, raw_lines[idx], fixed = TRUE)) {
            found_ut_line_idx <- idx
            break
          }
        }
      }
      
      if (found_ut_line_idx > 0) {
        current_ut_line <- found_ut_line_idx
        start_line_idx <- -1
        
        # 向后搜索PT或FN作为记录开始
        for (k in seq(current_ut_line, 1, by = -1)) {
          if (grepl("^PT ", raw_lines[k]) || grepl("^FN ", raw_lines[k])) {
            start_line_idx <- k
            break
          }
        }
        
        # 如果找不到PT/FN，从UT行的前一行开始
        if (start_line_idx == -1) {
          start_line_idx <- max(1, current_ut_line - 1)
        }
        
        # 向前搜索ER作为记录结束
        end_line_idx <- -1
        for (k in seq(current_ut_line, length(raw_lines))) {
          if (grepl("^ER", raw_lines[k])) {
            end_line_idx <- k
            break
          }
        }
        
        if (end_line_idx == -1) {
          end_line_idx <- length(raw_lines)
        }
        
        record_block <- raw_lines[start_line_idx:end_line_idx]
        return(list(
          source_file = basename(file_path),
          raw_record = paste(record_block, collapse = "\n")
        ))
      }
    }, error = function(e) {
      cat(sprintf("处理文件 %s 时出错: %s\n", basename(file_path), conditionMessage(e)))
    })
  }
  
  return(list(
    source_file = "Not found in any file",
    raw_record = sprintf("UT '%s' not found.", target_ut)
  ))
} 