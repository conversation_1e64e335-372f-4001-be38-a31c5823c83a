# 文献计量学多方法比较分析系统 - 优化版
# 作者：数据处理团队
# 日期：2024年

#-----------------------------------------------------------------------------
# 1. 环境准备与配置
#-----------------------------------------------------------------------------
# 清理工作环境
rm(list = ls())
gc()

# 加载必要的库
tryCatch({
  library(bibliometrix)
  library(tidyverse)
  library(igraph)
}, error = function(e) {
  # 如果库不存在，尝试安装
  missing_packages <- c("bibliometrix", "tidyverse", "igraph")[
    !c("bibliometrix", "tidyverse", "igraph") %in% installed.packages()[,"Package"]
  ]
  if(length(missing_packages) > 0) {
    install.packages(missing_packages)
    lapply(missing_packages, library, character.only = TRUE)
  }
})

#-----------------------------------------------------------------------------
# 2. 日志功能
#-----------------------------------------------------------------------------
# 设置工作目录为当前目录，或允许用户自定义
project_dir <- getwd()
cat("项目目录设置为:", project_dir, "\n")

# 创建日志文件夹
log_dir <- file.path(project_dir, "logs")
if (!dir.exists(log_dir)) dir.create(log_dir)

# 日志文件名包含时间戳
log_file <- file.path(log_dir, paste0("biblio_analysis_", format(Sys.time(), "%Y%m%d_%H%M%S"), ".log"))

# 日志函数
write_log <- function(message, type = "INFO") {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  log_message <- paste0("[", timestamp, "][", type, "] ", message)
  cat(log_message, "\n")
  cat(log_message, "\n", file = log_file, append = TRUE)
}

#-----------------------------------------------------------------------------
# 3. 数据导入与预处理
#-----------------------------------------------------------------------------
# 创建数据源文件夹结构
data_dir <- file.path(project_dir, "data")
if (!dir.exists(data_dir)) dir.create(data_dir)

# WoS数据文件夹
wos_dir <- file.path(data_dir, "wos_exports")
if (!dir.exists(wos_dir)) dir.create(wos_dir)

# Scopus数据文件夹
scopus_dir <- file.path(data_dir, "scopus_exports")
if (!dir.exists(scopus_dir)) dir.create(scopus_dir)

# Dimensions数据文件夹
dimensions_dir <- file.path(data_dir, "dimensions_exports")
if (!dir.exists(dimensions_dir)) dir.create(dimensions_dir)

# OpenAlex数据文件夹
openalex_dir <- file.path(data_dir, "openalex_exports")
if (!dir.exists(openalex_dir)) dir.create(openalex_dir)

# CrossRef数据文件夹
crossref_dir <- file.path(data_dir, "crossref_exports")
if (!dir.exists(crossref_dir)) dir.create(crossref_dir)

write_log("开始导入数据文件")

# 检查是否有用户自定义的数据文件夹
if(exists("custom_data_dir") && dir.exists(custom_data_dir)) {
  write_log(paste0("使用用户自定义数据目录: ", custom_data_dir))
  # 复制文件到标准目录结构
  txt_files <- list.files(custom_data_dir, pattern = "\\.txt$", full.names = TRUE)
  if(length(txt_files) > 0) {
    for(file in txt_files) {
      file.copy(file, file.path(wos_dir, basename(file)))
    }
    write_log(paste0("已复制", length(txt_files), "个文件到WoS导出目录"))
  }
}

# 数据导入函数，添加错误处理和验证
import_data <- function(directory, dbsource = "wos", format = "plaintext") {
  tryCatch({
    # 根据数据源类型设置文件匹配模式
    file_pattern <- switch(dbsource,
                           "wos" = "*.txt",
                           "scopus" = "*.csv",
                           "dimensions" = "*.csv",
                           "openalex" = "*.json",
                           "crossref" = "*.json",
                           "*.txt")
    
    # 使用bibliometrix的convert2df函数导入数据
    files <- list.files(directory, pattern = file_pattern, full.names = TRUE)
    
    if (length(files) == 0) {
      write_log(paste0("在指定目录未找到", file_pattern, "文件"), "ERROR")
      return(NULL)
    }
    
    write_log(paste0("找到", length(files), "个", dbsource, "文件用于导入"))
    
    # 逐个读取并合并，而不是一次性读取所有文件
    # 这样可以更好地控制错误并保持原始文件对应关系
    all_records <- data.frame()
    
    for (file in files) {
      write_log(paste0("正在处理文件: ", basename(file)))
      
      # 尝试读取文件
      single_file_data <- tryCatch({
        convert2df(file, dbsource = dbsource, format = format)
      }, error = function(e) {
        write_log(paste0("文件读取错误: ", basename(file), " - ", e$message), "ERROR")
        return(NULL)
      })
      
      # 如果成功读取，合并到总数据集
      if (!is.null(single_file_data)) {
        if (nrow(all_records) == 0) {
          all_records <- single_file_data
        } else {
          all_records <- rbind(all_records, single_file_data)
        }
        write_log(paste0("已成功读取: ", basename(file), " (", nrow(single_file_data), "条记录)"))
      }
    }
    
    if (nrow(all_records) == 0) {
      write_log("未能成功导入任何记录", "ERROR")
      return(NULL)
    }
    
    write_log(paste0("数据导入完成，共", nrow(all_records), "条记录"))
    return(all_records)
    
  }, error = function(e) {
    write_log(paste0("数据导入过程出错: ", e$message), "ERROR")
    return(NULL)
  })
}

# 根据优先级依次导入数据
# 按照优先级顺序：CrossRef/出版商API > WoS/Scopus > OpenAlex/Dimensions
wos_data <- NULL

# 尝试从主要数据源导入
write_log("按照优先级顺序导入数据")

# 首先尝试CrossRef数据(如果存在)
if(length(list.files(crossref_dir, pattern = "*.json")) > 0) {
  write_log("尝试从CrossRef导入数据")
  wos_data <- import_data(crossref_dir, dbsource = "dimensions", format = "csv") # CrossRef通常需要特殊处理
  if(!is.null(wos_data)) {
    write_log("成功从CrossRef导入基础数据集")
  }
}

# 如果没有CrossRef数据或导入失败，尝试WoS数据
if(is.null(wos_data)) {
  wos_data <- import_data(wos_dir, dbsource = "wos", format = "plaintext")
  if (!is.null(wos_data)) {
    write_log("成功从WoS导入基础数据集")
  } else {
    # 如果WoS数据不可用，尝试Scopus数据
    write_log("WoS数据不可用，尝试从Scopus导入", "WARNING")
    wos_data <- import_data(scopus_dir, dbsource = "scopus", format = "csv")
    
    if (!is.null(wos_data)) {
      write_log("成功从Scopus导入基础数据集")
    } else {
      # 如果Scopus数据也不可用，尝试OpenAlex/Dimensions数据
      write_log("WoS和Scopus数据均不可用，尝试从其他来源导入", "WARNING")
      
      # 尝试OpenAlex
      wos_data <- import_data(openalex_dir, dbsource = "dimensions", format = "csv")
      if (!is.null(wos_data)) {
        write_log("成功从OpenAlex导入基础数据集")
      } else {
        # 最后尝试Dimensions
        wos_data <- import_data(dimensions_dir, dbsource = "dimensions", format = "csv")
        if (!is.null(wos_data)) {
          write_log("成功从Dimensions导入基础数据集")
        }
      }
    }
  }
}

# 检查数据是否成功导入
if (is.null(wos_data)) {
  write_log("所有数据源均无法导入数据，程序终止", "ERROR")
  stop("数据导入失败")
}

# 检查数据结构，诊断潜在问题
write_log("开始数据结构诊断")
data_diagnosis <- function(M) {
  diagnosis_log <- list()
  
  # 基本维度
  diagnosis_log$dimensions <- dim(M)
  write_log(paste0("数据框维度: ", diagnosis_log$dimensions[1], " 行 x ", diagnosis_log$dimensions[2], " 列"))
  
  # 检查关键字段存在性
  key_fields <- c("AU", "TI", "SO", "PY", "DI", "CR")
  existing_fields <- key_fields[key_fields %in% names(M)]
  missing_fields <- key_fields[!key_fields %in% names(M)]
  diagnosis_log$existing_key_fields <- existing_fields
  diagnosis_log$missing_key_fields <- missing_fields
  
  if(length(missing_fields) > 0) {
    write_log(paste0("缺失关键字段: ", paste(missing_fields, collapse = ", ")), "WARNING")
  }
  
  # 检查字段完整性
  field_completeness <- sapply(existing_fields, function(f) {
    complete <- sum(!is.na(M[[f]]) & M[[f]] != "") / nrow(M) * 100
    return(round(complete, 2))
  })
  diagnosis_log$field_completeness <- field_completeness
  
  for(f in names(field_completeness)) {
    status <- if(field_completeness[f] > 95) "GOOD" else if(field_completeness[f] > 80) "ACCEPTABLE" else "POOR"
    write_log(paste0("字段 ", f, " 完整度: ", field_completeness[f], "% (", status, ")"))
  }
  
  return(diagnosis_log)
}

# 执行数据诊断
diagnosis_results <- data_diagnosis(wos_data)

#-----------------------------------------------------------------------------
# 4. 数据清洗与标准化
#-----------------------------------------------------------------------------
# 数据清洗：去除重复记录
write_log("开始数据清洗")

# 记录清洗前的文章数量
original_count <- nrow(wos_data)
write_log(paste0("清洗前记录数: ", original_count))

# 构建用于去重的字段组合
dedup_fields <- c()
if("DI" %in% colnames(wos_data)) dedup_fields <- c(dedup_fields, "DI")
if("TI" %in% colnames(wos_data)) dedup_fields <- c(dedup_fields, "TI")
if("PY" %in% colnames(wos_data)) dedup_fields <- c(dedup_fields, "PY")
if("AU" %in% colnames(wos_data)) dedup_fields <- c(dedup_fields, "AU")

# 确保至少有一个字段用于去重
if(length(dedup_fields) == 0) {
  write_log("没有可用于去重的字段，将使用所有数据", "WARNING")
  unique_data <- wos_data
} else {
  # 识别重复记录
  write_log(paste0("使用以下字段进行去重: ", paste(dedup_fields, collapse = ", ")))
  duplicates <- duplicated(wos_data[, dedup_fields])
  unique_data <- wos_data[!duplicates, ]
}

# 记录清洗后的文章数量
cleaned_count <- nrow(unique_data)
write_log(paste0("清洗后记录数: ", cleaned_count))
write_log(paste0("移除了 ", original_count - cleaned_count, " 条重复记录"))

# 标准化作者名称和关键词（自定义规则，比bibliometrix更严格）
normalize_authors <- function(df) {
  tryCatch({
    # 保存原始作者列，用于比较和验证
    if("AU" %in% colnames(df)) {
      df$AU_ORIGINAL <- df$AU
      
      # 这里可以添加更严格的作者名标准化代码
      # 例如处理名字缩写、不同格式的姓名等
      
      write_log("已完成作者名称标准化并保留原始数据")
    } else {
      write_log("作者字段(AU)不存在，跳过作者名标准化", "WARNING")
    }
    
    # 返回处理后的数据框
    return(df)
  }, error = function(e) {
    write_log(paste0("作者名标准化失败: ", e$message), "WARNING")
    return(df)
  })
}

normalize_keywords <- function(df) {
  tryCatch({
    # 保存原始关键词，用于比较和验证
    if ("DE" %in% colnames(df)) {
      df$DE_ORIGINAL <- df$DE
      # 这里可以添加作者关键词标准化代码
      write_log("已完成作者关键词(DE)标准化并保留原始数据")
    }
    
    if ("ID" %in% colnames(df)) {
      df$ID_ORIGINAL <- df$ID
      # 这里可以添加关键词Plus标准化代码
      write_log("已完成关键词Plus(ID)标准化并保留原始数据")
    }
    
    # 返回处理后的数据框
    return(df)
  }, error = function(e) {
    write_log(paste0("关键词标准化失败: ", e$message), "WARNING")
    return(df)
  })
}

# 应用标准化函数
write_log("开始数据标准化")
clean_data <- unique_data
clean_data <- normalize_authors(clean_data)
clean_data <- normalize_keywords(clean_data)
write_log("数据标准化完成")

#-----------------------------------------------------------------------------
# 5. 文献计量分析
#-----------------------------------------------------------------------------
# 基础分析
write_log("开始基础文献计量分析")

# 创建结果目录结构
results_dir <- file.path(project_dir, "results")
if (!dir.exists(results_dir)) dir.create(results_dir)

# 详细结果子目录
tables_dir <- file.path(results_dir, "tables")
if (!dir.exists(tables_dir)) dir.create(tables_dir)

plots_dir <- file.path(results_dir, "plots")
if (!dir.exists(plots_dir)) dir.create(plots_dir)

networks_dir <- file.path(results_dir, "networks")
if (!dir.exists(networks_dir)) dir.create(networks_dir)

# 保存处理后的数据
save(clean_data, file = file.path(results_dir, "clean_data.RData"))
write.csv(clean_data, file = file.path(tables_dir, "clean_data.csv"), row.names = FALSE)

# 运行bibliometrix基础描述性分析
basic_results <- tryCatch({
  results <- biblioAnalysis(clean_data, sep = ";")
  write_log("基础描述性分析完成")
  results
}, error = function(e) {
  write_log(paste0("基础描述性分析失败: ", e$message), "ERROR")
  NULL
})

# 如果基础分析成功，生成摘要和图表
if (!is.null(basic_results)) {
  # 生成文本摘要
  tryCatch({
    summary_results <- summary(basic_results, k = 10, pause = FALSE)
    capture.output(summary_results, file = file.path(tables_dir, "summary_results.txt"))
    write_log("已生成分析摘要")
  }, error = function(e) {
    write_log(paste0("生成分析摘要失败: ", e$message), "WARNING")
  })
  
  # 生成图表
  tryCatch({
    # 设置图形输出参数
    pdf(file = file.path(plots_dir, "bibliometric_plots.pdf"), width = 10, height = 8)
    
    # 创建多种图表，每种图表单独处理异常
    for (plot_type in c("top-authors", "country", "annual", "most-cited")) {
      tryCatch({
        plot <- plot(basic_results, k = 10, pause = FALSE, type = plot_type)
        # 图表已在tryCatch中绘制
        write_log(paste0("已生成", plot_type, "图表"))
      }, error = function(e) {
        write_log(paste0("生成", plot_type, "图表失败: ", e$message), "WARNING")
      })
    }
    
    dev.off()
  }, error = function(e) {
    write_log(paste0("图表生成过程出错: ", e$message), "WARNING")
  })
} 

#-----------------------------------------------------------------------------
# 6. 网络分析
#-----------------------------------------------------------------------------
write_log("开始进行网络分析")

# 网络分析函数，带有错误处理和参数调整
create_network <- function(M, analysis_type, network_type, ...) {
  tryCatch({
    # 网络配置参数
    args <- list(...)
    
    # 对不同类型网络使用不同策略和参数
    result <- switch(
      analysis_type,
      "co-citation" = {
        # 共被引网络
        if (network_type == "authors") {
          cocitation_threshold <- args$threshold %||% 1
          # 使用更健壮的参数配置
          NetMatrix <- biblioNetwork(M, analysis = "co-citation", 
                                     network = network_type, 
                                     sep = ";",
                                     shortlabel = TRUE)
          
          # 保存矩阵以供检查
          save(NetMatrix, file = file.path(networks_dir, paste0("cocitation_", network_type, "_matrix.RData")))
          
          # 使用可配置的normalizeNetwork参数
          if (args$normalize %||% TRUE) {
            NetMatrix <- normalizeSimilarity(NetMatrix, type = "association")
          }
          
          # 网络绘制
          if (is.null(NetMatrix) || sum(NetMatrix) == 0) {
            write_log(paste0("共被引网络矩阵为空或全零: ", network_type), "WARNING")
            return(NULL)
          }
          
          net <- networkPlot(NetMatrix, n = args$n %||% 50, 
                             Title = paste0("Co-citation Network: ", network_type),
                             type = args$layout %||% "fruchterman", 
                             size = args$size %||% TRUE,
                             remove.multiple = args$remove_multiple %||% TRUE,
                             labelsize = args$labelsize %||% 1,
                             edgesize = args$edgesize %||% 0.5,
                             edges.min = cocitation_threshold)
          return(list(network = net, matrix = NetMatrix))
        } else {
          # 其他共被引网络类型
          NetMatrix <- biblioNetwork(M, analysis = "co-citation", 
                                     network = network_type, 
                                     sep = ";")
          save(NetMatrix, file = file.path(networks_dir, paste0("cocitation_", network_type, "_matrix.RData")))
          
          if (is.null(NetMatrix) || sum(NetMatrix) == 0) {
            write_log(paste0("共被引网络矩阵为空或全零: ", network_type), "WARNING")
            return(NULL)
          }
          
          net <- networkPlot(NetMatrix, n = args$n %||% 50, 
                             Title = paste0("Co-citation Network: ", network_type),
                             type = args$layout %||% "fruchterman", 
                             size = args$size %||% TRUE,
                             remove.multiple = args$remove_multiple %||% TRUE,
                             labelsize = args$labelsize %||% 1,
                             edgesize = args$edgesize %||% 0.5)
          return(list(network = net, matrix = NetMatrix))
        }
      },
      "coupling" = {
        # 耦合网络
        NetMatrix <- biblioNetwork(M, analysis = "coupling", 
                                   network = network_type, 
                                   sep = ";")
        save(NetMatrix, file = file.path(networks_dir, paste0("coupling_", network_type, "_matrix.RData")))
        
        if (is.null(NetMatrix) || sum(NetMatrix) == 0) {
          write_log(paste0("耦合网络矩阵为空或全零: ", network_type), "WARNING")
          return(NULL)
        }
        
        net <- networkPlot(NetMatrix, n = args$n %||% 50, 
                           Title = paste0("Coupling Network: ", network_type),
                           type = args$layout %||% "fruchterman", 
                           size = args$size %||% TRUE,
                           remove.multiple = args$remove_multiple %||% TRUE,
                           labelsize = args$labelsize %||% 1,
                           edgesize = args$edgesize %||% 0.5)
        return(list(network = net, matrix = NetMatrix))
      },
      "collaboration" = {
        # 合作网络
        NetMatrix <- biblioNetwork(M, analysis = "collaboration", 
                                   network = network_type, 
                                   sep = ";")
        save(NetMatrix, file = file.path(networks_dir, paste0("collaboration_", network_type, "_matrix.RData")))
        
        if (is.null(NetMatrix) || sum(NetMatrix) == 0) {
          write_log(paste0("合作网络矩阵为空或全零: ", network_type), "WARNING")
          return(NULL)
        }
        
        net <- networkPlot(NetMatrix, n = args$n %||% 50, 
                           Title = paste0("Collaboration Network: ", network_type),
                           type = args$layout %||% "fruchterman", 
                           size = args$size %||% TRUE,
                           remove.multiple = args$remove_multiple %||% TRUE,
                           labelsize = args$labelsize %||% 1,
                           edgesize = args$edgesize %||% 0.5)
        return(list(network = net, matrix = NetMatrix))
      },
      "co-occurrences" = {
        # 共现网络
        # 对于关键词共现，增加检查和预处理
        if (network_type %in% c("keywords", "author_keywords", "index_keywords")) {
          
          # 检查关键词列是否存在且非空
          if (network_type == "keywords" && (!("DE" %in% colnames(M)) || all(is.na(M$DE)))) {
            write_log("作者关键词(DE)列缺失或为空，尝试改用索引关键词(ID)", "WARNING")
            network_type <- "index_keywords"
          }
          
          if (network_type == "author_keywords" && (!("DE" %in% colnames(M)) || all(is.na(M$DE)))) {
            write_log("作者关键词(DE)列缺失或为空，尝试改用索引关键词(ID)", "WARNING") 
            network_type <- "index_keywords"
          }
          
          if (network_type == "index_keywords" && (!("ID" %in% colnames(M)) || all(is.na(M$ID)))) {
            write_log("索引关键词(ID)列缺失或为空，无法创建关键词网络", "ERROR")
            return(NULL)
          }
          
          # 关键词预处理：去除特殊字符、转换大小写等
          if (network_type %in% c("keywords", "author_keywords")) {
            # 对作者关键词进行清洗
            write_log("对作者关键词进行预处理")
            # 这里可以添加更多关键词预处理代码
          } else if (network_type == "index_keywords") {
            # 对索引关键词进行清洗
            write_log("对索引关键词进行预处理")
            # 这里可以添加更多关键词预处理代码
          }
        }
        
        # 创建共现网络
        NetMatrix <- biblioNetwork(M, analysis = "co-occurrences", 
                                   network = network_type, 
                                   sep = ";")
        save(NetMatrix, file = file.path(networks_dir, paste0("cooccurrences_", network_type, "_matrix.RData")))
        
        if (is.null(NetMatrix) || sum(NetMatrix) == 0) {
          write_log(paste0("共现网络矩阵为空或全零: ", network_type), "WARNING")
          return(NULL)
        }
        
        net <- networkPlot(NetMatrix, n = args$n %||% 50, 
                           Title = paste0("Co-occurrences Network: ", network_type),
                           type = args$layout %||% "fruchterman", 
                           size = args$size %||% TRUE,
                           remove.multiple = args$remove_multiple %||% TRUE,
                           labelsize = args$labelsize %||% 1,
                           edgesize = args$edgesize %||% 0.5)
        return(list(network = net, matrix = NetMatrix))
      },
      {
        # 默认情况
        write_log(paste0("不支持的网络分析类型: ", analysis_type), "WARNING")
        return(NULL)
      }
    )
    
    # 记录网络创建成功
    write_log(paste0("成功创建", analysis_type, "-", network_type, "网络"))
    return(result)
    
  }, error = function(e) {
    write_log(paste0("创建", analysis_type, "-", network_type, "网络失败: ", e$message), "ERROR")
    return(NULL)
  })
}

# NULL合并运算符定义
`%||%` <- function(x, y) if (is.null(x)) y else x

# 执行网络分析
# 将每个网络分析放在单独的tryCatch中，确保一个失败不会影响其他
# 保存PDF图形
pdf(file = file.path(networks_dir, "network_plots.pdf"), width = 12, height = 10)

# 1. 共被引作者网络
coauth_network <- tryCatch({
  write_log("创建共被引作者网络")
  net <- create_network(clean_data, "co-citation", "authors", 
                        n = 30, 
                        threshold = 1, 
                        normalize = TRUE,
                        layout = "auto")
  write_log("完成共被引作者网络创建")
  net
}, error = function(e) {
  write_log(paste0("共被引作者网络创建失败: ", e$message), "ERROR")
  NULL
})

# 2. 共被引参考文献网络
coref_network <- tryCatch({
  write_log("创建共被引参考文献网络")
  net <- create_network(clean_data, "co-citation", "references", 
                        n = 30,
                        layout = "auto")
  write_log("完成共被引参考文献网络创建")
  net
}, error = function(e) {
  write_log(paste0("共被引参考文献网络创建失败: ", e$message), "ERROR")
  NULL
})

# 3. 作者耦合网络
coupling_auth_network <- tryCatch({
  write_log("创建作者耦合网络")
  net <- create_network(clean_data, "coupling", "authors", 
                        n = 30,
                        layout = "auto")
  write_log("完成作者耦合网络创建")
  net
}, error = function(e) {
  write_log(paste0("作者耦合网络创建失败: ", e$message), "ERROR")
  NULL
})

# 4. 国家合作网络
collab_country_network <- tryCatch({
  write_log("创建国家合作网络")
  # 检查国家数据是否完整
  if (!("AU_CO" %in% colnames(clean_data)) || sum(!is.na(clean_data$AU_CO)) < nrow(clean_data) * 0.5) {
    write_log("国家数据不完整，可能影响网络质量", "WARNING")
    # 尝试执行国家标签提取
    write_log("尝试从机构信息提取国家标签")
    clean_data <- metaTagExtraction(clean_data, Field = "AU_CO")
  }
  
  net <- create_network(clean_data, "collaboration", "countries", 
                        n = 30,
                        layout = "auto")
  write_log("完成国家合作网络创建")
  net
}, error = function(e) {
  write_log(paste0("国家合作网络创建失败: ", e$message), "ERROR")
  NULL
})

# 5. 关键词共现网络
keywords_network <- tryCatch({
  write_log("创建关键词共现网络")
  # 尝试作者关键词
  if ("DE" %in% colnames(clean_data) && sum(!is.na(clean_data$DE)) > nrow(clean_data) * 0.5) {
    net <- create_network(clean_data, "co-occurrences", "author_keywords", 
                          n = 50,
                          layout = "auto")
    write_log("完成作者关键词共现网络创建")
  } else if ("ID" %in% colnames(clean_data) && sum(!is.na(clean_data$ID)) > nrow(clean_data) * 0.5) {
    # 如果作者关键词不可用，尝试索引关键词
    write_log("作者关键词不足，转为创建索引关键词网络", "WARNING")
    net <- create_network(clean_data, "co-occurrences", "index_keywords", 
                          n = 50,
                          layout = "auto")
    write_log("完成索引关键词共现网络创建")
  } else {
    # 如果两种关键词都不可用
    write_log("关键词数据不足，无法创建关键词网络", "WARNING")
    net <- NULL
  }
  net
}, error = function(e) {
  write_log(paste0("关键词共现网络创建失败: ", e$message), "ERROR")
  NULL
})

# 关闭PDF设备
dev.off()

# 记录网络分析结果
network_results <- list(
  coauth_network = coauth_network,
  coref_network = coref_network,
  coupling_auth_network = coupling_auth_network,
  collab_country_network = collab_country_network,
  keywords_network = keywords_network
)
save(network_results, file = file.path(networks_dir, "network_results.RData"))
write_log("网络分析完成并保存结果") 

#-----------------------------------------------------------------------------
# 7. 主题分析
#-----------------------------------------------------------------------------
write_log("开始主题分析")

# 创建主题分析目录
themes_dir <- file.path(results_dir, "themes")
if (!dir.exists(themes_dir)) dir.create(themes_dir)

# 主题映射分析
thematic_map <- tryCatch({
  # 首先检查关键词可用性
  keyword_field <- NA
  
  if ("DE" %in% colnames(clean_data) && sum(!is.na(clean_data$DE) & clean_data$DE != "") > nrow(clean_data) * 0.5) {
    keyword_field <- "DE"
    write_log("使用作者关键词(DE)进行主题映射")
  } else if ("ID" %in% colnames(clean_data) && sum(!is.na(clean_data$ID) & clean_data$ID != "") > nrow(clean_data) * 0.5) {
    keyword_field <- "ID"
    write_log("使用索引关键词(ID)进行主题映射")
  } else {
    write_log("关键词数据不足，无法执行主题映射", "WARNING")
    return(NULL)
  }
  
  # 设置主题映射参数
  map <- thematicMap(clean_data, field = keyword_field, n = 250, 
                     minfreq = 5, stemming = FALSE, size = 0.5, 
                     n.labels = 5, repel = TRUE)
  
  # 绘制主题图
  pdf(file = file.path(themes_dir, "thematic_map.pdf"), width = 10, height = 8)
  plot(map$map)
  dev.off()
  
  # 保存数据
  save(map, file = file.path(themes_dir, "thematic_map.RData"))
  write.csv(map$clusters, file = file.path(themes_dir, "thematic_clusters.csv"), row.names = FALSE)
  
  write_log("主题映射分析完成")
  return(map)
}, error = function(e) {
  write_log(paste0("主题映射分析失败: ", e$message), "ERROR")
  return(NULL)
})

# 概念结构分析 - 对应分析
conceptual_structure <- tryCatch({
  # 检查关键词可用性
  if (("DE" %in% colnames(clean_data) && sum(!is.na(clean_data$DE) & clean_data$DE != "") > nrow(clean_data) * 0.3) ||
      ("ID" %in% colnames(clean_data) && sum(!is.na(clean_data$ID) & clean_data$ID != "") > nrow(clean_data) * 0.3)) {
    
    # 执行对应分析
    cs <- conceptualStructure(clean_data, 
                              field = "DE", 
                              method = "CA", 
                              minDegree = 5, 
                              clust = 5, 
                              stemming = FALSE, 
                              labelsize = 10, 
                              documents = 10)
    
    # 保存分析结果
    save(cs, file = file.path(themes_dir, "conceptual_structure.RData"))
    write_log("概念结构分析完成")
    return(cs)
  } else {
    write_log("关键词数据不足，无法执行概念结构分析", "WARNING")
    return(NULL)
  }
}, error = function(e) {
  write_log(paste0("概念结构分析失败: ", e$message), "ERROR")
  return(NULL)
})

# 历史直接引用流 (历史发展分析)
histNetwork <- tryCatch({
  # 检查引用数据
  if ("CR" %in% colnames(clean_data) && sum(!is.na(clean_data$CR) & clean_data$CR != "") > nrow(clean_data) * 0.5) {
    # 执行历史引用流分析
    histResults <- histNetwork(clean_data, min.citations = 10, n = 25)
    
    # 绘制历史流图
    pdf(file = file.path(themes_dir, "historical_network.pdf"), width = 14, height = 10)
    net <- histPlot(histResults, n = 20, size = 10, labelsize = 5)
    dev.off()
    
    # 保存分析结果
    save(histResults, file = file.path(themes_dir, "historical_network.RData"))
    write_log("历史引用流分析完成")
    return(histResults)
  } else {
    write_log("引用数据不足，无法执行历史引用流分析", "WARNING")
    return(NULL)
  }
}, error = function(e) {
  write_log(paste0("历史引用流分析失败: ", e$message), "ERROR")
  return(NULL)
})

# 作者关系分析
authorRelations <- tryCatch({
  if ("AU" %in% colnames(clean_data) && sum(!is.na(clean_data$AU) & clean_data$AU != "") > nrow(clean_data) * 0.7) {
    # 作者主要贡献分析
    auth_prod <- authorProdOverTime(clean_data, k = 10)
    
    # 绘制图形
    pdf(file = file.path(themes_dir, "author_productivity.pdf"), width = 12, height = 8)
    plot(auth_prod, las = 1)
    dev.off()
    
    # 作者优势指数
    dom <- dominance(basic_results, k = 10)
    write.csv(dom, file = file.path(themes_dir, "author_dominance.csv"), row.names = FALSE)
    
    # 保存分析结果
    save(auth_prod, file = file.path(themes_dir, "author_productivity.RData"))
    save(dom, file = file.path(themes_dir, "author_dominance.RData"))
    
    write_log("作者关系分析完成")
    return(list(productivity = auth_prod, dominance = dom))
  } else {
    write_log("作者数据不足，无法执行作者关系分析", "WARNING")
    return(NULL)
  }
}, error = function(e) {
  write_log(paste0("作者关系分析失败: ", e$message), "ERROR")
  return(NULL)
})

# 保存所有主题分析结果
theme_results <- list(
  thematic_map = thematic_map,
  conceptual_structure = conceptual_structure,
  historical_network = histNetwork,
  author_relations = authorRelations
)
save(theme_results, file = file.path(themes_dir, "theme_results.RData"))
write_log("主题分析完成并保存结果")

#-----------------------------------------------------------------------------
# 8. 数据质量报告
#-----------------------------------------------------------------------------
write_log("生成数据质量报告")

# 创建质量报告
quality_report <- data.frame(
  Metric = character(),
  Value = character(),
  Score = numeric(),
  stringsAsFactors = FALSE
)

# 原始数据量
quality_report <- rbind(quality_report, 
                         data.frame(Metric = "原始文献数量", 
                                    Value = as.character(original_count), 
                                    Score = 1.0))

# 去重后数据量
quality_report <- rbind(quality_report, 
                         data.frame(Metric = "清洗后文献数量", 
                                    Value = as.character(cleaned_count), 
                                    Score = cleaned_count / original_count))

# 重复率
quality_report <- rbind(quality_report, 
                         data.frame(Metric = "文献重复率", 
                                    Value = paste0(round((original_count - cleaned_count) / original_count * 100, 2), "%"), 
                                    Score = 1 - (original_count - cleaned_count) / original_count))

# 检查字段完整度
field_completeness <- function(df, field) {
  if (!(field %in% colnames(df))) return(c("缺失", 0))
  
  non_empty <- sum(!is.na(df[[field]]) & df[[field]] != "")
  completeness <- non_empty / nrow(df)
  
  return(c(paste0(round(completeness * 100, 2), "%"), completeness))
}

# 计算关键字段完整度
key_fields <- c("AU", "TI", "SO", "PY", "DI", "AB", "CR", "DE", "ID", "AU_CO")
field_names <- c("作者", "标题", "期刊", "年份", "DOI", "摘要", "引用", "作者关键词", "索引关键词", "作者国家")

for (i in 1:length(key_fields)) {
  field_result <- field_completeness(clean_data, key_fields[i])
  quality_report <- rbind(quality_report, 
                           data.frame(Metric = paste0(field_names[i], "字段完整度"), 
                                      Value = field_result[1], 
                                      Score = as.numeric(field_result[2])))
}

# 分析结果完整度
analysis_fields <- c(
  !is.null(basic_results),
  !is.null(coauth_network),
  !is.null(coref_network),
  !is.null(coupling_auth_network),
  !is.null(collab_country_network),
  !is.null(keywords_network),
  !is.null(thematic_map),
  !is.null(conceptual_structure),
  !is.null(histNetwork),
  !is.null(authorRelations)
)

analysis_names <- c(
  "基础描述分析",
  "共被引作者网络",
  "共被引参考文献网络",
  "作者耦合网络",
  "国家合作网络",
  "关键词共现网络",
  "主题映射分析",
  "概念结构分析",
  "历史引用流分析",
  "作者关系分析"
)

for (i in 1:length(analysis_fields)) {
  quality_report <- rbind(quality_report, 
                           data.frame(Metric = paste0(analysis_names[i], "结果"), 
                                      Value = ifelse(analysis_fields[i], "成功", "失败"), 
                                      Score = as.numeric(analysis_fields[i])))
}

# 计算总体得分
overall_score <- mean(quality_report$Score)
quality_report <- rbind(quality_report, 
                         data.frame(Metric = "总体质量得分", 
                                    Value = paste0(round(overall_score * 100, 2), "%"), 
                                    Score = overall_score))

# 保存质量报告
write.csv(quality_report, file = file.path(results_dir, "quality_report.csv"), row.names = FALSE)
write_log("数据质量报告已生成")

#-----------------------------------------------------------------------------
# 9. 保存与清理
#-----------------------------------------------------------------------------
# 保存整体分析环境
save.image(file = file.path(results_dir, "complete_analysis.RData"))
write_log("全部分析环境已保存")

# 输出主要文件位置
write_log("主要结果文件：")
write_log(paste0("- 基础分析摘要: ", file.path(tables_dir, "summary_results.txt")))
write_log(paste0("- 网络可视化: ", file.path(networks_dir, "network_plots.pdf")))
write_log(paste0("- 主题分析: ", file.path(themes_dir, "thematic_map.pdf")))
write_log(paste0("- 数据质量报告: ", file.path(results_dir, "quality_report.csv")))
write_log(paste0("- 分析日志: ", log_file))

# 显示质量评分
cat("\n=== 数据分析质量评分 ===\n")
cat(paste0("总体质量得分: ", round(overall_score * 100, 2), "%\n"))
cat(paste0("原始文献数量: ", original_count, "\n"))
cat(paste0("清洗后文献数量: ", cleaned_count, "\n"))
cat(paste0("文献重复率: ", round((original_count - cleaned_count) / original_count * 100, 2), "%\n"))

# 显示成功的分析
cat("\n=== 成功完成的分析 ===\n")
success_analysis <- analysis_names[analysis_fields]
cat(paste(success_analysis, collapse = "\n"))

# 显示失败的分析
failed_analysis <- analysis_names[!analysis_fields]
if(length(failed_analysis) > 0) {
  cat("\n=== 未能完成的分析 ===\n")
  cat(paste(failed_analysis, collapse = "\n"))
}

cat("\n主要分析结果已保存到", results_dir, "目录\n")
cat("分析日志保存在", log_file, "\n")

# 结束信息
write_log("文献计量学分析已完成")
print("分析已完成！") 