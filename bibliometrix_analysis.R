# 文献计量学多方法比较分析系统 - 改进版
# 使用bibliometrix进行文献计量学分析

# =============== 设置工作环境 ===============
# 加载必要的库
library(bibliometrix)
library(tidyverse)
library(igraph)
library(ggplot2)
library(Matrix)

# 设置工作目录和资源目录
current_dir <- getwd()
cat("当前工作目录已设置为:", current_dir, "\n")
data_dir <- "C:/Users/<USER>/Desktop/数据文件/citespace数据"
cat("找到原始数据目录:", data_dir, "\n")

# =============== 第一步：数据加载与格式转换 ===============
# 查找所有TXT文件
txt_files <- list.files(data_dir, pattern = "\\.txt$", full.names = TRUE)
cat("找到", length(txt_files), "个 .txt 文件，准备进行转换...\n")
print(basename(txt_files))

# 初始化结果存储
output_excel <- file.path(current_dir, "WoS_converted_data.xlsx")
output_rdata <- file.path(current_dir, "WoS_converted_data.RData")

# 使用tryCatch错误处理数据转换
cat("\n开始使用 bibliometrix 进行格式转换...\n")
tryCatch({
  # 转换WoS文件为bibliometrix数据框
  M <- convert2df(file = txt_files, dbsource = "wos", format = "plaintext")
  
  # 检查数据框维度
  orig_dim <- dim(M)
  
  # 检查和移除重复行
  duplicates <- duplicated(M$DI) & !is.na(M$DI)
  num_duplicates <- sum(duplicates)
  if(num_duplicates > 0) {
    M <- M[!duplicates,]
    cat("已移除", num_duplicates, "篇重复文档\n")
  }
  
  # 再次检查维度以确认去重效果
  final_dim <- dim(M)
  cat("转换完成。数据框 M 包含", final_dim[1], "条记录和", final_dim[2], "个字段。\n")
  
  # 诊断数据框内容
  cat("\n--- 诊断: M 对象 ---\n")
  cat("维度 (Dim_M): ", paste(dim(M), collapse = " x "), "\n")
  cat("列名 (Colnames_M): ", paste(names(M), collapse = ", "), "\n")
  
  # 打印前几行数据样本
  cat("前几行 AU (Authors_M_Head):\n")
  print(head(M$AU))
  cat("\n前几行 DE (AuthorKeywords_M_Head):\n")
  print(head(M$DE))
  cat("\n前几行 ID (KeywordsPlus_M_Head):\n")
  print(head(M$ID))
  cat("\n前几行 SO (Sources_M_Head):\n")
  print(head(M$SO))
  
  # 统计非空主要字段的数量
  cat("非空 AU 数量 (NonEmpty_AU_M): ", sum(!is.na(M$AU)), "\n")
  cat("非空 DE 数量 (NonEmpty_DE_M): ", sum(!is.na(M$DE)), "\n")
  cat("非空 ID 数量 (NonEmpty_ID_M): ", sum(!is.na(M$ID)), "\n")
  cat("非空 SO 数量 (NonEmpty_SO_M): ", sum(!is.na(M$SO)), "\n")
  cat("--- 诊断: M 对象结束 ---\n\n")
}, error = function(e) {
  cat("转换过程中发生错误:\n", e$message, "\n")
  stop("数据转换失败，请检查输入文件格式和路径")
})

# =============== 第二步：数据探索与缺失分析 ===============
# 分析字段缺失情况
missing_analysis <- function(M) {
  # 创建中文字段映射
  field_meanings <- c(
    AU = "作者", AF = "作者全名", CR = "引用参考文献", AB = "摘要",
    # ... 其他字段映射 ...
    DE = "作者关键词", ID = "关键词Plus"
  )
  
  # 计算每个字段的缺失值情况
  missing_summary <- tibble(
    Tag = names(M),
    n_miss = colSums(is.na(M) | M == ""),
    pct_miss = round(n_miss / nrow(M) * 100, 3)
  )
  
  # 添加中文含义
  missing_summary$Meaning <- field_meanings[missing_summary$Tag]
  missing_summary$Meaning[is.na(missing_summary$Meaning)] <- missing_summary$Tag[is.na(missing_summary$Meaning)]
  
  # 按缺失率降序排列
  missing_summary <- missing_summary %>% 
    arrange(desc(pct_miss)) %>%
    select(Tag, Meaning, n_miss, pct_miss)
  
  return(missing_summary)
}

# 执行缺失值分析
missing_summary <- missing_analysis(M)
cat("--- 字段缺失值分析 (按缺失比例降序排列) ---\n\n")
print(as_tibble(missing_summary), n = 75)  # 显示所有字段

# =============== 第三步：元数据字段提取与准备 ===============
# 确保AU_CO和AU_UN字段存在并正确
cat("\n--- 预处理机构和国家信息 ---\n")
tryCatch({
  # 检查C1字段是否存在且非空
  if("C1" %in% names(M) && sum(!is.na(M$C1)) > 0) {
    # 移除verbose参数，适应最新bibliometrix版本
    M <- metaTagExtraction(M, Field = "AU_UN")
    cat("已从C1提取作者机构 (AU_UN)\n")
    
    # 检查AU_CO是否存在，不存在则提取
    if(!"AU_CO" %in% names(M) || sum(!is.na(M$AU_CO)) < (nrow(M) * 0.5)) {
      M <- metaTagExtraction(M, Field = "AU_CO")
      cat("已从C1提取作者国家 (AU_CO)\n")
    }
  } else {
    cat("注意: C1字段缺失或大部分为空，机构和国家提取可能不完整\n")
  }
}, error = function(e) {
  cat("元数据提取错误:", e$message, "\n")
  cat("将尝试继续其他分析\n")
})

# 创建一个特殊用于合作网络分析的数据框
M_for_collaboration <- M
cat("已创建用于合作网络的独立数据框\n")

# =============== 第四步：基本文献计量分析 ===============
cat("\n--- 开始执行基本的文献计量分析 (biblioAnalysis) ---\n")
tryCatch({
  results <- biblioAnalysis(M, sep = ";")
  
  # 打印关键的结果对象结构
  cat("基本的文献计量分析计算完成。\n\n")
  cat("--- 诊断: results 对象 ---\n")
  cat("results 对象结构 (Structure_results):\n")
  print(str(results, max.level = 1))
  cat("results$Authors 前几行 (Authors_results_Head):\n")
  print(head(results$Authors))
  cat("results$Sources 前几行 (Sources_results_Head):\n")
  print(head(results$Sources))
  cat("--- 诊断: results 对象结束 ---\n\n")
  
  # 打印分析摘要
  cat("\n--- 文献计量分析结果摘要 (Top 10 用于控制台打印) ---\n\n")
  summary_results <- summary(results, k = 10)
  
  # 保存描述性统计表
  tables_dir <- file.path(current_dir, "tables")
  dir.create(tables_dir, showWarnings = FALSE)
  tryCatch({
    saveRDS(results, file.path(tables_dir, "biblioAnalysis_results.rds"))
    cat("\n--- 描述性统计表格 (所有项) 保存尝试完成 ---\n")
  }, error = function(e) {
    cat("保存描述性统计表格时出错:", e$message, "\n")
  })
}, error = function(e) {
  cat("文献计量分析错误:", e$message, "\n")
  cat("将尝试继续其他分析\n")
})

# =============== 第五步：网络分析 ===============
# 1. 关键词共现网络分析（使用矩阵方法而非直接网络构建）
cat("\n--- 开始计算作者关键词 (DE) 共现网络 ---\n")
tryCatch({
  # 打印DE字段的前20个唯一术语
  de_terms <- unique(unlist(strsplit(M$DE[!is.na(M$DE)], ";")))
  cat("Unique DE terms (first 20):\n")
  print(head(de_terms, 20))
  
  # 使用稳定的矩阵构建方法代替直接网络构建
  de_dtm <- cocMatrix(M, Field = "DE", type = "matrix", sep = ";", 
                       remove.terms = NULL, binary = FALSE)
  cat("作者关键词的文档-词语矩阵 (DTM) 已完成. 维度:", paste(dim(de_dtm), collapse = " x "), "\n")
  saveRDS(de_dtm, file.path(tables_dir, "DE_document_term_matrix.rds"))
  
  # 创建共现矩阵（转置与自身相乘）
  de_cooc_matrix <- Matrix::crossprod(de_dtm)
  saveRDS(de_cooc_matrix, file.path(tables_dir, "DE_cooc_matrix.rds"))
  cat("作者关键词的共现矩阵已保存.\n")
}, error = function(e) {
  cat("关键词共现分析错误:", e$message, "\n")
  cat("将尝试继续其他分析\n")
})

# 2. 关键词Plus共现网络
cat("\n--- 开始计算 Keywords Plus (ID) 共现网络 ---\n")
tryCatch({
  # 打印ID字段的前20个唯一术语
  id_terms <- unique(unlist(strsplit(M$ID[!is.na(M$ID)], ";")))
  cat("Unique ID terms (first 20):\n")
  print(head(id_terms, 20))
  
  # 使用稳定的矩阵构建方法
  id_dtm <- cocMatrix(M, Field = "ID", type = "matrix", sep = ";", 
                       remove.terms = NULL, binary = FALSE)
  cat("Keywords Plus 的文档-词语矩阵 (DTM) 已完成. 维度:", paste(dim(id_dtm), collapse = " x "), "\n")
  saveRDS(id_dtm, file.path(tables_dir, "ID_document_term_matrix.rds"))
  
  # 创建共现矩阵
  id_cooc_matrix <- Matrix::crossprod(id_dtm)
  saveRDS(id_cooc_matrix, file.path(tables_dir, "ID_cooc_matrix.rds"))
  cat("Keywords Plus 的共现矩阵已保存.\n")
  
  # 生成主题图
  cat("\n--- 开始生成主题图 (Thematic Map - ID) ---\n")
  thematicMap_results <- tryCatch({
    thematicMap(M, field = "ID", n = 250, minfreq = 5, 
                remove.terms = NULL, cluster = "walktrap", 
                n.labels = 5, repel = TRUE)
  }, error = function(e) {
    cat("主题图生成错误:", e$message, "\n")
    return(NULL)
  })
  if(!is.null(thematicMap_results)) {
    saveRDS(thematicMap_results, file.path(tables_dir, "thematicMap_ID_results.rds"))
    cat("主题图成功生成并保存\n")
  }
}, error = function(e) {
  cat("Keywords Plus 分析错误:", e$message, "\n")
  cat("将尝试继续其他分析\n")
})

# 3. 文献共被引网络
cat("\n--- 开始计算文献共被引网络 ---\n")
tryCatch({
  # 确认CR字段的状态
  cr_complete <- sum(!is.na(M$CR) & M$CR != "") / nrow(M) * 100
  cat("CR字段完整度 (百分比):", round(cr_complete, 2), "%\n")
  
  if(cr_complete > 70) {  # 如果CR字段完整度超过70%
    # 使用cocMatrix而非直接biblioNetwork
    cr_dtm <- cocMatrix(M, Field = "CR", type = "matrix", sep = ";", binary = FALSE)
    cat("引文文档-术语矩阵 (DTM) 已完成. 维度:", paste(dim(cr_dtm), collapse = " x "), "\n")
    
    # 创建共被引矩阵
    cr_cocit_matrix <- Matrix::crossprod(cr_dtm)
    saveRDS(cr_cocit_matrix, file.path(tables_dir, "CR_cocitation_matrix.rds"))
    cat("文献共被引网络矩阵已保存.\n")
  } else {
    cat("注意: CR字段不够完整，可能影响共被引分析\n")
  }
}, error = function(e) {
  cat("文献共被引分析错误:", e$message, "\n")
  cat("将尝试继续其他分析\n")
})

# 4. 作者文献耦合网络
cat("\n--- 开始计算作者文献耦合网络 ---\n")
tryCatch({
  # 使用矩阵构建方法代替直接网络构建
  if(cr_complete > 70) {  # 仅在CR字段足够完整时执行
    # 获取作者-文档矩阵和文档-引用矩阵
    au_dtm <- cocMatrix(M, Field = "AU", type = "matrix", sep = ";", binary = FALSE)
    
    # 计算中间的联合矩阵: 作者-引用
    au_cr_matrix <- au_dtm %*% cr_dtm
    
    # 计算耦合矩阵: 作者-作者（基于共同引用）
    au_coupling_matrix <- au_cr_matrix %*% t(au_cr_matrix)
    
    saveRDS(au_coupling_matrix, file.path(tables_dir, "AU_coupling_matrix.rds"))
    cat("作者文献耦合网络矩阵已保存.\n")
  } else {
    cat("注意: CR字段不够完整，跳过作者文献耦合分析\n")
  }
}, error = function(e) {
  cat("作者文献耦合分析错误:", e$message, "\n")
  cat("将尝试继续其他分析\n")
})

# 5. 期刊文献耦合网络
cat("\n--- 开始计算期刊文献耦合网络 ---\n")
tryCatch({
  # 使用矩阵构建方法
  if(cr_complete > 70) {  # 仅在CR字段足够完整时执行
    # 获取期刊-文档矩阵
    so_dtm <- cocMatrix(M, Field = "SO", type = "matrix", sep = ";", binary = FALSE)
    
    # 计算中间的联合矩阵: 期刊-引用
    so_cr_matrix <- so_dtm %*% cr_dtm
    
    # 计算耦合矩阵: 期刊-期刊（基于共同引用）
    so_coupling_matrix <- so_cr_matrix %*% t(so_cr_matrix)
    
    saveRDS(so_coupling_matrix, file.path(tables_dir, "SO_coupling_matrix.rds"))
    cat("期刊文献耦合网络矩阵已保存.\n")
  } else {
    cat("注意: CR字段不够完整，跳过期刊文献耦合分析\n")
  }
}, error = function(e) {
  cat("期刊文献耦合分析错误:", e$message, "\n")
  cat("将尝试继续其他分析\n")
})

# 6. 作者合作网络
cat("\n--- 开始计算作者合作网络 ---\n")
tryCatch({
  # 获取作者-文档矩阵
  au_dtm <- cocMatrix(M, Field = "AU", type = "matrix", sep = ";", binary = FALSE)
  
  # 计算合作矩阵
  au_collab_matrix <- Matrix::crossprod(au_dtm)
  
  saveRDS(au_collab_matrix, file.path(tables_dir, "AU_collaboration_matrix.rds"))
  cat("作者合作网络矩阵已保存.\n")
}, error = function(e) {
  cat("作者合作网络分析错误:", e$message, "\n")
  cat("将尝试继续其他分析\n")
})

# 7. 机构合作网络（修复WA对象问题）
cat("\n--- 开始计算机构合作网络 ---\n")
tryCatch({
  # 检查AU_UN字段存在且非空
  if("AU_UN" %in% names(M_for_collaboration) && 
     sum(!is.na(M_for_collaboration$AU_UN) & M_for_collaboration$AU_UN != "") > (nrow(M_for_collaboration) * 0.5)) {
    
    # 使用矩阵方法构建机构合作网络
    un_dtm <- cocMatrix(M_for_collaboration, Field = "AU_UN", type = "matrix", sep = ";", binary = FALSE)
    un_collab_matrix <- Matrix::crossprod(un_dtm)
    
    saveRDS(un_collab_matrix, file.path(tables_dir, "UN_collaboration_matrix.rds"))
    cat("机构合作网络矩阵已保存.\n")
  } else {
    cat("机构信息不足以构建有效的合作网络\n")
  }
}, error = function(e) {
  cat("机构合作网络分析错误:", e$message, "\n")
  cat("将尝试继续其他分析\n")
})

# 8. 国家合作网络
cat("\n--- 开始计算国家合作网络 ---\n")
tryCatch({
  # 检查AU_CO字段存在且非空
  if("AU_CO" %in% names(M_for_collaboration) && 
     sum(!is.na(M_for_collaboration$AU_CO) & M_for_collaboration$AU_CO != "") > (nrow(M_for_collaboration) * 0.5)) {
    
    # 使用矩阵方法构建国家合作网络
    co_dtm <- cocMatrix(M_for_collaboration, Field = "AU_CO", type = "matrix", sep = ";", binary = FALSE)
    co_collab_matrix <- Matrix::crossprod(co_dtm)
    
    saveRDS(co_collab_matrix, file.path(tables_dir, "CO_collaboration_matrix.rds"))
    cat("国家合作网络矩阵已保存.\n")
    
    # 生成国家合作网络可视化
    if(exists("results") && "CountryCollaboration" %in% names(results)) {
      countries_data <- results$CountryCollaboration
      saveRDS(countries_data, file.path(tables_dir, "country_collaboration_data.rds"))
      cat("国家合作数据已保存用于可视化.\n")
    } else {
      cat("注意: 没有可用的国家合作数据用于可视化\n")
    }
  } else {
    cat("国家信息不足以构建有效的合作网络\n")
  }
}, error = function(e) {
  cat("国家合作网络分析错误:", e$message, "\n")
  cat("将尝试继续其他分析\n")
})

# =============== 第六步：历史直接引文分析 ===============
cat("\n--- 开始进行历史直接引文分析 (Historiograph) ---\n")
tryCatch({
  # 检查CR字段状态和格式
  if(cr_complete > 90) {  # 需要非常完整的引文数据
    # 先检查DI字段是否存在且非空
    if("DI" %in% names(M) && sum(!is.na(M$DI)) > (nrow(M) * 0.5)) {
      # 尝试使用修复的历史引文分析方法
      histResults <- histNetwork(M, min.citations = 2, sep = ";")
      
      if(!is.null(histResults) && !identical(histResults, NA)) {
        saveRDS(histResults, file.path(tables_dir, "historiograph_results.rds"))
        cat("历史直接引文分析结果已保存.\n")
        
        # 尝试生成历史图谱
        histPlotResults <- tryCatch({
          histPlot(histResults, n = 30, size = 15, remove.isolates = TRUE)
        }, error = function(e) {
          cat("历史图谱生成错误:", e$message, "\n")
          return(NULL)
        })
        
        if(!is.null(histPlotResults)) {
          saveRDS(histPlotResults, file.path(tables_dir, "histPlot_results.rds"))
          cat("历史图谱已生成并保存.\n")
        }
      } else {
        cat("历史引文分析返回空结果\n")
      }
    } else {
      cat("DI字段(DOI)不足以进行有效的历史引文分析\n")
    }
  } else {
    cat("引文数据不够完整，无法进行有效的历史引文分析\n")
  }
}, error = function(e) {
  cat("历史直接引文分析错误:", e$message, "\n")
  cat("将尝试继续其他分析\n")
})

# =============== 第七步：概念结构分析 ===============
cat("\n--- 开始进行概念结构分析 (MCA - ID) ---\n")
tryCatch({
  # 使用关键词Plus进行概念结构分析
  cs_results <- conceptualStructure(M, field = "ID", method = "CA", 
                                    minDegree = 5, k.max = 8, 
                                    stemming = FALSE, n.terms = 50)
  
  if(!is.null(cs_results)) {
    saveRDS(cs_results, file.path(tables_dir, "conceptualStructure_ID_results.rds"))
    cat("关键词Plus的概念结构分析结果已保存.\n")
  } else {
    cat("概念结构分析返回空结果\n")
  }
}, error = function(e) {
  cat("概念结构分析 (ID) 错误:", e$message, "\n")
  
  # 尝试使用作者关键词替代
  cat("\n--- 尝试执行概念因子分析 (CA - DE) ---\n")
  tryCatch({
    cs_de_results <- conceptualStructure(M, field = "DE", method = "CA", 
                                         minDegree = 5, k.max = 8, 
                                         stemming = FALSE, n.terms = 50)
    
    if(!is.null(cs_de_results)) {
      saveRDS(cs_de_results, file.path(tables_dir, "conceptualStructure_DE_results.rds"))
      cat("作者关键词的概念结构分析结果已保存.\n")
    } else {
      cat("作者关键词的概念结构分析返回空结果\n")
    }
  }, error = function(e) {
    cat("概念结构分析 (DE) 错误:", e$message, "\n")
    cat("将尝试继续其他分析\n")
  })
})

# =============== 第八步：保存最终结果 ===============
cat("\n--- 保存所有分析结果 ---\n")
tryCatch({
  # 创建结果目录
  results_dir <- file.path(current_dir, "results")
  dir.create(results_dir, showWarnings = FALSE)
  
  # 保存主要数据对象
  save(M, file = file.path(results_dir, "M_processed.RData"))
  if(exists("results")) save(results, file = file.path(results_dir, "biblioAnalysis_results.RData"))
  
  # 保存缺失值分析
  write.csv(missing_summary, file.path(results_dir, "missing_fields_analysis.csv"), row.names = FALSE)
  
  cat("主要分析结果已保存到", results_dir, "目录\n")
}, error = function(e) {
  cat("保存结果时出错:", e$message, "\n")
})

cat("\n--- 研究流程脚本执行完毕 ---\n")