代码说明
这个脚本执行以下主要功能：

1. 数据转换功能
读取指定目录下的所有Web of Science文本文件
使用bibliometrix的convert2df函数将每个文件转换为数据框
合并所有数据框为一个统一的数据集
保存合并后的数据为RDATA格式
2. 质量评估功能
字段完整性分析：检查关键字段的存在性和完整性
引用参考文献(CR)评估：
分析引用数量和独特引用数
评估引用格式的标准化程度(卷号、页码、DOI等)
可视化数据质量：
生成年度出版物数量图表
创建作者合作网络图
生成关键词共现网络图
3. 综合评分系统
对数据质量进行量化评分(满分5分)，评估维度包括：

数据量充足性
字段完整性
引用参考文献完整性
引用格式标准化程度
时间跨度广度
4. 详细报告输出
文本日志：记录整个处理过程
转换日志Excel：记录每个文件的转换结果
字段完整性报告：详细分析主要字段的存在率
数据质量评估报告：提供全面评估结果和建议
可视化图表：生成多种可视化输出以直观展示数据特征
处理流程
脚本首先扫描指定目录中的所有Web of Science文本文件
逐个读取并分析每个文件，提取文档数量和字段信息
将所有成功转换的文件合并为一个统一的数据集
对合并后的数据进行多维度质量评估
生成综合评分和详细报告
保存结果为RDATA文件，便于后续文献计量分析
评估维度
该脚本评估的主要数据质量维度包括：

数据量：总文档数是否足够进行有意义的分析
字段完整性：核心字段(AU、TI、SO、PY、AB、CR)的存在和完整率
引用质量：引用参考文献的完整性和标准化程度
时间跨度：数据覆盖的时间范围
格式一致性：关键格式元素的标准化程度
使用方法
确保已安装bibliometrix、dplyr、ggplot2和openxlsx包
设置好输入和输出路径
运行脚本
查看生成的报告和图表
这个脚本不仅将数据转换为bibliometrix可用的格式，还提供了全面的数据质量评估，帮助您了解数据的优势和局限性，从而可以更好地选择适合的分析方法。
代码说明
这个修订后的脚本使用了bibliometrix的推荐方法来处理多个Web of Science文件，确保最终只生成一个合并的RDATA文件。主要改进包括：

1. 使用bibliometrix推荐的合并策略
与其单独解析每个文件然后尝试合并数据框（这会导致列不一致问题），我们先将所有文本文件合并为一个大的文本文件，然后使用bibliometrix的convert2df函数一次性处理整个文件。这种方法可以确保：

所有记录使用相同的字段结构
避免合并不同数据框时的列不一致问题
与bibliometrix推荐的数据处理流程保持一致
2. 清理处理流程
删除旧的RData文件：避免与先前生成的文件混淆
文本文件合并：将所有WoS文件合并为一个大的文本文件
确保文件格式正确：检查并添加缺失的ER标记
一次性转换：使用bibliometrix直接处理合并后的文件
保存单一RData文件：使用标准命名（wos_data）保存
3. 增强的数据质量评估
字段完整性分析：详细检查每个关键字段的完整率
引用分析：评估引用格式的一致性
可视化输出：生成多种标准的bibliometrix可视化
4. 额外的辅助工具
生成biblioshiny启动脚本：便于使用图形界面进行分析
提供示例代码：在评估报告中包含基本的分析代码
详细的建议和局限性：根据数据质量提供有针对性的建议
主要改进点
解决列不一致问题：使用bibliometrix标准流程处理多个文件
单一输出文件：只生成一个合并的RDATA文件
更加健壮：改进错误处理和日志记录
符合标准流程：遵循bibliometrix推荐的数据处理方法
更全面的评估：提供更详细的数据质量评估和建议
使用方法
确保已安装所有需要的包：bibliometrix、dplyr、ggplot2和openxlsx
运行修改后的脚本
查看生成的wos_data.RData文件
使用生成的biblioshiny启动脚本进行交互式分析
该脚本符合您希望生成一个文件的要求，并使用bibliometrix的标准处理方法解决了列不一致的问题。生成的RDATA文件可以直接用于bibliometrix包的所有分析功能，包括biblioshiny交互式界面。


# 加载必要的包
if (!require("bibliometrix")) install.packages("bibliometrix")
if (!require("dplyr")) install.packages("dplyr")
if (!require("ggplot2")) install.packages("ggplot2")
if (!require("openxlsx")) install.packages("openxlsx")

library(bibliometrix)
library(dplyr)
library(ggplot2)
library(openxlsx)

# 设置文件路径
input_path <- "C:/Users/<USER>/Desktop/实验用数据/整合后文件_仅原始字段"
output_path <- input_path  # 输出到同一目录

# 创建日志文件
log_file <- file.path(output_path, "convert_to_rdata_log.txt")
excel_log_file <- file.path(output_path, "convert_to_rdata_log.xlsx")
cat("", file = log_file)  # 清空日志文件

# 日志函数
log_message <- function(message) {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  log_entry <- paste0(timestamp, " - ", message)
  cat(log_entry, "\n", file = log_file, append = TRUE)
  cat(log_entry, "\n")
}

log_message("开始将Web of Science数据转换为单一bibliometrix RDATA格式")

# 列出输入目录中的所有txt文件，排除日志文件
wos_files <- list.files(input_path, pattern = "\\.txt$", full.names = TRUE)
wos_files <- wos_files[!grepl("convert_to_rdata_log\\.txt", wos_files)]
wos_files <- wos_files[!grepl("integration_log\\.txt", wos_files)]
wos_files <- wos_files[!grepl("detailed_operations_log\\.txt", wos_files)]
wos_files <- wos_files[!grepl("bibliometrix_summary\\.txt", wos_files)]
wos_files <- wos_files[!grepl("data_quality_evaluation\\.txt", wos_files)]

log_message(paste("在目录中找到", length(wos_files), "个Web of Science文本文件"))

# 初始化日志数据框
conversion_log <- data.frame(
  FileName = character(),
  DocCount = integer(),
  FieldCount = integer(),
  Status = character(),
  ErrorMessage = character(),
  stringsAsFactors = FALSE
)

# 清理之前可能生成的RData文件(避免混淆)
old_rdata_files <- list.files(output_path, pattern = "\\.RData$", full.names = TRUE)
if (length(old_rdata_files) > 0) {
  log_message(paste("删除", length(old_rdata_files), "个旧的RData文件"))
  file.remove(old_rdata_files)
}

# 合并所有文件为一个大的文本文件
log_message("步骤1: 合并所有Web of Science文本文件为一个大文件")
combined_txt_file <- file.path(output_path, "all_wos_combined.txt")

# 先清空合并文件
file.create(combined_txt_file)

# 循环处理每个文件并追加到合并文件
for (i in 1:length(wos_files)) {
  file_path <- wos_files[i]
  file_name <- basename(file_path)
  
  log_message(paste("  处理文件", i, "/", length(wos_files), ":", file_name))
  
  # 读取文件内容
  tryCatch({
    file_content <- readLines(file_path, warn = FALSE)
    
    # 检查文件最后一行是否是ER
    if (length(file_content) > 0 && file_content[length(file_content)] != "ER") {
      log_message(paste("    警告: 文件", file_name, "的最后一行不是'ER'，添加'ER'行"))
      file_content <- c(file_content, "ER")
    }
    
    # 追加到合并文件
    cat(file_content, file = combined_txt_file, sep = "\n", append = TRUE)
    
    # 记录到日志
    conversion_log <- rbind(conversion_log, data.frame(
      FileName = file_name,
      DocCount = NA,  # 暂时未知
      FieldCount = NA,  # 暂时未知
      Status = "已合并",
      ErrorMessage = "",
      stringsAsFactors = FALSE
    ))
    
  }, error = function(e) {
    log_message(paste("    错误处理文件:", e$message))
    
    # 记录到日志
    conversion_log <- rbind(conversion_log, data.frame(
      FileName = file_name,
      DocCount = NA,
      FieldCount = NA,
      Status = "合并失败",
      ErrorMessage = e$message,
      stringsAsFactors = FALSE
    ))
  })
}

log_message(paste("完成合并文本文件，合并文件大小:", file.size(combined_txt_file)/1024/1024, "MB"))

# 使用bibliometrix的convert2df函数处理合并后的文件
log_message("步骤2: 将合并的文本文件转换为bibliometrix数据框")

tryCatch({
  # 使用bibliometrix直接读取合并后的文件
  M <- convert2df(file = combined_txt_file, dbsource = "wos", format = "plaintext")
  
  # 更新日志
  doc_count <- nrow(M)
  field_count <- ncol(M)
  
  log_message(paste("  成功读取", doc_count, "篇文档，包含", field_count, "个字段"))
  
  # 保存为单一RData文件
  rdata_file <- file.path(output_path, "wos_data.RData")
  
  # 保存为bibliometrix标准命名的对象
  wos_data <- M
  save(wos_data, file = rdata_file)
  log_message(paste("  已保存合并数据到:", rdata_file))
  
  # 更新转换日志中的文档数和字段数
  idx <- which(conversion_log$Status == "已合并")
  if (length(idx) > 0) {
    conversion_log$DocCount[idx] <- doc_count / length(idx)  # 平均分配文档数
    conversion_log$FieldCount[idx] <- field_count
  }
  
  # 使用bibliometrix包进行数据质量评估
  log_message("步骤3: 评估数据质量")
  
  # 1. 生成基本描述性统计
  results <- tryCatch({
    biblioAnalysis(wos_data, sep = ";")
  }, error = function(e) {
    log_message(paste("  执行biblioAnalysis时出错:", e$message))
    NULL
  })
  
  if (!is.null(results)) {
    log_message("  已完成bibliometrix的基本分析")
    
    # 创建基本描述性统计摘要
    summary_file <- file.path(output_path, "bibliometrix_summary.txt")
    
    summary_text <- capture.output({
      summary(results, k = 10)
    })
    
    writeLines(summary_text, summary_file)
    log_message(paste("  已保存基本统计摘要到:", summary_file))
    
    # 2. 检查关键字段的完整性
    # 重点字段：AU, TI, SO, PY, AB, CR
    key_fields <- c("AU", "TI", "SO", "PY", "AB", "CR")
    field_completeness <- data.frame(
      Field = character(),
      Present = integer(),
      Missing = integer(),
      Percentage = numeric(),
      stringsAsFactors = FALSE
    )
    
    for (field in key_fields) {
      if (field %in% names(wos_data)) {
        missing_count <- sum(is.na(wos_data[[field]]) | wos_data[[field]] == "")
        present_count <- nrow(wos_data) - missing_count
        percentage <- round(present_count / nrow(wos_data) * 100, 2)
        
        field_completeness <- rbind(field_completeness, data.frame(
          Field = field,
          Present = present_count,
          Missing = missing_count,
          Percentage = percentage,
          stringsAsFactors = FALSE
        ))
        
        log_message(paste("  字段", field, "完整率:", percentage, "%"))
      } else {
        field_completeness <- rbind(field_completeness, data.frame(
          Field = field,
          Present = 0,
          Missing = nrow(wos_data),
          Percentage = 0,
          stringsAsFactors = FALSE
        ))
        
        log_message(paste("  字段", field, "在数据中不存在"))
      }
    }
    
    # 保存字段完整性数据
    completeness_file <- file.path(output_path, "field_completeness.xlsx")
    wb <- createWorkbook()
    addWorksheet(wb, "字段完整性")
    writeData(wb, "字段完整性", field_completeness)
    saveWorkbook(wb, completeness_file, overwrite = TRUE)
    log_message(paste("  已保存字段完整性报告到:", completeness_file))
    
    # 3. 评估引用参考文献的标准化情况
    if ("CR" %in% names(wos_data)) {
      # 提取所有引用参考文献
      all_refs <- unlist(strsplit(wos_data$CR[!is.na(wos_data$CR) & wos_data$CR != ""], ";"))
      all_refs <- trimws(all_refs)
      all_refs <- all_refs[all_refs != ""]
      
      # 计算引用参考文献的基本统计
      ref_count <- length(all_refs)
      unique_ref_count <- length(unique(all_refs))
      
      log_message(paste("  引用参考文献总数:", ref_count))
      log_message(paste("  唯一引用参考文献数:", unique_ref_count))
      
      # 检查标准格式 (DOI, 年份, 卷号, 页码的格式一致性)
      # 使用样本分析 - 查看前100个参考文献
      sample_size <- min(100, length(all_refs))
      sample_refs <- all_refs[1:sample_size]
      
      # 检查格式一致性
      has_volume <- sum(grepl("V[0-9]+", sample_refs))
      has_page <- sum(grepl("P[0-9]+", sample_refs))
      has_doi <- sum(grepl("DOI", sample_refs, ignore.case = TRUE))
      has_year <- sum(grepl("[0-9]{4}", sample_refs))
      
      log_message(paste("  样本引用中包含卷号格式(V数字)的比例:", round(has_volume/sample_size*100, 2), "%"))
      log_message(paste("  样本引用中包含页码格式(P数字)的比例:", round(has_page/sample_size*100, 2), "%"))
      log_message(paste("  样本引用中包含DOI的比例:", round(has_doi/sample_size*100, 2), "%"))
      log_message(paste("  样本引用中包含年份的比例:", round(has_year/sample_size*100, 2), "%"))
      
      # 保存引用标准化评估
      ref_stats <- data.frame(
        Metric = c("总引用数", "唯一引用数", "包含卷号比例", "包含页码比例", "包含DOI比例", "包含年份比例"),
        Value = c(ref_count, unique_ref_count, 
                 round(has_volume/sample_size*100, 2),
                 round(has_page/sample_size*100, 2),
                 round(has_doi/sample_size*100, 2),
                 round(has_year/sample_size*100, 2)),
        stringsAsFactors = FALSE
      )
      
      # 添加到Excel文件
      addWorksheet(wb, "引用参考文献统计")
      writeData(wb, "引用参考文献统计", ref_stats)
      saveWorkbook(wb, completeness_file, overwrite = TRUE)
    } else {
      log_message("  引用参考文献(CR)字段在数据中不存在")
    }
    
    # 4. 尝试生成bibliometrix标准可视化
    log_message("步骤4: 生成标准可视化")
    
    tryCatch({
      # 年度出版物数量
      if ("PY" %in% names(wos_data)) {
        png(file.path(output_path, "annual_production.png"), width = 800, height = 600)
        histPlot(results, k = 10, pause = FALSE)
        dev.off()
        log_message("  已保存年度出版物数量图表")
      }
    }, error = function(e) {
      log_message(paste("  生成年度出版物数量图表时出错:", e$message))
    })
    
    tryCatch({
      # 作者合作网络
      if (all(c("AU", "PY") %in% names(wos_data))) {
        # 创建作者合作网络
        net_matrix <- biblioNetwork(wos_data, analysis = "collaboration", network = "authors", sep = ";")
        
        # 保存网络图
        png(file.path(output_path, "author_collaboration.png"), width = 1000, height = 1000)
        networkPlot(net_matrix, n = 30, type = "auto", title = "作者合作网络", labelsize = 0.8)
        dev.off()
        log_message("  已保存作者合作网络图")
      }
    }, error = function(e) {
      log_message(paste("  生成作者合作网络时出错:", e$message))
    })
    
    tryCatch({
      # 关键词共现网络
      if ("DE" %in% names(wos_data) || "ID" %in% names(wos_data)) {
        keyword_field <- ifelse("DE" %in% names(wos_data), "DE", "ID")
        
        # 创建关键词共现网络
        net_matrix <- biblioNetwork(wos_data, analysis = "co-occurrences", 
                                     network = "keywords", sep = ";")
        
        # 保存网络图
        png(file.path(output_path, "keyword_cooccurrence.png"), width = 1000, height = 1000)
        networkPlot(net_matrix, n = 30, type = "auto", title = "关键词共现网络", labelsize = 0.8)
        dev.off()
        log_message("  已保存关键词共现网络图")
      }
    }, error = function(e) {
      log_message(paste("  生成关键词共现网络时出错:", e$message))
    })
    
    # 5. 生成主题地图
    tryCatch({
      if (all(c("AU", "TI", "AB", "DE") %in% names(wos_data)) || 
          all(c("AU", "TI", "AB", "ID") %in% names(wos_data))) {
        
        # 确定使用的关键词字段
        keyword_field <- ifelse("DE" %in% names(wos_data), "DE", "ID")
        
        # 生成概念结构地图
        png(file.path(output_path, "thematic_map.png"), width = 1200, height = 1000)
        thematicMap(wos_data, field = keyword_field, n = 250, minfreq = 5, stemming = FALSE, 
                    size = 0.7, n.labels = 5, repel = TRUE)
        dev.off()
        log_message("  已保存主题地图")
      }
    }, error = function(e) {
      log_message(paste("  生成主题地图时出错:", e$message))
    })
    
    # 6. 评估总结
    quality_score <- 0
    max_score <- 5
    quality_notes <- character()
    
    # 基于文档数量评分
    if (doc_count >= 1000) {
      quality_score <- quality_score + 1
      quality_notes <- c(quality_notes, "数据量充足")
    } else if (doc_count >= 100) {
      quality_score <- quality_score + 0.5
      quality_notes <- c(quality_notes, "数据量一般")
    } else {
      quality_notes <- c(quality_notes, "数据量不足")
    }
    
    # 基于字段完整性评分
    field_score <- sum(field_completeness$Percentage > 90) / nrow(field_completeness)
    if (field_score > 0.8) {
      quality_score <- quality_score + 1
      quality_notes <- c(quality_notes, "字段完整性高")
    } else if (field_score > 0.5) {
      quality_score <- quality_score + 0.5
      quality_notes <- c(quality_notes, "字段完整性一般")
    } else {
      quality_notes <- c(quality_notes, "字段完整性低")
    }
    
    # 引用参考文献评分
    if ("CR" %in% names(wos_data)) {
      cr_completeness <- field_completeness$Percentage[field_completeness$Field == "CR"]
      if (cr_completeness > 90) {
        quality_score <- quality_score + 1
        quality_notes <- c(quality_notes, "引用参考文献完整性高")
      } else if (cr_completeness > 50) {
        quality_score <- quality_score + 0.5
        quality_notes <- c(quality_notes, "引用参考文献完整性一般")
      } else {
        quality_notes <- c(quality_notes, "引用参考文献完整性低")
      }
      
      # 如果有引用标准化评估，增加评分
      if (exists("ref_stats")) {
        format_score <- (ref_stats$Value[3] + ref_stats$Value[4]) / 200  # 卷号和页码格式的平均比例
        if (format_score > 0.7) {
          quality_score <- quality_score + 1
          quality_notes <- c(quality_notes, "引用格式标准化程度高")
        } else if (format_score > 0.4) {
          quality_score <- quality_score + 0.5
          quality_notes <- c(quality_notes, "引用格式标准化程度一般")
        } else {
          quality_notes <- c(quality_notes, "引用格式标准化程度低")
        }
      }
    } else {
      quality_notes <- c(quality_notes, "缺少引用参考文献字段")
    }
    
    # 时间跨度评分
    if ("PY" %in% names(wos_data)) {
      years <- as.numeric(wos_data$PY)
      years <- years[!is.na(years)]
      if (length(years) > 0) {
        time_span <- max(years) - min(years)
        
        if (time_span >= 20) {
          quality_score <- quality_score + 1
          quality_notes <- c(quality_notes, paste("时间跨度广(", time_span, "年)", sep=""))
        } else if (time_span >= 10) {
          quality_score <- quality_score + 0.5
          quality_notes <- c(quality_notes, paste("时间跨度一般(", time_span, "年)", sep=""))
        } else {
          quality_notes <- c(quality_notes, paste("时间跨度窄(", time_span, "年)", sep=""))
        }
      } else {
        quality_notes <- c(quality_notes, "年份数据有问题")
      }
    } else {
      quality_notes <- c(quality_notes, "缺少出版年份字段")
    }
    
    # 计算最终得分百分比
    quality_percentage <- round(quality_score / max_score * 100, 1)
    
    # 生成总结报告
    evaluation_report <- file.path(output_path, "data_quality_evaluation.txt")
    
    report_content <- c(
      "======================================================",
      "            Web of Science数据质量评估报告            ",
      "======================================================",
      "",
      paste("评估时间:", format(Sys.time(), "%Y-%m-%d %H:%M:%S")),
      "",
      paste("总文件数:", length(wos_files)),
      paste("总文档数:", doc_count),
      "",
      "数据质量评分:",
      paste("  得分:", quality_score, "/", max_score, "(", quality_percentage, "%)", sep=""),
      "",
      "评估结果:",
      paste("  •", quality_notes),
      "",
      "字段完整性:",
      paste("  •", field_completeness$Field, ":", field_completeness$Percentage, "%"),
      "",
      "建议:",
      ifelse(quality_percentage >= 80, 
            "  数据质量优秀，适合进行全面的文献计量分析", 
            ifelse(quality_percentage >= 60,
                  "  数据质量良好，可以进行基本的文献计量分析，但某些高级分析可能受限",
                  "  数据质量不佳，建议改进数据质量后再进行文献计量分析")),
      "",
      ifelse("CR" %in% names(wos_data) && field_completeness$Percentage[field_completeness$Field == "CR"] < 50,
            "  • 引用参考文献完整性较低，引文分析和共被引分析可能不准确",
            ""),
      ifelse(!all(c("DE", "ID") %in% names(wos_data)),
            "  • 缺少完整的关键词字段，关键词共现分析可能不全面",
            ""),
      ifelse(!("AB" %in% names(wos_data)),
            "  • 缺少摘要字段，无法进行文本挖掘和主题建模",
            ""),
      "",
      "可执行的分析:",
      paste("  •", ifelse("PY" %in% names(wos_data), "年度趋势分析", "年度趋势分析(不可用)")),
      paste("  •", ifelse("AU" %in% names(wos_data), "作者分析", "作者分析(不可用)")),
      paste("  •", ifelse("SO" %in% names(wos_data), "期刊分析", "期刊分析(不可用)")),
      paste("  •", ifelse(all(c("AU", "PY") %in% names(wos_data)), "作者合作网络", "作者合作网络(不可用)")),
      paste("  •", ifelse(any(c("DE", "ID") %in% names(wos_data)), "关键词分析", "关键词分析(不可用)")),
      paste("  •", ifelse("CR" %in% names(wos_data) && field_completeness$Percentage[field_completeness$Field == "CR"] > 50, 
                      "引文分析", "引文分析(不可用)")),
      paste("  •", ifelse("AB" %in% names(wos_data), "文本挖掘和主题建模", "文本挖掘和主题建模(不可用)")),
      "",
      "bibliometrix示例代码:",
      "```r",
      "library(bibliometrix)",
      paste("load('", basename(rdata_file), "')", sep=""),
      "results <- biblioAnalysis(wos_data, sep = ';')",
      "summary(results, k = 10)",
      "# 创建共被引网络",
      "NetMatrix <- biblioNetwork(wos_data, analysis = 'co-citation', network = 'references', sep = ';')",
      "# 绘制网络图",
      "net <- networkPlot(NetMatrix, n = 50, type = 'fruchterman', title = '共被引网络')",
      "```",
      "",
      "参考资源:",
      "  • bibliometrix文档: https://www.bibliometrix.org/",
      "  • bibliometrix教程: https://www.bibliometrix.org/vignettes/Introduction_to_bibliometrix.html",
      "",
      "======================================================",
      paste("合并的RDATA文件:", basename(rdata_file)),
      "======================================================"
    )
    
    # 移除空行
    report_content <- report_content[report_content != ""]
    
    writeLines(report_content, evaluation_report)
    log_message(paste("  已保存数据质量评估报告到:", evaluation_report))
    
    # 创建biblioshiny启动脚本
    shiny_script <- file.path(output_path, "start_biblioshiny.R")
    shiny_content <- c(
      "# biblioshiny启动脚本",
      "# 加载必要的包",
      "library(bibliometrix)",
      "",
      "# 加载数据",
      paste("load('", basename(rdata_file), "')", sep=""),
      "",
      "# 启动biblioshiny交互式界面",
      "biblioshiny()"
    )
    writeLines(shiny_content, shiny_script)
    log_message(paste("  已创建biblioshiny启动脚本:", basename(shiny_script)))
    
  } else {
    log_message("  基本分析失败，跳过进一步的数据评估")
  }
  
}, error = function(e) {
  log_message(paste("错误处理合并文件:", e$message))
  
  # 更新所有文件的状态为失败
  idx <- which(conversion_log$Status == "已合并")
  if (length(idx) > 0) {
    conversion_log$Status[idx] <- "合并失败"
    conversion_log$ErrorMessage[idx] <- e$message
  }
})

# 尝试删除临时合并文件
if (file.exists(combined_txt_file)) {
  file.remove(combined_txt_file)
  log_message("已删除临时合并文件")
}

# 保存转换日志到Excel
wb_log <- createWorkbook()
addWorksheet(wb_log, "转换日志")
writeData(wb_log, "转换日志", conversion_log)

# 添加过滤和格式
setColWidths(wb_log, "转换日志", cols = 1:ncol(conversion_log), widths = "auto")
freezePane(wb_log, "转换日志", firstRow = TRUE)

# 设置条件格式
conditionalFormatting(wb_log, "转换日志", cols = which(names(conversion_log) == "Status"),
                     rows = 2:(nrow(conversion_log) + 1), 
                     rule = "已合并", 
                     style = createStyle(bgFill = "#E2EFDA"))

conditionalFormatting(wb_log, "转换日志", cols = which(names(conversion_log) == "Status"),
                     rows = 2:(nrow(conversion_log) + 1), 
                     rule = "合并失败", 
                     style = createStyle(bgFill = "#F8CBAD"))

saveWorkbook(wb_log, excel_log_file, overwrite = TRUE)
log_message(paste("已保存转换日志:", excel_log_file))

log_message("Web of Science数据转换为单一bibliometrix RDATA格式完成")