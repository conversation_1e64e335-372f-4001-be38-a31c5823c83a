bibliometrix R 包功能模块详解
I. bibliometrix 简介
A. 软件包概述：目的、范围与核心功能
bibliometrix 是一个专为科学计量学和文献计量学领域的定量研究所设计的 R 软件包 1。其核心目标在于将定量分析方法应用于科学出版物及其引用数据。该软件包为研究人员在文献计量分析的各个关键阶段提供支持，这些阶段包括：数据导入与转换、描述性分析、网络提取（用于共被引、耦合、合作和共词分析）、手稿文本挖掘（如标题、摘要、关键词等）以及概念结构映射 1。
bibliometrix 由 Massimo Aria 和 Corrado Cuccurullo 共同开发 1。作为一个基于 R 语言的开源工具，它具备高度的灵活性，能够快速升级，并可与其他 R 软件包集成 1。R 语言本身在统计计算和图形处理方面拥有强大功能，包括大量的统计算法、高质量的数值计算程序以及集成的数据可视化工具，这些都为 bibliometrix 提供了坚实的基础 1。值得注意的是，bibliometrix 在执行特定任务时，会巧妙地利用 R 生态系统中其他成熟的软件包，例如用于主成分分析（PCA）和多重对应分析（MCA）的 factominer 包，用于多维尺度分析（MDS）的 cmdscale 包，用于聚类的 cluster 包，以及用于社交网络可视化的 igraph 包和二维映射的 ggplot2 包 1。这种设计策略使得 bibliometrix 能够专注于核心的文献计量工作流程，同时借助外部工具的强大功能来增强其分析和可视化能力。对于熟悉这些基础软件包的用户而言，他们可以更进一步地定制或扩展分析。
该软件包的设计以完整的工作流程为中心 1。文献计量学研究通常遵循一个结构化的过程：数据收集、清洗、描述性分析、网络分析和结果解读。bibliometrix 提供的函数与这些阶段一一对应，使得熟悉文献计量方法的研究人员能够直观地使用。这种结构化的方法论不仅仅是将一系列工具简单地集合起来，而是构建了一个内聚的系统，旨在支持从头到尾的文献计量研究，从而促进了更为流畅和可复现的研究过程。
B. bibliometrix 中的一般文献计量工作流程
使用 bibliometrix 进行文献计量分析时，用户通常会遵循以下一系列操作步骤：
1.	数据采集 (Data Acquisition)：从主流文献数据库（如 Scopus、Web of Science (WoS)、PubMed、Dimensions、Cochrane Library、Lens）获取原始文献数据 1。
2.	数据导入与转换 (Data Importing and Conversion)：使用如 convert2df() 等函数，将下载的文献数据转换成 R 数据框（data frame）格式，这是后续分析的基础 1。
3.	数据清洗与准备 (Data Cleaning and Preparation)：对导入的数据进行清洗，例如处理缺失值、移除重复记录、提取特定元数据字段等（详见模块 II）。
4.	描述性分析 (Descriptive Analysis)：利用 biblioAnalysis() 及其相关函数，计算文献集的基本计量指标，了解其概况 1。
5.	网络分析 (Network Analysis)：构建并可视化多种类型的文献计量网络，如合作网络、共被引网络、耦合网络和共词网络 1。
6.	概念与主题分析 (Conceptual and Thematic Analysis)：通过共词分析等方法，绘制研究领域的知识图谱和概念结构 1。
7.	历史分析 (Historical Analysis)：追溯研究领域的演化路径，识别历史根源文献 2。
文献计量分析高度依赖于将外部数据转换为标准化的 R 数据框，这一过程主要通过 convert2df() 函数完成 1。该数据框通常采用标准的 Clarivate Analytics Web of Science (WoS) 字段标签编码 1。由于文献数据来源多样，格式各异，拥有一个标准化的内部数据结构对于软件包中各个函数能够一致地操作至关重要。以 WoS 标签为基准提供了一套广泛理解且相对全面的字段集。因此，用户必须确保其输入数据被正确映射到这些标准标签，bibliometrix 才能按预期工作。convert2df() 函数是进入整个软件包分析生态系统的门户，初始步骤中的任何错误或不一致都将影响所有后续分析。bibtag 数据对象 23 在此过程中可能扮演着关键角色，负责将各种来源的标签映射到基于 WoS 的内部标准。
II. 模块 1：数据管理 - 导入、转换与准备数据
数据管理是文献计量分析流程的初始且至关重要的阶段。它涉及将从各种文献数据库下载的原始数据导入 R 环境，将其转换为 bibliometrix 包可识别和处理的标准化格式，并进行初步的清洗和预处理，以确保数据质量，为后续的复杂分析奠定坚实基础。
A. 导入与转换文献数据
此分节聚焦于将文献数据导入 bibliometrix 并转换为可用格式的初始步骤，这是所有分析的基础。
1. convert2df()：全面的数据导入与转换
●	目的：该函数的核心功能是读取并转换来自多种文献数据库（如 SCOPUS、Web of Science、Dimensions、PubMed、Cochrane Library、Lens、OpenAlex）和多种文件格式（如 BibTeX、纯文本、CSV、EndNote、Excel、API 对象）的文献数据，将其统一为标准的 R 数据框格式 5。这个生成的数据框是 bibliometrix 中大多数其他函数的主要输入对象。
●	输入字段：函数接受原始的导出文件或 API 对象作为输入。这些文件内部的具体字段取决于用户从源数据库导出时选择的数据库和格式（例如，WoS 纯文本文件包含 AU、TI、SO、CR 等标签，如 126 所列）。convert2df 根据 dbsource 和 format 参数来解析这些字段。
●	参数 15：
○	file：字符型向量，包含一个或多个文件名，或者 API 对象名。
○	dbsource：字符型，指明文献数据的来源数据库。可选值包括 "wos"、"scopus"、"dimensions"、"pubmed"、"cochrane"、"lens"、"openalex"、"generic" 等。默认值为 "isi"（等同于 "wos"）。
○	format：字符型，指明导入文件的格式。可选值包括 "bibtex"、"plaintext"、"csv"、"endnote"、"excel"、"api"、"pubmed" 等。默认值为 "plaintext"。
○	remove.duplicates 23：逻辑型，如果为 TRUE（默认值），则根据 DOI 和数据库 ID 移除重复记录。
●	计算方法/过程：
1.	读取指定文件（或文件列表）或 API 对象的内容。
2.	根据 dbsource 和 format 参数解析数据，识别单个记录及其元数据字段 15。
3.	将特定来源的标签映射到一套标准化的字段标签，通常基于 Clarivate Analytics WoS 的编码规范 1。bibtag 数据对象 23 在此过程中起着重要作用。
4.	构建一个 R 数据框，其中行代表手稿（文献），列代表标准化的字段标签。
5.	如果 remove.duplicates = TRUE，则识别并移除重复的记录 23。
●	输出结果：一个 R 数据框，其中行是手稿，列是标准化的文献计量字段标签（例如 AU、TI、SO、PY、CR、DE、ID、TC、AB、C1、RP、UT、DB）。输出对象的类为 bibliometrixDB 和 data.frame 24。
●	注释：这是数据导入的基石函数。15 强调了文献集需要包含一组最少的强制性元数据字段（如作者姓名、标题、期刊名称、机构、出版年份）。15 提供了针对各种 WoS 和 Scopus 格式的示例。16 指出，biblioshiny 工具期望导入的 BibTeX 文件是按照 WoS 或 Scopus 导出的精确格式，并包含所有必需的元数据。
表 1：convert2df 函数参数
参数	类型	描述	默认值
file	字符型向量	包含一个或多个待导入文件名，或 API 对象名。	无
dbsource	字符型	指明文献数据的来源数据库，如 "wos", "scopus", "dimensions", "pubmed", "cochrane", "lens", "openalex", "generic"。	"isi"
format	字符型	指明导入文件的格式，如 "bibtex", "plaintext", "csv", "endnote", "excel", "api", "pubmed"。	"plaintext"
remove.duplicates	逻辑型	是否移除重复记录（基于 DOI 和数据库 ID）。	TRUE
2. readFiles()：读取文件（注意：已弃用，建议使用 convert2df()）
●	目的：此函数曾用于将一系列 ISI 或 Scopus 导出文件加载为一个大型字符对象，该对象随后会传递给 convert2df 函数进行转换 8。
●	状态：自 bibliometrix 3.0.0 版本起已弃用。强烈建议用户直接使用 convert2df 函数导入并转换文件 25。这种演进简化了导入工作流程，将文件读取和数据框转换合并为一个步骤，减少了用户出错的可能性，并提高了效率，反映了软件为提升可用性和效率而进行的自然迭代。
●	参数 25：...（表示一个或多个文件名序列）。
●	历史输出结果：一个大型字符型向量。
3. mergeDbSources()：合并来自多个来源的数据
●	目的：将可能源自不同数据库（例如 Scopus 和 WoS）的多个文献数据框合并成一个单一的数据框。该函数还能在合并过程中识别并移除重复的记录 5。
●	输入字段：接受两个或多个由 convert2df 函数创建的文献数据框作为输入。在检测重复记录时，它依赖于如 DOI (DI)、标题 (TI) 和出版年份 (PY) 等字段 27。
●	参数 27：
○	...：一个或多个待合并的文献数据框。
○	remove.duplicated：逻辑型。如果为 TRUE（默认值），则删除重复的文献记录。
○	verbose：逻辑型。如果为 TRUE（默认值），则在屏幕上打印关于被移除的重复文献的信息。
●	计算方法/过程：
1.	将输入数据框按行合并（使用 dplyr::bind_rows）27。
2.	如果 remove.duplicated = TRUE：
■	首先基于 DOI 移除重复项（如果 DI 列存在且非空/NA）。
■	然后基于规范化后的标题 (TI) 和出版年份 (PY) 的组合来移除重复项。标题规范化过程包括移除字母数字以外的字符和多余的空格 27。
3.	如果合并的数据框来自不同的数据库（例如 "scopus", "wos"），函数会将 DB 字段的值标准化为 "ISI" (WoS 的标识)，并将原始的数据库来源信息存储在名为 DB_Original 的新列中。此外，还会进行一些作者数据的清洗工作，例如规范化作者姓名的格式 27。 在整合来自多个来源的数据时，实现完美的去重是一个挑战，因为不同数据库在字段覆盖、命名约定和数据录入实践上可能存在细微差别。尽管 mergeDbSources 提供了一个有价值的工具，并且其内部的去重机制（依赖 DOI，以及标题/年份的组合）已属稳健，但用户应意识到，根据数据的复杂性和研究问题的具体要求，可能仍需要进行额外的手动检查或采用更复杂的去重策略。该函数尝试标准化作者姓名的做法也突显了文献计量学中作者名 disambiguation (消歧) 这一常见问题。
●	输出结果：一个单一的、合并后的文献数据框，如果指定，则其中的重复记录已被移除。
表 2：mergeDbSources 函数参数
参数	类型	描述	默认值
...	数据框	一个或多个待合并的文献数据框。	无
remove.duplicated	逻辑型	是否删除重复的文献记录。	TRUE
verbose	逻辑型	是否在屏幕上打印关于被移除的重复文献的信息。	TRUE
B. 数据清洗与预处理
此分节详细介绍用于确保数据质量并为稳健分析准备元数据的函数。文献计量分析的质量高度依赖于基础元数据的完整性。例如，引文分析需要完整的 CR（被引文献）和 TC（总被引次数）字段；作者分析需要 AU（作者）字段；机构分析则需要 C1（机构地址）字段。关键字段中大量缺失数据可能会使结果无效或产生严重偏差。
1. missingData()：评估和报告缺失的元数据
●	目的：计算并报告文献数据框中每个元数据字段的缺失数据百分比。它对每个字段的完整性进行分类，以帮助用户了解哪些分析是可行的 2。
●	输入字段：一个文献数据框 M（通常是 convert2df 的输出）。该函数会检查 M 中的所有列。
●	参数 29：
○	M：文献数据框。
●	计算方法/过程 28：
1.	对于 M 中的每一列，统计缺失值的数量。缺失值被识别为 NA、空字符串 ""，或特定的占位符如 "NA,0000,NA"、"NA"、"none" 28。对于 TC（总被引次数）列，如果其数值总和为 0，则该列中的所有条目均被视为缺失 28。
2.	计算每列的缺失值百分比：(缺失值数量 / 总行数) * 100。
3.	根据缺失数据的百分比分配一个状态类别："Excellent" (0%)、"Good" (0.01%-10%)、"Acceptable" (10.01%-20%)、"Poor" (20.01%-50%)、"Critical" (50.01%-99.99%)、"Completely missing" (100%) 2。
●	输出结果 2：一个列表，包含两个数据框：
○	allTags：包含 M 中所有元数据标签的结果（缺失数量、百分比、状态）。
○	mandatoryTags：仅报告那些对于使用 bibliometrix 或 biblioshiny 进行分析所必需的元数据标签的结果。28 列出了这些强制性标签：AB（摘要）、AU（作者）、C1（作者机构）、CR（被引文献）、DE（作者关键词）、DI（DOI）、DT（文献类型）、ID（扩展关键词）、LA（语言）、PY（出版年份）、RP（通讯作者机构）、SO（来源期刊）、TC（总被引次数）、TI（标题）、WC（WoS学科分类）。 用户必须在其工作流程的早期运行 missingData。mandatoryTags 的输出尤为重要，因为它直接告知哪些 bibliometrix 分析可以可靠地执行。如果关键字段的状态为 "Poor" 或 "Critical"，用户可能需要重新审视其数据采集策略或在其研究中承认这些局限性。
表 3：missingData 输出结构 (mandatoryTags)
列名	描述
tag	元数据字段标签
description	字段描述
missing_counts	缺失值数量
missing_pct	缺失值百分比
status	基于缺失百分比的完整性状态
2. duplicatedMatching()：识别和处理重复记录
●	目的：在文献数据框中搜索并移除重复的记录。该函数可以使用精确匹配或近似字符串匹配（受限 Damerau-Levenshtein 距离）方法 5。文献记录由于数据录入错误、不同的索引实践或出版细节的微小差异，常常在标题、作者名或其他字段中存在细微变化。精确匹配可能会遗漏这些"近似重复"的记录。模糊匹配提供了一种更稳健的方法来识别此类情况，但 tol 参数的选择至关重要——过低可能合并不同的记录，过高则可能遗漏真正的重复项。
●	输入字段 31：一个文献数据框 M。函数针对 M 中指定的 Field（字段）进行匹配，通常是 TI（标题）、AB（摘要）或 UT（唯一手稿 ID）。
●	参数 31：
○	M：文献数据框。
○	Field：字符型，用于匹配的字段标签（例如 "TI", "AB", "UT"）。默认值："TI"。
○	exact：逻辑型。如果为 TRUE，则使用精确匹配。如果为 FALSE（默认值），则使用 Damerau-Levenshtein 距离。
○	tol：数值型，用于近似匹配的最小相对相似度阈值（0 到 1 之间）。默认值：0.95。仅在 exact=FALSE 时使用。
●	计算方法/过程 31：
1.	如果 exact = TRUE：对指定 Field 中的内容进行直接的、逐字符的比较。
2.	如果 exact = FALSE：计算指定 Field 中记录间内容的 Damerau-Levenshtein 距离。该距离衡量将一个字符串转换为另一个字符串所需的单字符编辑（插入、删除、替换、相邻字符调换）次数。由此派生出一个相对相似度得分，如果该得分 >= tol，则记录被视为重复。
3.	移除已识别的重复条目，仅保留每个唯一记录的一个实例。 用户需要仔细考虑使用哪个 Field 进行去重（例如，如果 UT 或 DOI 可用且可靠，则优先使用；否则可使用 TI 并配合模糊匹配）。tol 参数需要仔细调整，可能通过迭代测试，以在重复检测的精确度和召回率之间取得平衡。此函数不同于 convert2df 或 mergeDbSources 中的 remove.duplicates 参数，后者主要使用 DOI 和可能更简单的标题/年份匹配。duplicatedMatching 提供了在单个数据框内对基于字符串的去重进行更细致的控制。
●	输出结果 31：一个数据框，其中包含移除了已识别重复记录的原始文献数据。
表 4：duplicatedMatching 函数参数
参数	类型	描述	默认值
M	数据框	文献数据框。	无
Field	字符型	用于匹配的字段标签（例如 "TI", "AB", "UT"）。	"TI"
exact	逻辑型	如果为 TRUE，则使用精确匹配。如果为 FALSE，则使用 Damerau-Levenshtein 距离。	FALSE
tol	数值型	近似匹配的最小相对相似度阈值（0 到 1）。仅在 exact=FALSE 时使用。	0.95
3. metaTagExtraction()：提取和创建新的元数据字段
●	目的：从文献数据框内已有的聚合数据中提取额外的元数据字段。这对于创建新的、更具体的分析字段非常有用 4。原始文献数据通常以聚合形式存储信息（例如，单个字符串中包含所有作者的机构）。不同的文献计量分析需要不同粒度的元数据。例如，分析第一作者国家的合作网络不同于分析所有作者国家的合作网络。metaTagExtraction 提供了适当解析和结构化数据以进行这些特定网络分析的工具。
●	输入字段 6：一个文献数据框 M。函数操作现有的字段，如 CR（被引文献）、C1（作者地址/机构）和 RP（通讯作者地址/机构），以派生新信息。
●	参数 6：
○	M：输入的文献数据框。
○	Field：字符型，指定要提取的新标签。可选值包括：
■	"CR_AU"：每个被引文献的第一作者。
■	"CR_SO"：每个被引文献的来源（期刊）。
■	"AU_CO"：所有合著者的机构所属国家。
■	"AU1_CO"：第一作者的机构所属国家。
■	"AU_UN"：每个合著者及通讯作者的机构（大学/研究中心）。
■	"SR"：文献的短标签（例如 "ARIA M, 2017, J INFORMETRICS"）。
○	sep：字符型，源数据列（例如 C1 或 CR）中字段的分隔符。默认值：";"。
○	aff.disamb：逻辑型。如果为 TRUE（默认值）且 Field="AU_UN"，则使用机构消歧算法。
●	计算方法/过程 6：
○	对于 CR_AU：解析 CR 字段，识别每个被引文献的第一作者。
○	对于 CR_SO：解析 CR 字段，识别每个被引文献的来源期刊/书籍。
○	对于 AU_CO：解析 C1 字段（作者地址），提取所有列出机构的国家信息。
○	对于 AU1_CO：解析 C1 或 RP 字段中第一作者的机构信息，并提取国家。
○	对于 AU_UN：解析 C1（以及 RP，如 34 中针对 AU1_UN 逻辑所示），提取大学/机构名称。如果 aff.disamb=TRUE，则应用算法尝试标准化机构名称。aff.disamb 参数的存在也暗示了机构名称模糊性的固有挑战以及软件包试图解决此问题的努力。
○	对于 SR：使用第一作者、年份和来源缩写构建一个简短的参考文献字符串。
●	输出结果 6：输入的文献数据框 M，并新增一列，列名根据 Field 参数命名，包含提取出的元数据。例如，使用 Field="CR_AU" 提取被引文献的作者后，可以利用这个新字段在 biblioNetwork 中构建作者共被引网络。
表 5：metaTagExtraction 函数参数
参数	类型	描述	默认值
M	数据框	文献数据框。	无
Field	字符型	指定要提取的新标签。可选值包括："CR_AU" (被引文献第一作者), "CR_SO" (被引文献来源), "AU_CO" (所有作者国家), "AU1_CO" (第一作者国家), "AU_UN" (作者机构), "SR" (文献短标签)。	无
sep	字符型	源数据列中字段的分隔符。	";"
aff.disamb	逻辑型	如果为 TRUE 且 Field="AU_UN"，则使用机构消歧算法。	TRUE
C. 文本与字符串实用工具
此分节涵盖用于基本文本操作的辅助函数，这些函数通常在内部使用或用于准备文本数据。文献数据源中的文本信息（如作者名、关键词）常因录入或导出过程引入不一致的空格，这些不一致可能导致本应相同的条目被视为不同，从而影响统计的准确性。文本规范化是数据清洗的基础步骤，确保匹配、计数和分析的准确性。bibliometrix 提供的这些修剪函数虽然用户可能不常直接调用，但它们的存在及其在内部函数（如 Hindex 35 和 KeywordGrowth 36）中的使用，突显了该包对数据质量的关注。
1. trim()：移除首尾空白字符
●	目的：从字符对象或字符向量的元素中删除开头和结尾的空白字符（如空格、制表符）5。
●	输入字段：一个字符对象或字符向量 x。
●	参数 37：x (字符对象)。
●	计算方法/过程：可能使用正则表达式或字符串函数来移除字符串开头和结尾的空格、制表符等。
●	输出结果：移除了首尾空白字符的字符对象或向量 37。
2. trim.leading()：移除前导空白字符
●	目的：仅从字符对象或字符向量的元素中删除开头的空白字符 5。
●	输入字段：一个字符对象或字符向量 x。
●	参数 39：x (字符对象)。
●	计算方法/过程：与 trim() 类似，但仅针对字符串的开头。
●	输出结果：移除了前导空白字符的字符对象或向量 39。
3. trimES()：移除多余空白字符
●	目的：从字符对象内部删除多余的（连续多个）空白字符，将其减少为单个空格，并且通常也会修剪首尾空格 5。
●	输入字段：一个字符对象或字符向量 x。
●	参数 40：x (字符对象)。
●	计算方法/过程：使用字符串操作将连续多个空格替换为单个空格。
●	输出结果：内部间距已规范化的字符对象或向量 40。
D. 用于数据准备的支持性数据对象
这些是内置的数据集或对象，有助于数据准备和分析。
1. stopwords：内置英文停用词列表
●	目的：提供一个包含常见英文单词（例如 "the", "is", "an", "a"）的字符向量。在文本分析过程中，这些词通常会被移除，以便更关注具有实际意义的术语 5。
●	数据类型：字符向量 44。
●	内容：包含 665 个英文停用词的列表 44。如 41 和 41 所示，termExtraction 等函数内部还会额外添加更具领域特异性的停用词，如出版商名称（"elsevier", "springer"）或常见研究短语（"rights reserved"）。这种对停用词的上下文管理非常重要，因为通用停用词列表是一个良好的起点，但特定领域或分析可能需要排除额外的常见但非信息性的词语。通过 remove.terms 参数或函数内部的特定添加，可以灵活地定制停用词列表。
●	典型用途：主要被 termExtraction 41 和 conceptualStructure 2 等函数用于在共词分析或基于术语的网络构建之前，从文本字段（标题、摘要、关键词）中过滤掉常见的、非信息性的词语。biblioAnalysis 函数也使用它来辅助提取被引文献和作者的国家字段信息 44。用户应意识到停用词移除并非一刀切的过程，有效的文本分析通常需要根据具体数据集和研究问题定制停用词列表，以避免移除有意义的术语或保留不相关的术语。
2. countries：用于标准化的国家索引
●	目的：一个包含规范化国家索引的数据框，其中包括国家名称、所属大陆以及地理坐标（经纬度）。biblioAnalysis 函数使用此对象来提取和标准化作者机构及被引文献中的国家信息 5。
●	数据类型：数据框 47。
●	内容 47：包含 199 行和 4 个变量：countries (国家名称), continent (大陆), Longitude (经度), Latitude (纬度)。
●	典型用途：主要由 biblioAnalysis 函数内部用于国家层面的分析，并可能被绘图函数用于标准化国家名称和提供地理数据。
3. bibtag：文献计量标签映射表
●	目的：一个数据框，用于映射来自不同数据库（如 WoS、Scopus）和不同格式（如 BibTeX、Dimensions CSV/XLSX）的文献计量标签字段到一个通用的内部标准。这很可能为 convert2df 函数的数据转换过程提供支持 1。文献数据库（WoS、Scopus、Dimensions 等）对等效字段使用不同的名称（例如，"Cited References"在 WoS BibTeX 中可能是 CR 标签，但在 Scopus CSV 中可能是另一个标签）。为了让 convert2df 能够正确地将各种输入处理成单一标准化的数据框，它需要这样一个"翻译词典"。bibtag 就充当了这个关键的内部查找表。
●	数据类型：数据框 23。
●	内容 23：包含 44 行和 6 个变量：TAG (通用标签), SCOPUS (Scopus BibTeX 字段), ISI (WoS BibTeX 字段), GENERIC (通用 BibTeX 字段), DIMENSIONS_OLD (旧版 Dimensions.ai CSV/XLSX 字段), DIMENSIONS (当前 Dimensions.ai CSV/XLSX 字段)。
●	典型用途：主要由 convert2df 函数内部使用，以正确解析各种输入文件格式和来源的字段，并将它们映射到 bibliometrix 标准化的数据框结构中。convert2df 的稳健性和准确性在很大程度上取决于 bibtag 映射的完整性和正确性。虽然用户通常不直接与 bibtag 交互，但它的存在对于软件包处理异构数据源的能力至关重要。
III. 模块 2：核心文献计量分析 - 描述研究图景
此模块涵盖了提供文献集合基本描述性统计和分析的函数。
A. 中心描述性分析
1. biblioAnalysis()：执行主要的文献计量计算
●	目的：对数据集执行全面的文献计量分析，计算与文献、作者、机构、来源期刊、关键词和引文相关的广泛指标 1。
●	输入字段 4：一个文献数据框 M（convert2df 的输出）。它利用许多标准字段，如 AU (作者), PY (出版年份), TC (总被引次数), SO (来源), DE (作者关键词), ID (扩展关键词), CR (被引文献), C1 (作者机构/地址), RP (通讯作者地址)。
●	参数 4：
○	M：文献数据框。
○	sep：用于多值字段（如作者、关键词）的字段分隔符。默认值：";"。
●	计算方法/过程：
○	计算作者、来源、关键词的频率和分布。
○	统计文献数量、每篇文献的作者数量、作者出现次数。
○	识别被引次数最多的文献。
○	分析历年的出版趋势。
○	提取并分析机构数据，包括国家合作情况（SCP/MCP 指数）。
○	计算文献的总被引次数和年均被引次数。
○	countries 数据对象 47 用于国家字段的提取和标准化。
●	输出结果 4：一个类名为 bibliometrix 的对象。这是一个列表，包含众多组成部分，例如：Articles (文献总数), Authors (作者列表及其发文数), AuthorsFrac (作者发文数，分数化计算), FirstAuthors (通讯作者/第一作者列表), nAUperPaper (每篇文献的作者数), Appearances (作者出现总次数), nAuthors (独立作者总数), AuMultiAuthoredArt (参与多作者合著的作者数), MostCitedPapers (高被引文献列表), Years (文献出版年份), FirstAffiliation (第一作者机构), Affiliations (机构频次统计), Aff_frac (机构频次统计，分数化计算), CO (第一作者国家), Countries (国家发文频次统计), CountryCollaboration (国家合作分析结果), TotalCitation (文献总被引次数), TCperYear (文献年均被引次数), Sources (来源期刊/出版物频次统计), DE (作者关键词频次统计), ID (扩展关键词频次统计)。
表 6：biblioAnalysis 函数参数
参数	类型	描述	默认值
M	数据框	文献数据框。	无
sep	字符型	用于多值字段（如作者、关键词）的字段分隔符。	";"



**表 7：`biblioAnalysis` 对象关键输出组件**

组件名	描述
Articles	文献总数
Authors	作者列表及其发文数
MostCitedPapers	高被引文献列表
Years	文献出版年份
Countries	国家发文频次统计
TCperYear	文献年均被引次数
Sources	来源期刊/出版物频次统计
DE	作者关键词频次统计
ID	扩展关键词（Keywords Plus）频次统计
CountryCollaboration	国家合作分析结果（如 SCP, MCP 指数）
2. summary.bibliometrix()：汇总 biblioAnalysis 的结果
●	目的：此方法用于提供 bibliometrix 对象（即 biblioAnalysis 函数的输出）主要结果的格式化摘要。它会显示关键信息表，如年度产出、顶级作者、最高被引文献、主要国家、最相关来源期刊以及最相关关键词等 1。R 语言中 summary() 和 plot() 是泛型函数，bibliometrix 为 bibliometrix 类对象提供了特定的方法（summary.bibliometrix, plot.bibliometrix）。这种面向对象的 S3 系统是 R 中常见且强大的范式，允许用户对自定义对象使用熟悉的函数名，而这些函数的行为则根据它们所操作对象的特定类别进行定制。由 biblioAnalysis 返回的 bibliometrix 对象封装了所有必要的数据，因此 summary 和 plot 可以直接访问和呈现这些数据，无需用户为每个表格或图表手动指定数据子集，从而简化了获取标准描述性输出的用户体验。
●	输入字段：一个 bibliometrix 类的对象。
●	参数 19：
○	object：bibliometrix 对象。
○	k：整数，每个摘要表格显示的行数（例如，显示前 k 位作者）。默认值：10。
○	pause：逻辑值，是否在屏幕滚动时暂停。默认值：FALSE。
○	width：整数，屏幕输出宽度。默认值：120。
○	verbose：逻辑值，是否将输出打印到屏幕。默认值：TRUE。
●	计算方法/过程：从输入的 bibliometrix 对象中提取预先计算好的数据，并将其格式化为易读的表格。例如，它会提取 Authors 组件，对其进行排序，并显示前 k 个。
●	输出结果 19：一个包含摘要统计信息的列表，通常以格式化表格的形式打印到控制台。关键表格包括：MainInformation (数据主要信息), AnnualProduction (年度科学产出), AnnualGrowthRate (年度增长率), MostProdAuthors (最高产作者), MostCitedPapers (最高被引文献), MostProdCountries (最高产国家/通讯作者国家), TCperCountries (各国总被引次数), MostRelSources (最相关来源), MostRelKeywords (最相关关键词)。
表 8：summary.bibliometrix 函数参数
参数	类型	描述	默认值
object	对象	bibliometrix 对象。	无
k	整数	每个摘要表格显示的行数。	10
pause	逻辑值	是否在屏幕滚动显示结果时暂停。	FALSE
width	整数	定义屏幕输出宽度。	120
verbose	逻辑值	是否允许屏幕输出。	TRUE
3. plot.bibliometrix()：可视化 biblioAnalysis 的结果
●	目的：此方法用于从 bibliometrix 对象生成基本图表，可视化关键的描述性指标，如年度科学产出、最高产作者、最高被引文献等 1。
●	输入字段：一个 bibliometrix 类的对象。它使用此对象的多个组件，例如 x$Authors, x$Countries, x$AnnualProduction, x$MostProdCountries, x$TCperYear, x$MostRelSources, x$MostRelKeywords （根据 50 及文献计量学的一般绘图需求推断）。
●	参数 50：
○	x：bibliometrix 对象。
○	k：整数，图表中显示的项目数量（例如，前 k 位作者）。默认值：10。
○	pause：逻辑值，是否在图表间暂停屏幕滚动。默认值：FALSE。
○	（其他 ... 参数可以传递给 ggplot2 函数，但在 50 的描述中，除了 k 和 pause 外，未明确详述 plot.bibliometrix 本身接受的其他特定绘图参数）。
●	计算方法/过程：从 bibliometrix 对象中提取相关的数据框/向量（例如 x$Authors, x$AnnualProduction）。使用 ggplot2 生成各种图表。51 的源代码注释显示了其内部对以下图表的 ggplot 调用：
○	最高产作者 (Most Productive Authors)：基于 x$Authors 的条形图。
○	最高产国家 (Most Productive Countries)：基于 x$Countries 的条形图。
○	年度科学产出 (Annual Scientific Production)：基于 x$AnnualProduction 的折线图。
○	最相关来源 (Most Relevant Sources)：基于 x$Sources 的条形图。
○	最相关关键词 (Most Relevant Keywords)：基于 x$DE 或 x$ID 的条形图。
○	国家科学产出 (Country Scientific Production)：基于 x$CountryCollaboration$SCP (国家内合作) 和 x$CountryCollaboration$MCP (国际合作) 的条形图。
○	年均被引次数 (Average Citations per Year)：基于 x$TCperYear$MeanTCperYear 的折线图。
●	输出结果 50：一个包含 ggplot2 图表对象的列表。如果 pause=TRUE，这些图表通常会按顺序显示。
表 9：plot.bibliometrix 函数参数
参数	类型	描述	默认值
x	对象	bibliometrix 对象。	无
k	整数	图表中显示的项目数量。	10
pause	逻辑值	是否在图表间暂停屏幕滚动。	FALSE
B. 基于引文的指标
理解文献计量中的各种"引文"概念至关重要。citations() 函数分析的是文献集中论文的参考文献列表（CR 字段），从而识别出被该文献集频繁引用的外部文献或作者。这些可视为文献集"指向外部"的引文，帮助识别构成该领域知识基础的影响性著作。相对地，localCitations() 函数关注的是文献集内部的引文关系，即集合中的文献被同一集合中其他文献引用的情况，揭示了集合内部的影响力结构。而文献数据框 M 中的 TC（总被引次数）字段，则代表文献集中的每篇文献从整个数据库（如 WoS、Scopus）获得的全局总被引次数 16。因此，基于 TC 的排序（例如通过 biblioAnalysis 结果中的 MostCitedPapers）显示的是文献集内文献在更广泛的学术领域中的影响力。
1. citations()：全局引文频次分布
●	目的：计算整个文献集合中被引文献或被引第一作者的频次分布 1。
●	输入字段 53：文献数据框 M。主要使用 CR (被引文献) 字段。如果 field = "author"，则隐式地使用 CR 字符串中的作者信息。
●	参数 53：
○	M：文献数据框。
○	field：字符型，"article" (默认值) 表示分析被引文献，"author" 表示分析被引第一作者 (WoS 数据仅限第一作者)。
○	sep：字符型，CR 字段中引文间的分隔符。默认值：";"。
●	计算方法/过程 53：
1.	解析 M 中每篇手稿的 CR 字段，按 sep 分割。
2.	如果 field = "article"：统计每个唯一被引文献字符串出现的频率。
3.	如果 field = "author"：从每个被引文献字符串中提取第一作者，并统计其频率。
4.	如果 field = "article"，则提取被引文献的年份和来源。
●	输出结果 53：一个列表对象，包含：
○	Cited：数据框或向量，包含最常被引用的手稿或作者。
○	Year：向量，包含出版年份 (仅当 field = "article" 时)。
○	Source：向量，包含期刊/来源名称 (仅当 field = "article" 时)。
表 10：citations 函数参数
参数	类型	描述	默认值
M	数据框	文献数据框。	无
field	字符型	分析类型："article" (被引文献) 或 "author" (被引第一作者，WoS数据源)。	"article"
sep	字符型	CR 字段中引文间的分隔符。	";"
2. localCitations()：文献集内部的局部引文分析
●	目的：计算文献集内作者和文献的局部引文数 (LCS)。LCS衡量的是文献集中的一篇文献或一位作者被该文献集内其他文献引用的次数 1。
●	输入字段 54：文献数据框 M。使用 CR (被引文献) 字段查找引文，AU (作者) 字段归属作者的 LCS，以及如 SR (短文献标识) 或 DI (DOI) 等文献标识符归属文献的 LCS。TC (总被引次数) 字段用于 fast.search 选项。
●	参数 54：
○	M：文献数据框。
○	fast.search：逻辑型。如果为 TRUE，则仅为全局被引次数最高的前 25% 文献计算 LCS (基于 TC 的分位数)。默认值：FALSE。
○	sep：字符型，CR 字段中引文间的分隔符。默认值：";"。
○	verbose：逻辑型。如果为 TRUE，则打印结果。默认值：FALSE。
●	计算方法/过程 54：
1.	内部调用 histNetwork(M, network=FALSE) 获取局部引文数据（即 M 中每篇论文的 LCS 计数）。histNetwork 函数解析 CR 字段，并将这些被引文献与 M 中的文献进行匹配（可能使用 SR 或 DI）。
2.	对于作者 LCS：将论文的 LCS 值聚合到这些论文的 AU 字段中列出的作者。
3.	对于论文 LCS：直接使用 M 中每篇论文已识别的 LCS 计数。
4.	fast.search 选项在处理前根据 TC 过滤 M。
●	输出结果 54：一个列表，包含：
○	Authors：数据框，包含作者及其总局部引文数。
○	Papers：数据框，包含文献 (通过 SR, DI, Year 识别) 及其局部 (LCS) 和全局 (GCS, 即 TC) 引文计数。
○	M：输入的文献数据框 M，可能已增加了包含 LCS 的列。
表 11：localCitations 函数参数
参数	类型	描述	默认值
M	数据框	文献数据框。	无
fast.search	逻辑型	如果为 TRUE，则仅为全局被引次数最高的前 25% 文献计算 LCS。	FALSE
sep	字符型	CR 字段中引文间的分隔符。	";"
verbose	逻辑型	如果为 TRUE，则打印结果。	FALSE
3. normalizeCitationScore()：计算规范化引文分数
●	目的：计算文献、作者或来源的规范化引文分数 (NCS)，可以使用全局引文 (NGCS) 或局部引文 (NLCS)。规范化过程消除了出版年份不同对引文累积时间的差异影响 5。由于年代较早的论文比新发表的论文有更多时间积累引文，直接比较原始引文计数可能会产生误导。此外，不同领域和时期的引文实践也可能存在差异。规范化通过考虑出版物的年龄（有时还包括领域，尽管此函数主要关注基于年份的规范化），为引文影响力提供了更公平的比较。
●	输入字段 78：文献数据框 M。使用 TC (全局总被引次数) 或 LCS (局部引文数，如果 impact.measure="local"，则可能通过 localCitations 或类似逻辑派生) 和 PY (出版年份)。对于作者/来源的规范化，还会分别使用 AU 或 SO 字段。
●	参数 78：
○	M：文献数据框。
○	field：字符型，分析单元："documents" (默认值), "authors", 或 "sources"。
○	impact.measure：字符型，"local" 表示计算 NLCS，"global" 表示计算 NGCS。
●	计算方法/过程 78：
1.	文献 NCS：实际被引次数 / 同年度文献的预期被引率。
2.	预期被引率通常是该文献集中同年份所有文献的平均被引次数。
3.	作者/来源的 MNCS (平均规范化引文分数)：归属于该作者/来源的所有文献的 NCS 值的平均值。
4.	NGCS 使用全局 TC；NLCS 使用局部引文数 (LCS)。localCitations 函数 55 可为 M 计算 LCS。 选择 NGCS 还是 NLCS 取决于用户是希望评估文献在更广泛科学界的影响力，还是在特定文献集内部的影响力。
●	输出结果 78：一个数据框，很可能是 M 数据框增加了新的一列，包含计算出的规范化引文分数 (例如 NGCS 或 NLCS)。
表 12：normalizeCitationScore 函数参数
参数	类型	描述	默认值
M	数据框	文献数据框。	无
field	字符型	分析单元："documents", "authors", 或 "sources"。	"documents"
impact.measure	字符型	影响力量度："local" (计算NLCS) 或 "global" (计算NGCS)。	无（需指定）
C. 作者层面指标
1. dominance()：计算作者主导因子
●	目的：根据 Kumar & Kumar (2008) 提出的方法，计算作者的主导因子排名，该因子基于作者作为第一作者的多人合著文献的比例 1。
●	输入字段 83：一个 bibliometrix 对象 (biblioAnalysis 函数的输出 results)。它使用 results$Authors (作者发文数统计) 以及关于第一作者身份和多人合著文献的隐式信息，这些信息由 biblioAnalysis 预先计算（例如，通过分析 AU 字段，统计每篇论文的作者数量）。
●	参数 22：
○	results：bibliometrix 对象。
○	k：整数，要分析的最高产作者的数量。默认值：10。
●	计算方法/过程 83：
○	对于排名前 k 的每位作者：
■	TAA (Total Authored Articles)：总发文量。
■	SAA (Single-Authored Articles)：独立作者发文量。
■	MAA (Multi-Authored Articles) = TAA - SAA：多人合著发文量。
■	FAA (First-Authored Articles among MAA)：在多人合著文献中作为第一作者的发文量。
■	主导因子 (Dominance Factor, DF) = FAA / MAA。
●	输出结果 83：一个数据框，包含以下列：Author (作者), Dominance Factor (主导因子), Tot Articles (TAA), Single Authored (SAA), Multi Authored (MAA), First Authored (FAA), Rank by Articles (按发文量排名), Rank by DF (按主导因子排名)。
表 13：dominance 函数参数
参数	类型	描述	默认值
results	对象	bibliometrix 对象。	无
k	整数	要分析的最高产作者的数量。	10
2. Hindex()：计算作者/来源的 h 指数及其变体
●	目的：为指定的作者或来源计算 h 指数、g 指数和 m 指数 1。
●	输入字段 35：文献数据框 M。使用 AU (作者) 或 SO (来源), PY (出版年份) 和 TC (总被引次数)。SR (短文献标识) 可能用于构建 CitationList。
●	参数 35：
○	M：文献数据框。
○	field：字符型，"author" (默认值) 或 "source"。
○	elements：字符向量，包含作者姓名 (格式为 "SURNAME N"，例如 "ARIA M") 或来源名称。如果为 NULL，则为 M 中的所有元素计算。
○	sep：字符型，AU 字段中作者间的分隔符。默认值：";"。
○	years：整数，计算时考虑的最近年数（例如，过去 5 年的 h 指数）。默认值：Inf (所有年份)。
○	verbose 35：逻辑型，用于控制屏幕输出。
●	计算方法/过程（基于通用理解，非代码片段详述）：
○	根据 elements 和 years 筛选 M。
○	对每个元素 (作者/来源)：
■	收集其出版物及对应的 TC。
■	H 指数：存在 h 篇论文至少被引用 h 次的最大数值 h。
■	G 指数：使得被引次数最多的前 g 篇论文的总被引次数至少为 g2 的最大数值 g。
■	M 指数：H 指数除以第一篇论文发表以来的年数。
●	输出结果 35：一个列表，包含：
○	H：数据框，包含 Element (作者/来源名称), h_index, g_index, m_index, TC (该元素的总被引次数), NP (该元素的出版物数量), PY_start (首次出版年份)。
○	CitationList：一个列表，其中每个元素对应一位作者/一个来源，并包含其文献集合 (论文和被引次数)。
表 14：Hindex 函数参数
参数	类型	描述	默认值
M	数据框	文献数据框。	无
field	字符型	计算对象："author" 或 "source"。	"author"
elements	字符向量	作者姓名列表 (格式 "SURNAME N") 或来源名称列表。如果为 NULL，则计算所有元素。	NULL
sep	字符型	AU 字段中作者间的分隔符。	";"
years	整数	计算时考虑的最近年数。	Inf
3. authorProdOverTime()：分析高产作者的历时生产力
●	目的：计算并绘制前 k 位作者在一段时间内的出版生产力（年发文量和年总被引次数）5。
●	输入字段 60：文献数据框 M。使用 AU (作者), PY (出版年份), TC (总被引次数) 和 DI 87。
●	参数 60：
○	M：文献数据框。
○	k：整数，要分析的顶级作者数量。默认值：10。
○	graph：逻辑型。如果为 TRUE (默认值)，则绘制作者生产力图。
●	计算方法/过程 87：
1.	根据总发文量确定前 k 位最高产作者。
2.	对于这些顶级作者，按年份 (PY) 汇总其发文量 (freq) 和总被引次数 (TC)。
3.	计算 TCpY (年总被引次数，似乎是指该作者当年发表论文的平均被引次数，或当年论文的总被引次数)。
4.	如果 graph=TRUE，则生成一个 ggplot 对象。
●	输出结果 60：一个列表，包含：
○	dfAU：数据框，总结了前 k 位作者的总体生产力 (Author, n (总论文数), TC (总被引次数))。
○	dfpapersAU：数据框，包含前 k 位作者的年度生产力详情 (AU.x, PY, TI, SO, DI, TC)。
○	graph：一个 ggplot 对象，可视化历时生产力 (点的大小代表文章数量，透明度代表 TCpY)。
表 15：authorProdOverTime 函数参数
参数	类型	描述	默认值
M	数据框	文献数据框。	无
k	整数	要分析的顶级作者数量。	10
graph	逻辑型	如果为 TRUE，则绘制作者生产力图。	TRUE
D. 来源层面指标
1. sourceGrowth()：分析顶级来源的出版物增长情况
●	目的：计算并绘制最高产的前 k 个来源（如期刊、书籍等）的年度发文量 1。5。
●	输入字段 (推断)：文献数据框 M。使用 SO (来源) 和 PY (出版年份) 字段。
●	参数 (推断，基于通用模式)：
○	M：文献数据框。
○	top：整数，要分析的顶级来源数量。默认值可能为 10。
○	graph：逻辑型。如果为 TRUE，则绘制增长图。默认值可能为 TRUE。
●	计算方法/过程 (推断)：
1.	根据 M 中的总发文量确定前 k 个最高产的来源。
2.	对于这些顶级来源，统计每年的发文量。
3.	如果 graph=TRUE，则生成一个图表 (很可能是 ggplot2 类型) 显示趋势。
●	输出结果 (推断)：一个列表，包含：
○	一个数据框，其中包含顶级来源的年度发文量。
○	如果 graph=TRUE，则包含一个 ggplot 对象。
表 16：sourceGrowth 函数参数 (推断)
参数	类型	描述	默认值 (可能)
M	数据框	文献数据框。	无
top	整数	要分析的顶级来源数量。	10
graph	逻辑型	如果为 TRUE，则绘制增长图。	TRUE
2. bradford()：应用布拉德福定律分析文献分散度
●	目的：估计并绘制布拉德福分散定律图，该定律描述了特定主题的文献在期刊中的分布情况。它将来源划分为不同的生产力区域 1。
●	输入字段 101：文献数据框 M。主要使用 SO (来源) 字段来统计每个来源的文献数量。
●	参数 101：
○	M：文献数据框。
○	101。
●	计算方法/过程 101：
1.	统计每个来源 (期刊) 发表的文献数量。
2.	按文献数量降序排列来源。
3.	将来源划分为三个区域，使得每个区域包含大约三分之一的总文献量。
4.	统计每个区域中的来源数量。布拉德福定律表明，这些区域中来源数量的关系近似为 1:n:n2。
●	输出结果 101：一个列表，包含：
○	table：数据框，显示按区域划分的来源分布 (例如，来源名称、文献数量、所属区域)。
○	graph：一个 ggplot2 对象，可视化来源分布图。
表 17：bradford 函数参数
参数	类型	描述	默认值
M	数据框	文献数据框。	无
E. 科学产出率定律
1. lotka()：估计洛特卡定律系数
●	目的：估计科学产出率的洛特卡定律系数，该定律描述了作者按发文量分布的频率 1。
●	输入字段 58：一个 bibliometrix 对象 (biblioAnalysis 的输出 results)。特别使用 results$Authors (作者发文量统计)。
●	参数 58：
○	results：bibliometrix 对象。
○	(通常不需要其他参数如 k，因为它分析的是 results 中的完整作者分布)。
●	计算方法/过程 58：
1.	从 results$Authors 中提取作者发文频率。
2.	聚合数据以获得每种发文量的作者数量 (例如，X 位作者发表了 1 篇论文，Y 位作者发表了 2 篇论文)。
3.	使用对数转换值进行线性回归拟合洛特卡定律 (反平方定律, f(x)=C/xβ)：log10(作者频率) ~ log10(文章数量)。
4.	计算 Beta (指数，回归系数的绝对值) 和 C (常数，10截距)。
5.	计算 R 方以评估拟合优度。
6.	执行柯尔莫哥洛夫-斯米尔诺夫检验，比较观测分布与理论洛特卡分布 (通常假设 β=2)。
●	输出结果 58：一个列表，包含：
○	Beta：估计的 Beta 系数。
○	C：估计的常数 C。
○	R2：拟合优度 (R 方)。
○	fitted：模型的拟合值。
○	p.value：柯尔莫哥洛夫-斯米尔诺夫检验的 P 值。
○	AuthorProd：作者生产力分布的数据框 (文章数量、作者数量、频率)。
○	plot (通常隐式生成或可根据 AuthorProd 和拟合值生成)：显示观测分布与拟合分布的图表。
表 18：lotka 函数参数
参数	类型	描述	默认值
results	对象	bibliometrix 对象。	无
F. 趋势与时序分析
1. KeywordGrowth()：追踪顶级关键词的年度出现次数
●	目的：计算并可选地绘制顶级关键词/术语的年度出现次数，从而进行趋势分析 5。
●	输入字段 36：文献数据框 M。使用由 Tag 参数指定的关键词字段 (例如 ID, DE, 或由 termExtraction 创建的自定义字段如 TI_TM, AB_TM) 以及 PY (出版年份)。
●	参数 36：
○	M：数据框。
○	Tag：字符型，关键词字段标签 (例如 "ID", "DE", "TI_TM")。默认值："ID"。
○	sep：字符型，关键词间的分隔符。默认值：";"。
○	top：数值型，要分析的顶级关键词数量。默认值：10。
○	cdf：逻辑型。如果为 TRUE，则计算累积出现次数。默认值：TRUE。
○	remove.terms：字符向量，要排除的术语。默认值：NULL。
○	synonyms：字符向量，用于合并同义词。默认值：NULL。
○	graph 36：逻辑型，如果为 TRUE，则生成图表。
●	计算方法/过程 36：
1.	从指定的 Tag 字段中提取关键词，如果提供了 remove.terms 和 synonyms，则应用它们。
2.	识别总体上频率最高的 top 个关键词。
3.	对于这些顶级关键词，统计它们在每年 (PY) 的出现次数。
4.	如果 cdf=TRUE，则计算历年的累积频率。
5.	如果 graph=TRUE (推断)，则生成一个 ggplot2 折线图显示趋势。
●	输出结果 36：一个数据框，第一列为 Year，后续各列对应每个顶级关键词，显示其年度 (或累积) 频率。如果启用了绘图，则 ggplot 对象也可能是列表的一部分或直接显示。
表 19：KeywordGrowth 函数参数
参数	类型	描述	默认值
M	数据框	文献数据框。	无
Tag	字符型	关键词字段标签 (例如 "ID", "DE", "TI_TM")。	"ID"
sep	字符型	关键词间的分隔符。	";"
top	数值型	要分析的顶级关键词数量。	10
cdf	逻辑型	如果为 TRUE，则计算累积出现次数。	TRUE
remove.terms	字符向量	要排除的术语列表。	NULL
synonyms	字符向量	用于合并同义词的列表。每个元素包含以";"分隔的同义词，将合并为第一个词。	NULL
2. fieldByYear()：按年份分析字段标签分布
●	目的：计算并绘制选定字段标签的元素在不同年份的分布情况（通常是中位数年份或顶级项目的频率），用于观察不同类别（如特定关键词、研究领域、国家）的活跃度如何随时间变化 5。
●	输入字段 62：文献数据框 M。使用由 field 参数指定的字段 (例如 "ID", "DE", "SO", "AU", "C1") 以及 PY (出版年份)。
●	参数 62：
○	M：数据框。
○	field：字符型，要分析的字段标签。默认值："ID"。
○	timespan：数值向量 c(min_year, max_year)。如果为 NULL (默认值)，则分析整个时期。
○	min.freq：整数，项目被纳入分析的最小频率。默认值：2。
○	n.items：整数，图表中每年包含的最大项目数。默认值：5。
○	labelsize：已弃用。
○	remove.terms：字符向量，要排除的术语。默认值：NULL。
○	synonyms：字符向量，用于合并同义词。默认值：NULL。
○	dynamic.plot：逻辑型。如果为 TRUE，则优化绘图以适应 plotly 包。默认值：FALSE。
○	graph：逻辑型。如果为 TRUE (默认值)，则绘制分布图。
●	计算方法/过程 62：
1.	按 timespan 筛选数据。
2.	识别 field 中满足 min.freq 的唯一项目。
3.	统计这些项目每年的出现次数。
4.	为绘图选择每年频率最高的 n.items 个项目。
5.	计算每个项目的中位数年份 62。
6.	如果 graph=TRUE，则生成一个 ggplot2 图表。
●	输出结果 62：一个列表，包含：
○	df：数据框，包含分布数据 (项目、年份、频率/中位数年份)。
○	df_graph：为生成图表而格式化的数据框。
○	graph：一个 ggplot 对象。
表 20：fieldByYear 函数参数
参数	类型	描述	默认值
M	数据框	文献数据框。	无
field	字符型	要分析的字段标签。	"ID"
timespan	数值向量	c(min_year, max_year) 定义分析的时间跨度。如果为 NULL，则分析整个时期。	NULL
min.freq	整数	项目被纳入分析的最小频率。	2
n.items	整数	图表中每年包含的最大项目数。	5
remove.terms	字符向量	要排除的术语列表。	NULL
synonyms	字符向量	用于合并同义词的列表。	NULL
dynamic.plot	逻辑型	如果为 TRUE，则优化绘图以适应 plotly 包。	FALSE
graph	逻辑型	如果为 TRUE，则绘制分布图。	TRUE
3. rpys()：参考文献出版年份谱分析
●	目的：计算并绘制参考文献出版年份谱 (RPYS)，通过分析被引文献的出版年份来检测研究领域的历史根源 (Marx et al., 2014) 1。RPYS 专门分析被引参考文献的出版年份分布，其假设是领域内基础性或历史性重要论文即使在很久以后仍会被频繁引用。被引参考文献出版年份分布的峰值可以指示那些发表了塑造该领域的开创性著作的时期，从而帮助识别一个领域的知识谱系。
●	输入字段 64：文献数据框 M。主要使用 CR (被引文献) 字段从中提取参考文献的出版年份，以及 PY (引用文献的出版年份)。
●	参数 64：
○	M：数据框。
○	sep：字符型，CR 中被引文献间的分隔符。默认值：";"。
○	timespan：数值向量 c(min_year, max_year)，用于筛选被引文献的年份。默认值：NULL (整个时间跨度)。
○	graph：逻辑型。如果为 TRUE (默认值)，则绘制谱图。
●	计算方法/过程 64：
1.	解析 M 中所有文献的 CR 字段。
2.	从每个被引文献中提取出版年份。
3.	如果提供了 timespan，则根据其筛选这些被引文献的年份。
4.	统计被引文献中每个出版年份的频率。
5.	生成一个显示这些频率的图表 (谱图)，其中的峰值指示了具有历史影响力的出版年份。
●	输出结果 64：一个列表，包含：
○	graph：谱图 (ggplot2 类)。
○	df_spectroscopy：数据框，包含被引文献的年度被引次数。
○	df_references：数据框，列出每年的被引文献。
○	df_reference_list_by_year：数据框，包含参考文献列表及逐年记录的被引次数。
G. 其他描述性工具
1. tableTag()：对标签字段列中的元素进行制表
●	目的：对文献数据框中某个标签字段列的元素进行制表统计，即生成一个频次表 1。
●	输入字段 110：文献数据框 M。具体字段由 Tag 参数指定，可以是 CR (被引文献), DE (作者关键词), ID (扩展关键词), AU (作者) 等。
●	参数 110：
○	M：数据框。
○	Tag：字符型，指示标准 ISI WoS 字段标签编码中的一个字段标签。默认值："CR"。
○	sep：字符型，字段分隔符。默认值：";"。
○	ngrams：整数，1 到 3 之间。指示从标题或摘要中提取的 n-gram 类型 (仅当 Tag 指向文本字段时相关)。
○	remove.terms：字符向量，在术语提取前要从文献中删除的附加术语列表。默认值：NULL。
○	synonyms：字符向量。每个元素包含一个同义词列表（以";"分隔），这些同义词将被合并为单个术语（列表中的第一个词）。默认值：NULL。
●	计算方法/过程 110：
1.	读取 M 中由 Tag 指定的列。
2.	根据 sep 分割字符串以识别单个元素。
3.	统计每个唯一元素的出现次数，生成频次表。
4.	如果 Tag 指向文本字段且 ngrams > 1，则提取 n-grams 而非单个词。
●	输出结果 110：一个 table 类的对象，包含指定标签字段中元素的频次计数。
2. threeFieldsPlot()：三字段图 (Sankey 图)
●	目的：通过桑基图 (Sankey diagram) 可视化三个字段（例如作者、关键词、期刊）主要项目之间的关联关系 1。
●	输入字段 113：文献数据框 M。具体字段由 fields 参数指定，例如 AU (作者), DE (作者关键词), SO (来源期刊), C1 (作者机构国家部分)。
●	参数 113：
○	M：文献数据框。
○	fields：字符向量，指示要使用标准 WoS 字段标签进行分析的字段。默认值：c("AU", "DE", "SO")。
○	n：整数向量，指示每个字段要绘制的项目数量。默认值：c(20, 20, 20)。
●	计算方法/过程 114：
1.	识别 M 中三个选定字段内出现频率最高的 n 个元素。
2.	分析这些顶级元素在文献中的共现情况，创建它们之间的链接和流。
3.	例如，一个特定的作者（来自 AU 字段的顶级元素）使用了某些特定的关键词（来自 DE 字段的顶级元素）并发表在特定的期刊上（来自 SO 字段的顶级元素），这三者之间的共现会形成一个流。流的宽度通常与共现频率成正比。
●	输出结果 114：一个桑基图对象 (sankeyPlot 对象，可能使用 networkD3 或类似包生成 HTML 小部件)，可视化三个选定字段顶级元素之间的联系和流。
IV. 模块 3：网络分析 - 揭示结构关系
此模块专注于构建和分析各种类型的文献计量网络，以揭示研究领域内的实体（如作者、文献、关键词、国家等）之间的结构性关系。
A. 网络矩阵构建
1. cocMatrix()：构建文献计量二分网络矩阵
●	目的：计算文献数据框中某个标签字段内元素间的共现情况，其中手稿（文献）是分析的基本单元。生成的共现矩阵代表一个二分网络，这个网络可以被转换为多种文献计量网络，如耦合网络、共被引网络等 1。
●	输入字段 17：文献数据框 M。具体操作的字段由 Field 参数指定，例如 AU (作者), DE (作者关键词), ID (扩展关键词), SO (来源期刊), CR (被引文献) 等。此外，函数隐式地使用 M 的行作为文献的标识符。
●	参数 17：
○	M：由 convert2df 函数获得的数据框。
○	Field：字符型，指示标准 ISI WoS 字段标签编码中的一个字段标签。默认值："AU"。
○	type：字符型，指定输出矩阵的格式。"matrix" 生成一个标准的密集矩阵对象，"sparse" (默认值) 生成一个 Matrix 包中的 dgCMatrix 稀疏矩阵对象，这种格式对于大型稀疏矩阵更高效。
○	n：整数，指示要选择的项目数量。如果为 NULL，则选择所有项目。
○	sep：字符型，字段分隔符。默认值：";"。
○	binary：逻辑型。如果为 TRUE (默认值)，每个单元格包含 0/1（表示是否存在）。如果为 FALSE，每个单元格包含频次。
○	short：逻辑型。如果为 TRUE，则删除频次低于2的所有项目以减小矩阵大小。默认值：FALSE。
○	remove.terms：字符向量，在术语提取前要从文献中删除的附加术语列表。默认值：NULL。
○	synonyms：字符向量。每个元素包含一个同义词列表（以";"分隔），这些同义词将被合并为单个术语（列表中的第一个词）。默认值：NULL。
●	计算方法/过程 120：
1.	元素提取：对于 M 中的每篇文献（行），从 Field 参数指定的列中提取元素。如果提供了 sep 参数，则根据分隔符分割字符串以识别单个元素（例如，以";"分隔的多个作者）。
2.	唯一元素识别：识别所选 Field 中所有文献中出现的所有唯一元素。这些唯一元素将构成输出矩阵的列。
3.	矩阵构建：创建一个矩阵，其中行代表文献，列代表唯一元素。
4.	共现处理：对于每篇文献和每个唯一元素，确定该元素是否存在于该文献的指定 Field 中。
■	如果 binary = TRUE，矩阵中的相应单元格在元素存在于文献中时设为 1，否则为 0。
■	如果 binary = FALSE，相应单元格包含该元素在文献指定 Field 中出现的次数。
5.	可选的过滤和清洗：
■	如果指定了 n，则过滤矩阵以仅包括频率最高的 n 个元素。
■	如果 short = TRUE，则移除在所有文献中总频率低于 2 的元素。
■	如果提供了 remove.terms，则在矩阵构建前移除指定术语的任何出现。
■	如果提供了 synonyms，则函数将同义术语的出现合并为单个术语。
●	输出结果 17：一个二分网络矩阵。其格式由 type 参数决定：
○	如果 type = "matrix"，输出为 matrix 类的密集矩阵。
○	如果 type = "sparse"，输出为 Matrix 包中的 dgCMatrix 类稀疏矩阵。 这个二分矩阵（一侧为文献，另一侧为从标签字段提取的元素）是使用 bibliometrix 包中其他函数（如 biblioNetwork）创建各种文献计量网络（如合著网络、关键词共现网络和引文网络）的基础。
2. biblioNetwork()：创建各种文献计量网络
●	目的：从文献数据框创建不同类型的文献计量网络，如合作网络、共被引网络、耦合网络和共现网络 1。
●	输入字段 121：文献数据框 M。具体使用的字段因 analysis 和 network 参数的类型而异。例如：
○	作者网络：AU (作者)。
○	参考文献网络：CR (被引文献)。
○	来源网络：SO (来源标题)。
○	国家网络：C1 (作者机构，从中提取国家信息)。
○	关键词网络：DE (作者关键词) 或 ID (扩展关键词)。
○	标题网络：文献标题字段。
○	摘要网络：文献摘要字段。
●	参数 2：
○	M：文献数据框。
○	analysis：字符型，指定要执行的网络分析类型。可选值："collaboration" (合作), "coupling" (耦合), "co-occurrences" (共现), "co-citation" (共被引)。默认值："coupling"。
○	network：字符型，指示基于所选 analysis 创建的具体网络类型。可选值取决于 analysis，例如："authors" (作者), "references" (参考文献), "sources" (来源), "countries" (国家), "universities" (大学), "keywords" (关键词), "author_keywords" (作者关键词), "titles" (标题), "abstracts" (摘要)。默认值："authors"。
○	n：整数，网络中包含的项目数量（例如，顶级作者、顶级被引文献）。如果为 NULL，则选择所有项目。
○	sep：字符型，输入数据框 M 列中使用的字段分隔符。默认值：";"。
○	short：逻辑型。如果为 TRUE，则从网络中移除频率低于 2 的项目（例如作者、关键词）。默认值：FALSE。
○	shortlabel：逻辑型。如果为 TRUE，网络中的参考文献标签将以短格式存储。默认值：TRUE。
○	remove.terms：字符向量，在术语提取前要从文献中删除的附加术语列表（与 "titles" 和 "abstracts" 网络相关）。默认值：NULL。
○	synonyms：字符向量，每个元素包含以";"分隔的同义词列表。这些同义词将被合并为单个术语（列表中的第一个词）。默认值：NULL。
●	计算方法/过程 121：
○	共被引 (Co-citation)：例如，对于作者共被引，函数识别在数据集中文献的参考文献中被共同引用的作者对。矩阵单元格 (i, j) 的值表示作者 i 和作者 j 被共同引用的次数。通常的计算逻辑是 C=ATA，其中 A 是一个文献 × 被引对象（如被引文献、被引作者）的二分网络矩阵（可由 cocMatrix 生成）。
○	耦合 (Coupling)：例如，对于文献耦合（基于参考文献），函数识别引用相同参考文献的文献对。矩阵单元格 (i, j) 的值表示文献 i 和文献 j 共同引用的参考文献数量。计算逻辑通常是 C=AAT，其中 A 是一个文献 × 引用对象（如参考文献）的二分网络矩阵。
○	合作 (Collaboration)：例如，对于作者合作，函数识别两位或多位作者共同署名的文献。矩阵单元格 (i, j) 的值表示作者 i 和作者 j 合作发表的文献数量。
○	共现 (Co-occurrence)：例如，对于关键词共现，函数识别两个或多个关键词共同出现的文献。矩阵单元格 (i, j) 的值表示关键词 i 和关键词 j 在同一文献中共同出现的次数。
●	输出结果 121：一个方形网络矩阵。该矩阵是 R 中 Matrix 包的 dgCMatrix 类对象。它表示文献计量网络，其中行和列都对应于所分析的实体（例如作者、参考文献、来源、关键词）。矩阵单元格中的值表示相应实体之间关系的强度，具体取决于执行的分析类型（共被引计数、耦合强度、合作次数、共现频率等）。此邻接矩阵随后可用于 bibliometrix 包中的其他函数（如 networkPlot）进行进一步的网络分析和可视化。
3. normalizeSimilarity()：计算网络中顶点间的相似性指数
●	目的：计算网络中顶点（节点）之间的相对文献计量共现度量，即量化项目（如关键词、作者或文献）之间基于它们在文献数据集中共同出现频率的相似性 1。
●	输入字段 66：一个网络矩阵 (NetMatrix)，通常由 biblioNetwork 或 cocMatrix 函数生成。
●	参数 66：
○	NetMatrix：由 biblioNetwork 或 cocMatrix 函数获得的耦合矩阵或共现矩阵。
○	type：字符型，指定要计算的相似性指数类型。可选值：
■	"association" (关联强度，默认值)：Van Eck 和 Waltman 使用的关联强度，也称为邻近指数或概率亲和度/活动指数。
■	"jaccard" (杰卡德指数)：衡量两组参考文献列表重叠的相对程度，计算公式为交集与并集的比率，即 ∣A∩B∣/∣A∪B∣。
■	"inclusion" (包含指数/辛普森系数)：信息检索中使用的重叠度量。
■	"salton" (萨尔顿指数/余弦相似度)：将两个列表的交集与两个集合大小的几何平均数相关联，即 ∣A∩B∣/(∣A∣⋅∣B∣)。
■	"equivalence" (等价指数)：萨尔顿指数的平方，即 (∣A∩B∣)2/(∣A∣⋅∣B∣)。
●	计算方法/过程 66：根据所选 type 计算相应的相似性指数。如果两个项目（例如，两篇手稿的参考文献列表）的交集为空，则所有这些指数的值均为零。
●	输出结果 66：一个相似性矩阵。该矩阵包含输入网络中不同顶点之间根据所选相似性类型计算出的相似性指数。矩阵中的值已根据特定相似性指数的公式进行了归一化，提供了网络元素间关系强度的相对度量。
4. couplingSimilarity() (注意：在较新版本中，其功能已整合或被 normalizeSimilarity 取代)
●	目的：计算文献计量耦合的相对度量，特别是杰卡德或萨尔顿相似性系数 1。
●	输入字段 123：一个耦合矩阵 (NetMatrix)，由 biblioNetwork 或 cocMatrix 函数获得。
●	参数 123：
○	NetMatrix：耦合矩阵。
○	type：字符型，"jaccard" (默认值) 或 "salton"。
●	计算方法/过程 123：
○	杰卡德指数：计算公式为两篇手稿参考文献列表的交集与并集的比率。
○	萨尔顿指数：将两个列表的交集与两个列表大小的几何平均数相关联。 如果参考文献列表的交集为空，则两个指数均为零；如果两个列表完全相同，则最大值为一。
●	输出结果 123：一个相似性矩阵。
●	注释：6 指出 normalizeSimilarity 用于对耦合网络的边进行归一化，并提及了萨尔顿指数和关联强度。66 的 normalizeSimilarity 文档中包含了 Jaccard、Salton、Association Strength 和 Inclusion Index。这表明 couplingSimilarity 的功能可能已被更通用的 normalizeSimilarity 函数所包含或取代。在当前版本的 bibliometrix (如 4.x) 中，normalizeSimilarity 是进行此类计算的主要函数。
B. 网络统计与可视化
1. networkStat()：计算网络摘要统计信息
●	目的：计算主要的网络统计数据，既可以针对整个网络，也可以针对构成网络的单个节点 1。
●	输入字段 67：一个网络矩阵（由 biblioNetwork 函数获得）或一个 igraph 类的图对象。
●	参数 67：
○	object：网络矩阵或 igraph 对象。
○	`stat
引用的著作
1.	Bibliometrix - Wikipedia, 访问时间为 五月 15, 2025， https://en.wikipedia.org/wiki/Bibliometrix
2.	massimoaria/bibliometrix: An R-tool for comprehensive science mapping analysis. A package for quantitative research in scientometrics and bibliometrics. - GitHub, 访问时间为 五月 15, 2025， https://github.com/massimoaria/bibliometrix
3.	bibliometrix package - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/3.0.0
4.	A brief introduction to bibliometrix - The Comprehensive R Archive Network, 访问时间为 五月 15, 2025， http://cran.nexr.com/web/packages/bibliometrix/vignettes/bibliometrix-vignette.html
5.	bibliometrix package - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.2
6.	A brief introduction to bibliometrix, 访问时间为 五月 15, 2025， https://www.bibliometrix.org/vignettes/Introduction_to_bibliometrix.html
7.	bibliometrix-package function - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.3/topics/bibliometrix-package
8.	bibliometrix package - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/2.1.2
9.	Biblioshiny - Bibliometrix, 访问时间为 五月 15, 2025， https://www.bibliometrix.org/home/<USER>/layout/bibliometrix
10.	Aria, M. and Cuccurullo, C. (2017) Bibliometrix An R-tool for Comprehensive Science Mapping Analysis. Journal of Informetrics, 11, 959-975. - References - Scientific Research Publishing, 访问时间为 五月 15, 2025， https://www.scirp.org/reference/referencespapers?referenceid=3393723
11.	Aria, M., & Cuccurullo, C. (2017). Bibliometrix An R-Tool for Comprehensive Science Mapping Analysis. Journal of Informetrics, 11, 959-975. - References - Scientific Research Publishing, 访问时间为 五月 15, 2025， https://www.scirp.org/reference/referencespapers?referenceid=3565332
12.	bibliometrix-package: Comprehensive Science Mapping Analysis - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/bibliometrix-package.html
13.	bibliometrix: Comprehensive Science Mapping Analysis version 4.3.3 from CRAN - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/
14.	Bibliometrix Seminar | PPT - SlideShare, 访问时间为 五月 15, 2025， https://www.slideshare.net/MassimoAria/bibliometrix-phd-seminar
15.	Data Importing and Converting - Bibliometrix, 访问时间为 五月 15, 2025， https://www.bibliometrix.org/vignettes/Data-Importing-and-Converting.html
16.	FAQ - Bibliometrix, 访问时间为 五月 15, 2025， https://www.bibliometrix.org/home/<USER>/faq
17.	cocMatrix Bibliographic bipartite network matrices - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.3/topics/cocMatrix
18.	bibliometrix package - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/2.2.0
19.	bibliometrix/R/summary.bibliometrix.R at master · massimoaria/bibliometrix - GitHub, 访问时间为 五月 15, 2025， https://github.com/massimoaria/bibliometrix/blob/master/R/summary.bibliometrix.R
20.	Bibliometric Analysis - R, 访问时间为 五月 15, 2025， https://search.r-project.org/CRAN/refmans/bibliometrix/help/biblioAnalysis.html
21.	Science Mapping Analysis with bibliometrix R-package: an example, 访问时间为 五月 15, 2025， https://bibliometrix.org/documents/bibliometrix_Report.html
22.	R BibliometRics - Prof Dr. Roberto Campos Leoni - RPubs, 访问时间为 五月 15, 2025， https://rpubs.com/rcleoni/253042
23.	bibtag: Tag list and bibtex fields. in bibliometrix: Comprehensive ..., 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/bibtag.html
24.	bibliometrix source: R/termExtraction.R - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/src/R/termExtraction.R
25.	bibliometrix/R/readFiles.R at master - GitHub, 访问时间为 五月 15, 2025， https://github.com/massimoaria/bibliometrix/blob/master/R/readFiles.R
26.	aprijunaidi/scopus-wos-combine - GitHub, 访问时间为 五月 15, 2025， https://github.com/aprijunaidi/scopus-wos-combine
27.	bibliometrix/R/mergeDbSources.R at master - GitHub, 访问时间为 五月 15, 2025， https://github.com/massimoaria/bibliometrix/blob/master/R/mergeDbSources.R
28.	massimoaria/bibliometrix source: R/missingData.R - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/github/massimoaria/bibliometrix/src/R/missingData.R
29.	missingData: Completeness of bibliographic metadata in bibliometrix: Comprehensive Science Mapping Analysis - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/missingData.html
30.	massimoaria/bibliometrix: README.md - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/github/massimoaria/bibliometrix/f/README.md
31.	duplicatedMatching: Searching of duplicated records in a bibliographic database - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.0/topics/duplicatedMatching
32.	duplicatedMatching: Searching of duplicated records in a ... - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/duplicatedMatching.html
33.	metaTagExtraction: Meta-Field Tag Extraction in bibliometrix: Comprehensive Science Mapping Analysis - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/metaTagExtraction.html
34.	bibliometrix/R/metaTagExtraction.R at master - GitHub, 访问时间为 五月 15, 2025， https://github.com/cran/bibliometrix/blob/master/R/metaTagExtraction.R
35.	bibliometrix source: R/Hindex.R - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/src/R/Hindex.R
36.	bibliometrix/R/keywordGrowth.R at master · massimoaria ... - GitHub, 访问时间为 五月 15, 2025， https://github.com/massimoaria/bibliometrix/blob/master/R/keywordGrowth.R
37.	trim: Deleting leading and ending white spaces in bibliometrix: Comprehensive Science Mapping Analysis - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/trim.html
38.	bibliometrix – R documentation - Quantargo, 访问时间为 五月 15, 2025， https://www.quantargo.com/help/r/latest/packages/bibliometrix/3.1.3
39.	trim.leading: Deleting leading white spaces in bibliometrix: Comprehensive Science Mapping Analysis - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/trim.leading.html
40.	trimES function - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/2.3.1/topics/trimES
41.	bibliometrix/R/termExtraction.R at master - GitHub, 访问时间为 五月 15, 2025， https://github.com/massimoaria/bibliometrix/blob/master/R/termExtraction.R
42.	stopwords/R/stopwords.R at master · quanteda/stopwords - GitHub, 访问时间为 五月 15, 2025， https://github.com/quanteda/stopwords/blob/master/R/stopwords.R
43.	Stop words - Database Search Tips - LibGuides at Central European University, 访问时间为 五月 15, 2025， https://ceu.libguides.com/databasesearchtips/stop-words
44.	stopwords function - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.2/topics/stopwords
45.	bibliometrix source: R/conceptualStructure.R - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/src/R/conceptualStructure.R
46.	conceptualStructure: Creating and plotting conceptual structure map of a... in bibliometrix: Comprehensive Science Mapping Analysis - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/conceptualStructure.html
47.	countries: Index of Countries. in bibliometrix: Comprehensive ..., 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/countries.html
48.	bibliometrix/R/biblioAnalysis.R at master - GitHub, 访问时间为 五月 15, 2025， https://github.com/cran/bibliometrix/blob/master/R/biblioAnalysis.R
49.	summary.bibliometrix: Summarizing bibliometric analysis results in ..., 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/summary.bibliometrix.html
50.	plot.bibliometrix function - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/4.1.2/topics/plot.bibliometrix
51.	bibliometrix/R/plot.bibliometrix.R at master · cran/bibliometrix · GitHub, 访问时间为 五月 15, 2025， https://github.com/cran/bibliometrix/blob/master/R/plot.bibliometrix.R
52.	R/plot.bibliometrix.R - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/src/R/plot.bibliometrix.R
53.	citations function - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/4.1.2/topics/citations
54.	localCitations function - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.0/topics/localCitations
55.	bibliometrix/R/localCitations.R at master - GitHub, 访问时间为 五月 15, 2025， https://github.com/massimoaria/bibliometrix/blob/master/R/localCitations.R
56.	访问时间为 一月 1, 1970， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.2/topics/localCitations
57.	bibliometrix package - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.5
58.	bibliometrix source: R/lotka.R - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/src/R/lotka.R
59.	bibliometrix package - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.3
60.	authorProdOverTime: Top-Authors' Productivity over Time in ... - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/authorProdOverTime.html
61.	bibliometrix API and function index - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/api/
62.	fieldByYear Field Tag distribution by Year - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.1/topics/fieldByYear
63.	fieldByYear: Field Tag distribution by Year in bibliometrix: Comprehensive Science Mapping Analysis - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/fieldByYear.html
64.	bibliometrix source: R/rpys.R - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/src/R/rpys.R
65.	bibliometrix source: R/cocMatrix.R - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/src/R/cocMatrix.R
66.	normalizeSimilarity Calculate similarity indices - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.3/topics/normalizeSimilarity
67.	bibliometrix source: R/networkStat.R - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/src/R/networkStat.R
68.	bibliometrix source: R/networkPlot.R - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/src/R/networkPlot.R
69.	net2VOSviewer Open a bibliometrix network in VosViewer - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.3/topics/net2VOSviewer
70.	net2VOSviewer: Open a bibliometrix network in VosViewer - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/net2VOSviewer.html
71.	bibliometrix source: R/splitCommunities.R - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/src/R/splitCommunities.R
72.	bibliometrix source: R/thematicEvolution.R - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/src/R/thematicEvolution.R
73.	Man pages for bibliometrix Comprehensive Science Mapping Analysis - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/
74.	bibliometrix source: R/histNetwork.R - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/src/R/histNetwork.R
75.	bibliometrix source: R/histPlot.R - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/src/R/histPlot.R
76.	biblioshiny: Shiny UI for bibliometrix package - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/biblioshiny.html
77.	retrievalByAuthorID: Get Author Content on SCOPUS by ID in bibliometrix - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/retrievalByAuthorID.html
78.	normalizeCitationScore: Calculate the normalized citation score metric in bibliometrix: Comprehensive Science Mapping Analysis - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/normalizeCitationScore.html
79.	bibliometrix source: R/normalizeCItationScore.R - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/src/R/normalizeCItationScore.R
80.	bibliometrix source: R/couplingMap.R - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/src/R/couplingMap.R
81.	couplingMap: Coupling Analysis in bibliometrix - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/couplingMap.html
82.	timeslice: Bibliographic data frame time slice in bibliometrix: Comprehensive Science Mapping Analysis - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/timeslice.html
83.	dominance function - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.0/topics/dominance
84.	访问时间为 一月 1, 1970， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.2/topics/dominance
85.	Hindex function - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/4.1.2/topics/Hindex
86.	访问时间为 一月 1, 1970， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.2/topics/Hindex
87.	bibliometrix/R/authorProdOverTime.R at master - GitHub, 访问时间为 五月 15, 2025， https://github.com/massimoaria/bibliometrix/blob/master/R/authorProdOverTime.R
88.	authorProdOverTime function - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/3.2/topics/authorProdOverTime
89.	访问时间为 一月 1, 1970， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.2/topics/authorProdOverTime
90.	访问时间为 一月 1, 1970， httpsrdrr.io/cran/bibliometrix/man/authorProdOverTime.html
91.	histNetwork: Historical co-citation network in bibliometrix: Comprehensive Science Mapping Analysis - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/histNetwork.html
92.	histPlot: Plotting historical co-citation network in bibliometrix: Comprehensive Science Mapping Analysis - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/histPlot.html
93.	idByAuthor: Get Complete Author Information and ID from Scopus in bibliometrix - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/idByAuthor.html
94.	bibliometrix source: R/retrievalByAuthorID.R - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/src/R/retrievalByAuthorID.R
95.	logo: Bibliometrix logo. in bibliometrix: Comprehensive Science ..., 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/logo.html
96.	customTheme: Custom Theme variables for Biblioshiny. in ... - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/customTheme.html
97.	Custom Theme variables for Biblioshiny. - R, 访问时间为 五月 15, 2025， https://search.r-project.org/CRAN/refmans/bibliometrix/html/customTheme.html
98.	访问时间为 一月 1, 1970， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.2/topics/sourceGrowth
99.	访问时间为 一月 1, 1970， httpsrdrr.io/cran/bibliometrix/man/sourceGrowth.html
100.	访问时间为 一月 1, 1970， https://rdrr.io/cran/bibliometrix/man/sourceGrowth.html
101.	bradford function - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/4.1.2/topics/bradford
102.	访问时间为 一月 1, 1970， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.2/topics/bradford
103.	访问时间为 一月 1, 1970， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.2/topics/lotka
104.	KeywordGrowth function - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/4.0.0/topics/KeywordGrowth
105.	访问时间为 一月 1, 1970， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.2/topics/KeywordGrowth
106.	fieldByYear function - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.2/topics/fieldByYear
107.	rpys: Reference Publication Year Spectroscopy in bibliometrix ..., 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/rpys.html
108.	访问时间为 一月 1, 1970， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.2/topics/rpys
109.	访问时间为 一月 1, 1970， httpsrdrr.io/cran/bibliometrix/man/rpys.html
110.	tableTag function - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.0/topics/tableTag
111.	访问时间为 一月 1, 1970， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.2/topics/tableTag
112.	访问时间为 一月 1, 1970， httpsrdrr.io/cran/bibliometrix/man/tableTag.html
113.	threeFieldsPlot function - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/4.1.4/topics/threeFieldsPlot
114.	threeFieldsPlot: Three Fields Plot in bibliometrix: Comprehensive ..., 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/threeFieldsPlot.html
115.	访问时间为 一月 1, 1970， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.2/topics/threeFieldsPlot
116.	访问时间为 一月 1, 1970， httpsrdrr.io/cran/bibliometrix/man/threeFieldsPlot.html
117.	bibliometrix/R/cocMatrix.R at master - GitHub, 访问时间为 五月 15, 2025， https://github.com/massimoaria/bibliometrix/blob/master/R/cocMatrix.R
118.	thematicMap function - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/1.9.4/topics/thematicMap
119.	keywordAssoc ID and DE keyword associations - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/4.3.3/topics/keywordAssoc
120.	cocMatrix: Bibliographic bipartite network matrices in bibliometrix ..., 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/cocMatrix.html
121.	biblioNetwork: Creating Bibliographic networks in bibliometrix ..., 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/biblioNetwork.html
122.	normalizeSimilarity: Calculate similarity indices in bibliometrix ..., 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/normalizeSimilarity.html
123.	couplingSimilarity Coupling similarity index - RDocumentation, 访问时间为 五月 15, 2025， https://www.rdocumentation.org/packages/bibliometrix/versions/1.6/topics/couplingSimilarity
124.	net2Pajek: Save a network graph object as Pajek files in massimoaria/bibliometrix: Comprehensive Science Mapping Analysis - rdrr.io, 访问时间为 五月 15, 2025， https://rdrr.io/github/massimoaria/bibliometrix/man/net2Pajek.html
125.	networkStat: Calculating network summary statistics in bibliometrix ..., 访问时间为 五月 15, 2025， https://rdrr.io/cran/bibliometrix/man/networkStat.html
126.	www.bibliometrix.org, 访问时间为 五月 15, 2025， https://www.bibliometrix.org/documents/Field_Tags_bibliometrix.pdf
