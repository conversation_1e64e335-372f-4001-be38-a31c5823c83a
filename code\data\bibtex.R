# =================================================================================================
# === BibTeX转换模块 ===
# =================================================================================================
# 版本: 1.0.0
# 描述: 将OpenAlex数据转换为BibTeX格式

# 加载必要的包
required_packages <- c(
  "jsonlite",
  "tidyverse",
  "lubridate"
)

# 检查并安装缺失的包
missing_packages <- required_packages[!required_packages %in% installed.packages()[,"Package"]]
if (length(missing_packages) > 0) {
  install.packages(missing_packages)
}

# 加载所有必要的包
for (pkg in required_packages) {
  suppressPackageStartupMessages(library(pkg, character.only = TRUE))
}

# 处理作者信息
process_authors <- function(authorships) {
  if (is.null(authorships) || length(authorships) == 0) return(NULL)
  
  # 提取作者名称
  author_names <- sapply(authorships, function(x) {
    if (!is.null(x$author$display_name)) {
      # 处理作者名称格式
      name_parts <- strsplit(x$author$display_name, " ")[[1]]
      if (length(name_parts) > 1) {
        # 将姓放在前面
        paste(name_parts[length(name_parts)], 
              paste(name_parts[-length(name_parts)], collapse = " "))
      } else {
        x$author$display_name
      }
    }
  })
  
  # 用"and"连接所有作者
  paste(author_names, collapse = " and ")
}

# 处理摘要
process_abstract <- function(abstract_inverted_index) {
  if (is.null(abstract_inverted_index)) return(NULL)
  
  # 将倒排索引转换为普通文本
  words <- names(abstract_inverted_index)
  positions <- unlist(abstract_inverted_index)
  sorted_words <- words[order(positions)]
  paste(sorted_words, collapse = " ")
}

# 处理关键词
process_keywords <- function(keywords, topics) {
  all_keywords <- c()
  
  # 处理关键词
  if (!is.null(keywords) && length(keywords) > 0) {
    all_keywords <- c(all_keywords, keywords$display_name)
  }
  
  # 处理主题
  if (!is.null(topics) && length(topics) > 0) {
    all_keywords <- c(all_keywords, topics$display_name)
  }
  
  # 去重并合并
  if (length(all_keywords) > 0) {
    paste(unique(all_keywords), collapse = ", ")
  } else {
    NULL
  }
}

# 处理参考文献
process_references <- function(referenced_works) {
  if (is.null(referenced_works) || length(referenced_works) == 0) return(NULL)
  
  # 提取参考文献ID
  ref_ids <- sapply(referenced_works, function(x) {
    gsub("https://openalex.org/", "", x)
  })
  
  paste(ref_ids, collapse = ", ")
}

# 将OpenAlex数据转换为BibTeX格式
convert_to_bibtex <- function(work_data) {
  # 创建基本条目
  bib_entry <- list(
    type = "article",
    key = gsub("https://openalex.org/", "", work_data$id),
    title = work_data$display_name,
    year = format(as.Date(work_data$publication_date), "%Y"),
    author = process_authors(work_data$authorships)
  )
  
  # 添加期刊信息
  if (!is.null(work_data$locations) && length(work_data$locations) > 0) {
    bib_entry$journal <- work_data$locations[[1]]$source$display_name
    bib_entry$issn <- work_data$locations[[1]]$source$issn_l
    bib_entry$url <- work_data$locations[[1]]$landing_page_url
    bib_entry$pdf <- work_data$locations[[1]]$pdf_url
  }
  
  # 添加摘要
  bib_entry$abstract <- process_abstract(work_data$abstract_inverted_index)
  
  # 添加关键词
  bib_entry$keywords <- process_keywords(work_data$keywords, work_data$topics)
  
  # 添加引用信息
  if (!is.null(work_data$cited_by_count)) {
    bib_entry$citation_count <- work_data$cited_by_count
  }
  
  # 添加参考文献
  bib_entry$references <- process_references(work_data$referenced_works)
  
  # 移除NULL值
  bib_entry <- bib_entry[!sapply(bib_entry, is.null)]
  
  return(bib_entry)
}

# 将BibTeX条目转换为字符串
bibtex_to_string <- function(bib_entry) {
  # 创建BibTeX格式的字符串
  bib_str <- sprintf("@%s{%s,\n", bib_entry$type, bib_entry$key)
  
  # 添加所有字段
  for (field in names(bib_entry)[-1]) {
    bib_str <- paste0(bib_str, sprintf("  %s = {%s},\n", field, bib_entry[[field]]))
  }
  
  # 移除最后一个逗号并添加结束括号
  bib_str <- sub(",\n$", "\n", bib_str)
  bib_str <- paste0(bib_str, "}\n")
  
  return(bib_str)
}

# 保存BibTeX文件
save_bibtex <- function(bib_entries, filename, base_dir = "data") {
  # 创建BibTeX目录
  bibtex_dir <- file.path(base_dir, "bibtex")
  if (!dir.exists(bibtex_dir)) {
    dir.create(bibtex_dir, recursive = TRUE)
  }
  
  # 生成文件路径
  filepath <- file.path(bibtex_dir, filename)
  
  # 转换所有条目为字符串
  bib_str <- paste(sapply(bib_entries, bibtex_to_string), collapse = "\n")
  
  # 保存文件
  writeLines(bib_str, filepath)
  
  return(filepath)
}

# 批量转换文献数据
batch_convert_to_bibtex <- function(work_data_list) {
  # 转换每个文献
  bib_entries <- lapply(work_data_list, convert_to_bibtex)
  
  return(bib_entries)
}

# 从文件加载BibTeX数据
load_bibtex <- function(filename, base_dir = "data") {
  # 构建文件路径
  filepath <- file.path(base_dir, "bibtex", filename)
  
  # 检查文件是否存在
  if (!file.exists(filepath)) {
    stop(sprintf("文件不存在: %s", filepath))
  }
  
  # 读取文件内容
  bib_str <- readLines(filepath)
  
  # 解析BibTeX条目
  # TODO: 实现BibTeX解析功能
  
  return(bib_str)
} 