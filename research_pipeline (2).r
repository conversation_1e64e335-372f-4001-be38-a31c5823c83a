# research_pipeline.R
# 这个脚本将逐步实现研究框架

# --- 1. 数据加载与格式转换 ---

# 1.1 加载必要的库
# 加载 bibliometrix 用于文献计量分析，tidyverse 用于数据处理和文件操作
library(bibliometrix)
library(tidyverse)

# 检查并安装 writexl 包 (用于写入 Excel 文件)
if (!requireNamespace("writexl", quietly = TRUE)) {
  cat("writexl 包未安装，正在尝试安装...\n")
  install.packages("writexl")
}
library(writexl)

# 1.2 设置工作目录和数据路径
working_dir <- "C:/Users/<USER>/Desktop/article/数据处理部分"
setwd(working_dir)
cat("当前工作目录已设置为:", getwd(), "\n")

raw_data_dir <- "C:/Users/<USER>/Desktop/数据文件/citespace数据"

if (!dir.exists(raw_data_dir)) {
  stop("指定的原始数据目录不存在: ", raw_data_dir)
} else {
  cat("找到原始数据目录:", raw_data_dir, "\n")
}

# 1.3 读取并转换 WoS 数据文件
wos_files <- list.files(path = raw_data_dir, pattern = "\\.txt$", full.names = TRUE)

if (length(wos_files) == 0) {
  stop("在指定目录下没有找到 .txt 文件: ", raw_data_dir)
} else {
  cat("找到", length(wos_files), "个 .txt 文件，准备进行转换...\n")
  print(basename(wos_files))
}

cat("\n开始使用 bibliometrix 进行格式转换...\n")
M <- convert2df(file = wos_files, dbsource = "wos", format = "plaintext")
cat("转换完成。 数据框 M 包含", nrow(M), "条记录 和", ncol(M), "个字段。\n")

cat("\n--- 诊断: M 对象 --- \n")
cat("维度 (Dim_M): ", paste(dim(M), collapse = " x "), "\n")
cat("列名 (Colnames_M): ", paste(colnames(M), collapse = ", "), "\n")
cat("前几行 AU (Authors_M_Head):\n"); print(head(M$AU))
cat("前几行 DE (AuthorKeywords_M_Head):\n"); print(head(M$DE))
cat("前几行 ID (KeywordsPlus_M_Head):\n"); print(head(M$ID))
cat("前几行 SO (Sources_M_Head):\n"); print(head(M$SO))
cat("非空 AU 数量 (NonEmpty_AU_M): ", sum(!is.na(M$AU) & M$AU != ""), "\n")
cat("非空 DE 数量 (NonEmpty_DE_M): ", sum(!is.na(M$DE) & M$DE != ""), "\n")
cat("非空 ID 数量 (NonEmpty_ID_M): ", sum(!is.na(M$ID) & M$ID != ""), "\n")
cat("非空 SO 数量 (NonEmpty_SO_M): ", sum(!is.na(M$SO) & M$SO != ""), "\n")
cat("--- 诊断: M 对象结束 --- \n\n")

M_for_excel <- M
all_na_cols <- sapply(M_for_excel, function(x) all(is.na(x)))
if (any(all_na_cols)) {
  cat("检测到以下列完全由 NA 组成，将其转换为字符型以便写入 Excel:",
      paste(names(M_for_excel)[all_na_cols], collapse=", "), "\n")
  for (col_name in names(M_for_excel)[all_na_cols]) {
    M_for_excel[[col_name]] <- as.character(M_for_excel[[col_name]])
  }
}

excel_char_limit <- 32700
truncated_cols <- character(0)
char_cols <- names(M_for_excel)[sapply(M_for_excel, is.character)]
for (col_name in char_cols) {
  char_lengths <- nchar(M_for_excel[[col_name]], keepNA = FALSE)
  if (any(char_lengths > excel_char_limit, na.rm = TRUE)) {
    truncated_cols <- c(truncated_cols, col_name)
    M_for_excel[[col_name]] <- ifelse(
      !is.na(M_for_excel[[col_name]]) & char_lengths > excel_char_limit,
      paste0(substr(M_for_excel[[col_name]], 1, excel_char_limit), "... [截断]"),
      M_for_excel[[col_name]]
    )
  }
}
if (length(truncated_cols) > 0) {
  cat("警告：检测到以下列中存在超长文本 (超过", excel_char_limit, "字符)，已截断以便写入 Excel:",
      paste(unique(truncated_cols), collapse=", "), "\n")
}

output_xlsx_file <- file.path(working_dir, "WoS_converted_data.xlsx")
tryCatch({
  write_xlsx(M_for_excel, path = output_xlsx_file)
  cat("已将转换后的数据框同时保存为 Excel 文件到:", output_xlsx_file, "\n")
}, error = function(e) {
  cat("错误：无法将数据框保存为 Excel 文件。错误信息:", conditionMessage(e), "\n")
})

output_rdata_file <- file.path(working_dir, "WoS_converted_data.RData")
save(M, file = output_rdata_file)
cat("已将转换后的数据框保存到:", output_rdata_file, "\n")
cat("\n--- 第一步：数据加载与格式转换完成 ---\n")

# --- 2. 数据探索与初步缺失分析 ---
load("WoS_converted_data.RData")
cat("\n已加载数据框 M。\n")

cat("\n--- 数据框 M 基本信息 ---\n")
cat("维度 (记录数 x 字段数):", dim(M)[1], "x", dim(M)[2], "\n")

field_mapping <- data.frame(
  Tag = c("DT", "AU", "AF", "TI", "SO", "LA", "DE", "ID", "AB", "C1", "RP", "CR", "TC",
          "PY", "SC", "UT", "DI", "WC", "J9", "JI", "PU", "PI", "PA", "SN", "BN",
          "FU", "NR", "VL", "IS", "BP", "EP", "PG", "DA", "EM", "OI", "RI", "PM",
          "OA", "HC", "HP", "Z9", "U1", "U2", "U3", "U4", "U5", "U6", "D2", "EA",
          "EY", "DB", "AU_CO", "AU_UN", "AU1_CO", "AU1_UN", "SR", "LCS", "GCS", "MCP",
          "SCP", "TI_TM", "AB_TM", "DE_TM", "ID_TM", "CO_CA", "N_GRANT",
          "AR", "BA", "BE", "BF", "C3", "CA", "CL", "CT", "CY",
          "EF", "EI", "ER", "FX", "GA", "HO", "PD", "PN", "PT", "SE", "SI", "SP", "SU",
          "SR_FULL", "WE", "C1raw", "AU_UN_NR"
          ),
  Meaning = c("文档类型", "作者", "作者全名", "标题", "出版物来源", "语言", "作者关键词",
              "关键词Plus", "摘要", "作者地址", "通讯作者地址", "引用参考文献", "被引频次",
              "出版年份", "WoS学科类别", "唯一标识符(WoS)", "DOI", "Web of Science类别",
              "期刊缩写(J9)", "ISO期刊缩写(JI)", "出版商", "出版商城市", "出版商地址",
              "ISSN", "ISBN", "资助机构与编号", "参考文献数量", "卷号", "期号", "起始页码",
              "结束页码", "页数", "出版日期(数据库记录)", "电子邮件地址", "ORCID标识符", "ResearcherID",
              "PubMed ID", "开放获取状态", "高被引论文", "热点论文", "总引用次数(WoS)",
              "过去180天的使用计数", "自2013年以来的使用计数", "用户定义字段3", "用户定义字段4",
              "用户定义字段5", "用户定义字段6", "电子出版日期", "提前访问日期",
              "提前访问年份", "数据来源", "作者国家", "作者机构", "第一作者国家",
              "第一作者机构", "简短引用格式", "局部引用得分", "全局引用得分", "多国出版物",
              "单国出版物", "标题术语矩阵", "摘要术语矩阵", "关键词术语矩阵",
              "KeywordsPlus术语矩阵", "通讯作者国家", "资助项目数",
              "文章编号", "图书作者", "编者", "图书作者全名", "会议标题(C3)",
              "会议赞助者(CA)", "会议地点", "会议标题(CT)", "会议日期",
              "文件结束符", "电子ISSN", "记录结束符", "资助文本", "团体作者", "会议主办方",
              "出版日期(月/日)", "部分号", "出版物类型", "丛书标题", "特刊/增刊标识", "会议赞助者(SP)",
              "增刊", "完整引文格式", "WoS 版本", "原始作者地址", "作者机构数量"
              )
)
actual_colnames <- colnames(M)
colnames_df <- data.frame(Tag = actual_colnames) %>%
  left_join(field_mapping, by = "Tag") %>%
  mutate(Meaning = ifelse(is.na(Meaning) & Tag %in% actual_colnames,
                        paste0("-- (", Tag, ") 未在预定义映射中 --"),
                        Meaning))
cat("\n--- 数据框 M 字段列表 (Tag 与 中文含义) ---\n")
print(colnames_df, row.names = FALSE, right = FALSE)

if (!requireNamespace("naniar", quietly = TRUE)) {
  cat("naniar 包未安装，正在尝试安装...\n")
  install.packages("naniar")
}
library(naniar)

cat("\n--- 字段缺失值分析 (按缺失比例降序排列) ---\n")
missing_summary <- miss_var_summary(M) %>%
  arrange(desc(pct_miss)) %>%
  left_join(select(colnames_df, Tag, Meaning), by = c("variable" = "Tag")) %>%
  select(Tag = variable, Meaning, n_miss, pct_miss)
cat("\n确认：计算得到的 missing_summary 对象包含", nrow(missing_summary), "行 (字段)。\n\n")
print(missing_summary, row.names = FALSE, right = FALSE, n = Inf)
cat("\n--- 第二步：数据探索与初步缺失分析完成 ---\n")

# --- 3. 描述性统计分析 ---
cat("\n--- 开始执行基本的文献计量分析 (biblioAnalysis) ---\n")
results <- biblioAnalysis(M)
cat("基本的文献计量分析计算完成。\n")

cat("\n--- 诊断: results 对象 --- \n")
cat("results 对象结构 (Structure_results):\n"); str(results, max.level = 1) # Max level to avoid too much output
if ("Authors" %in% names(results)) {
    cat("results$Authors 前几行 (Authors_results_Head):\n"); print(head(results$Authors))
} else { cat("results$Authors 不存在\n") }
if ("Sources" %in% names(results)) {
    cat("results$Sources 前几行 (Sources_results_Head):\n"); print(head(results$Sources))
} else { cat("results$Sources 不存在\n") }
cat("--- 诊断: results 对象结束 --- \n\n")

cat("\n--- 文献计量分析结果摘要 (Top 10) ---\n")
options(width = 150)
summary_results_obj <- summary(results, k = 10) # summary() auto-prints
options(width = 80)

cat("\n--- 诊断: summary_results_obj 对象 --- \n")
cat("summary_results_obj 对象结构 (Structure_summary_results_obj):\n"); str(summary_results_obj, max.level = 1)
if (!is.null(summary_results_obj$MostProdAuthors)) {
    cat("summary_results_obj$MostProdAuthors 内容:\n"); print(summary_results_obj$MostProdAuthors)
} else { cat("summary_results_obj$MostProdAuthors 为 NULL\n") }
if (!is.null(summary_results_obj$MostRelKeywords)) {
    cat("summary_results_obj$MostRelKeywords (DE & ID) 内容:\n"); print(summary_results_obj$MostRelKeywords)
} else { cat("summary_results_obj$MostRelKeywords 为 NULL\n") }
if (!is.null(summary_results_obj$MostRelSources)) {
    cat("summary_results_obj$MostRelSources 内容:\n"); print(summary_results_obj$MostRelSources)
} else { cat("summary_results_obj$MostRelSources 为 NULL\n") }
cat("--- 诊断: summary_results_obj 对象结束 --- \n\n")

output_basedir <- file.path(working_dir, "baseline_analysis_output")
if (!dir.exists(output_basedir)) {
  dir.create(output_basedir, recursive = TRUE)
  cat("创建输出目录:", output_basedir, "\n")
}

if (!is.null(summary_results_obj$MainInformationDF)) {
  write.csv(summary_results_obj$MainInformationDF,
            file.path(output_basedir, "desc_main_info_from_summary.csv"), row.names = TRUE)
} else { cat("警告: summary_results_obj$MainInformationDF 未找到或为空\n") }

if (!is.null(summary_results_obj$MostProdAuthors) && nrow(summary_results_obj$MostProdAuthors) > 0) {
  top_authors_df <- summary_results_obj$MostProdAuthors[, 1:2] # Authors, Articles
  write.csv(top_authors_df, file.path(output_basedir, "desc_top_authors.csv"), row.names = FALSE)
  if (ncol(summary_results_obj$MostProdAuthors) >= 4) { # Check for fractionalized part
    top_authors_frac_df <- summary_results_obj$MostProdAuthors[, 3:4] # Authors_Frac, Articles_Frac
    write.csv(top_authors_frac_df, file.path(output_basedir, "desc_top_authors_frac.csv"), row.names = FALSE)
  }
} else { cat("警告: summary_results_obj$MostProdAuthors 为空或不存在\n") }

if (!is.null(summary_results_obj$MostRelKeywords) && nrow(summary_results_obj$MostRelKeywords) > 0) {
  if (ncol(summary_results_obj$MostRelKeywords) >= 2) { # DE part
    top_de_df <- summary_results_obj$MostRelKeywords[, 1:2]
    write.csv(top_de_df, file.path(output_basedir, "desc_top_author_keywords.csv"), row.names = FALSE)
  }
  if (ncol(summary_results_obj$MostRelKeywords) >= 4) { # ID part
    top_id_df <- summary_results_obj$MostRelKeywords[, 3:4]
    write.csv(top_id_df, file.path(output_basedir, "desc_top_keywords_plus.csv"), row.names = FALSE)
  }
} else { cat("警告: summary_results_obj$MostRelKeywords 为空或不存在\n") }

if (!is.null(summary_results_obj$MostRelSources) && nrow(summary_results_obj$MostRelSources) > 0) {
  write.csv(summary_results_obj$MostRelSources, file.path(output_basedir, "desc_top_sources.csv"), row.names = FALSE)
} else { cat("警告: summary_results_obj$MostRelSources 为空或不存在\n") }
cat("\n--- 描述性统计表格保存尝试完成 ---\n")

# --- 4. 知识结构分析 ---

# --- 4.1 概念结构 (Co-occurrence, Co-word analysis) ---

# --- 4.1.1 作者关键词共现网络 (DE) ---
cat("\n--- 开始计算作者关键词 (DE) 共现网络 ---\n")
NetMatrix_DE_DTM <- NULL # Explicitly for DTM
if (!is.null(M$DE) && sum(!is.na(M$DE) & M$DE != "") > 0) {
  M_DE_processed <- as.character(M$DE); M_DE_processed[is.na(M_DE_processed)] <- ""; M_DE_processed <- trimws(M_DE_processed)
  cat("Unique DE terms (first 20):\n"); print(head(unique(trimws(unlist(strsplit(M_DE_processed, ";")))), 20))
  M_temp_DE <- M; M_temp_DE$DE <- M_DE_processed
  
  tryCatch({
    cat("Attempting direct biblioNetwork for DE (co-occurrence)...\n")
    NetMatrix_DE_Cooc <- biblioNetwork(M_temp_DE, analysis = "co-occurrence", network = "author_keywords", sep = ";")
    cat("biblioNetwork for DE (co-occurrence) completed. Saving...\n")
    saveRDS(NetMatrix_DE_Cooc, file.path(output_basedir, "net_cooccurrence_author_keywords_COOC.rds"))
  }, error = function(e_bn) {
    cat("ERROR during direct biblioNetwork for DE (co-occurrence): ", conditionMessage(e_bn), "\n")
    cat("Now attempting cocMatrix for DE (DTM) as an alternative...\n")
    tryCatch({
      NetMatrix_DE_DTM <<- cocMatrix(M_temp_DE, Field = "DE", sep = ";") # Assign to global in this catch
      cat("cocMatrix for DE (DTM) completed. Dimensions:", paste(dim(NetMatrix_DE_DTM), collapse=" x "), "\n")
      saveRDS(NetMatrix_DE_DTM, file.path(output_basedir, "net_cooccurrence_author_keywords_DTM.rds"))
      cat("作者关键词的文档-词语矩阵 (DTM) 已保存.\n")
    }, error = function(e_coc) {
      cat("ERROR during cocMatrix for DE: ", conditionMessage(e_coc), "\n")
    })
  })
} else { cat("警告: M$DE (作者关键词) 为空，跳过作者关键词共现网络分析。\n") }

# --- 4.1.2 Keywords Plus 共现网络 (ID) ---
cat("\n--- 开始计算 Keywords Plus (ID) 共现网络 ---\n")
NetMatrix_ID_DTM <- NULL # Explicitly for DTM
if (!is.null(M$ID) && sum(!is.na(M$ID) & M$ID != "") > 0) {
  M_ID_processed <- as.character(M$ID); M_ID_processed[is.na(M_ID_processed)] <- ""; M_ID_processed <- trimws(M_ID_processed)
  cat("Unique ID terms (first 20):\n"); print(head(unique(trimws(unlist(strsplit(M_ID_processed, ";")))), 20))
  M_temp_ID <- M; M_temp_ID$ID <- M_ID_processed

  tryCatch({
    cat("Attempting direct biblioNetwork for ID (co-occurrence)...\n")
    NetMatrix_ID_Cooc <- biblioNetwork(M_temp_ID, analysis = "co-occurrence", network = "keywords", sep = ";") # 'keywords' is often alias for ID
    cat("biblioNetwork for ID (co-occurrence) completed. Saving...\n")
    saveRDS(NetMatrix_ID_Cooc, file.path(output_basedir, "net_cooccurrence_keywords_plus_COOC.rds"))
  }, error = function(e_bn_id) {
    cat("ERROR during direct biblioNetwork for ID (co-occurrence): ", conditionMessage(e_bn_id), "\n")
    cat("Now attempting cocMatrix for ID (DTM) as an alternative...\n")
    tryCatch({
      NetMatrix_ID_DTM <<- cocMatrix(M_temp_ID, Field = "ID", sep = ";") # Assign to global
      cat("cocMatrix for ID (DTM) completed. Dimensions:", paste(dim(NetMatrix_ID_DTM), collapse=" x "), "\n")
      saveRDS(NetMatrix_ID_DTM, file.path(output_basedir, "net_cooccurrence_keywords_plus_DTM.rds"))
      cat("Keywords Plus 的文档-词语矩阵 (DTM) 已保存.\n")
    }, error = function(e_coc_id) {
      cat("ERROR during cocMatrix for ID: ", conditionMessage(e_coc_id), "\n")
    })
  })
} else { cat("警告: M$ID (Keywords Plus) 为空，跳过 Keywords Plus 共现网络分析。\n") }


# --- 4.1.3 主题图 (Thematic Map) ---
cat("\n--- 开始生成主题图 (Thematic Map) ---\n")

# 优先使用直接生成的共现网络，其次使用DTM转换的共现网络
Cooc_Matrix_for_ThematicMap <- NULL
field_for_tm_source_name <- ""
original_field_for_tm <- "" # e.g. "ID" or "DE" for M_clean

if (exists("NetMatrix_ID_Cooc") && !is.null(NetMatrix_ID_Cooc) && (is.matrix(NetMatrix_ID_Cooc) || inherits(NetMatrix_ID_Cooc, "Matrix")) && ncol(NetMatrix_ID_Cooc) >= 10) {
    Cooc_Matrix_for_ThematicMap <- NetMatrix_ID_Cooc
    field_for_tm_source_name <- "ID_directCOOC"
    original_field_for_tm <- "ID"
    cat("将使用直接从 biblioNetwork 生成的 Keywords Plus 共现矩阵 (NetMatrix_ID_Cooc) 进行主题图。\n")
} else if (exists("NetMatrix_DE_Cooc") && !is.null(NetMatrix_DE_Cooc) && (is.matrix(NetMatrix_DE_Cooc) || inherits(NetMatrix_DE_Cooc, "Matrix")) && ncol(NetMatrix_DE_Cooc) >= 10) {
    Cooc_Matrix_for_ThematicMap <- NetMatrix_DE_Cooc
    field_for_tm_source_name <- "DE_directCOOC"
    original_field_for_tm <- "DE"
    cat("将使用直接从 biblioNetwork 生成的 Author Keywords 共现矩阵 (NetMatrix_DE_Cooc) 进行主题图。\n")
} else {
    cat("直接生成的共现网络不可用或太小。尝试从 DTMs 创建词语-词语共现矩阵...\n")
    Temp_Cooc_ID <- NULL
    if (exists("NetMatrix_ID_DTM") && !is.null(NetMatrix_ID_DTM) && ncol(NetMatrix_ID_DTM) > 0) {
        tryCatch({
            Temp_Cooc_ID <- crossprod(NetMatrix_ID_DTM); diag(Temp_Cooc_ID) <- 0
            cat("从 Keywords Plus DTM 创建的词语-词语共现矩阵维度:", dim(Temp_Cooc_ID), "\n")
        }, error = function(e) {cat("从 NetMatrix_ID_DTM 创建共现矩阵失败:", conditionMessage(e),"\n")})
    }
    Temp_Cooc_DE <- NULL
    if (exists("NetMatrix_DE_DTM") && !is.null(NetMatrix_DE_DTM) && ncol(NetMatrix_DE_DTM) > 0) {
        tryCatch({
            Temp_Cooc_DE <- crossprod(NetMatrix_DE_DTM); diag(Temp_Cooc_DE) <- 0
            cat("从 Author Keywords DTM 创建的词语-词语共现矩阵维度:", dim(Temp_Cooc_DE), "\n")
        }, error = function(e) {cat("从 NetMatrix_DE_DTM 创建共现矩阵失败:", conditionMessage(e),"\n")})
    }

    if (!is.null(Temp_Cooc_ID) && ncol(Temp_Cooc_ID) >= 10) {
        Cooc_Matrix_for_ThematicMap <- Temp_Cooc_ID
        field_for_tm_source_name <- "ID_fromDTM"
        original_field_for_tm <- "ID"
        cat("将使用从 Keywords Plus DTM 转换的共现矩阵进行主题图。\n")
    } else if (!is.null(Temp_Cooc_DE) && ncol(Temp_Cooc_DE) >= 10) {
        Cooc_Matrix_for_ThematicMap <- Temp_Cooc_DE
        field_for_tm_source_name <- "DE_fromDTM"
        original_field_for_tm <- "DE"
        cat("将使用从 Author Keywords DTM 转换的共现矩阵进行主题图。\n")
    }
}

if (!is.null(Cooc_Matrix_for_ThematicMap) && !is.null(M) && original_field_for_tm != "") {
    # 准备 M_clean (确保它在此作用域内定义)
    M_clean <- M[, c("DI", "PY", "SO", "AU", "TI", original_field_for_tm), drop = FALSE]
    colnames(M_clean)[colnames(M_clean) == original_field_for_tm] <- "Keywords_for_ThematicMap" # Generic name
    M_clean$AU_CO <- if (!is.null(M$AU_CO)) M$AU_CO else "UNKNOWN" # thematicMap might need this

    # 确保 Keywords_for_ThematicMap 列存在于 M_clean 中
    if (!"Keywords_for_ThematicMap" %in% names(M_clean)) {
        stop("Critical error: 'Keywords_for_ThematicMap' column not found in M_clean.")
    }

    thematic_map_rds_path <- file.path(output_basedir, paste0("thematic_map_", field_for_tm_source_name, ".rds"))
    plot_thematic_map_path <- file.path(output_basedir, paste0("plot_thematic_map_", field_for_tm_source_name, ".png"))

    tryCatch({
        cat("开始尝试执行主题图分析 (使用 ", field_for_tm_source_name, " 网络)...\n")
        cat("共现矩阵维度:", dim(Cooc_Matrix_for_ThematicMap), "\n")
        cat("M_clean 中用于词频的字段名: Keywords_for_ThematicMap (源自: ", original_field_for_tm, ")\n")
        
        Map_obj <- thematicMap(
            net = Cooc_Matrix_for_ThematicMap,
            M = M_clean, 
            field = "Keywords_for_ThematicMap", # thematicMap uses this field from M to get term frequencies for minfreq
            n = 3,           # 减少每个主题显示的关键词数量
            minfreq = 10,    # 调整关键词最小频率 (这是在M_clean$Keywords_for_ThematicMap中的频率)
            size = 0.6,
            repel = TRUE,
            n.labels = 2,    # 减少标签数量
            cluster = "walktrap"
        )
        
        if (!is.null(Map_obj) && !is.null(Map_obj$map)) {
            ggsave(plot_thematic_map_path, plot = Map_obj$map, width = 12, height = 10, dpi = 300)
            cat("主题图已保存为图像到:", plot_thematic_map_path, "\n")
            saveRDS(Map_obj, file = thematic_map_rds_path)
            cat("主题图对象已保存到:", thematic_map_rds_path, "\n")
        } else {
            cat("警告: thematicMap (使用 ", field_for_tm_source_name, " 网络) 未能成功生成绘图对象。\n")
            stop("ThematicMap failed to produce output, triggering fallback.") # Force fallback
        }
    }, error = function(e_tm) {
        cat("错误: 生成主题图 (使用 ", field_for_tm_source_name, " 网络) 时发生错误: ", conditionMessage(e_tm), "\n")
        cat("错误详情:\n"); print(e_tm)
        
        cat("\n主题图失败，尝试 conceptualStructure 作为替代...\n")
        tryCatch({
            field_for_cs_alt <- if (sum(!is.na(M[[original_field_for_tm]]) & M[[original_field_for_tm]] != "") > 30) original_field_for_tm else "TI"
            cat("使用字段 '", field_for_cs_alt, "' 执行 conceptualStructure (CA)...\n")
            M_minimal_cs <- M[1:min(2000, nrow(M)), ] # Use a subset if M is very large

            CS_alt <- conceptualStructure(
                M_minimal_cs,
                field = field_for_cs_alt,
                method = "CA", # CA is often more robust than MCA for this
                minDegree = ifelse(field_for_cs_alt == "TI", 3, 5), # Adjust minDegree based on field
                clust = 4, # Reduce clusters
                stemming = FALSE,
                labelsize = 8,
                documents = 30
            )
            
            if (exists("CS_alt") && !is.null(CS_alt) && inherits(CS_alt[[1]], "ggplot")) { # Check if it's a list of ggplots
                for (i in seq_along(CS_alt)) {
                    if (inherits(CS_alt[[i]], "ggplot")) {
                        cs_plot_path <- file.path(output_basedir, paste0("conceptual_structure_alt_plot_", field_for_cs_alt, "_", i, ".png"))
                        ggsave(cs_plot_path, plot = CS_alt[[i]], width = 10, height = 8, dpi = 300)
                        cat("概念结构替代图 ", i, " 已保存到: ", cs_plot_path, "\n")
                    }
                }
                saveRDS(CS_alt, file.path(output_basedir, paste0("conceptual_structure_alt_", field_for_cs_alt, ".rds")))
                cat("完整概念结构替代对象已保存.\n")
            } else {
                cat("警告: conceptualStructure (替代方案) 返回的对象结构不是预期的ggplot列表或为空。\n")
                stop("ConceptualStructure (alternative) also failed.") # Force next fallback
            }
        }, error = function(e_cs_alt) {
            cat("错误: 执行 conceptualStructure (替代方案) 时发生错误: ", conditionMessage(e_cs_alt), "\n")
            
            cat("\nConceptualStructure 失败，尝试基本词频分析作为最终备选...\n")
            tryCatch({
                analyze_term_freq <- function(column_data, field_name_str, min_f = 5) {
                    if (is.null(column_data) || all(is.na(column_data) | column_data == "")) {
                        cat(paste0(field_name_str, " 数据为空或全为NA/空，无法分析词频\n")); return(NULL)
                    }
                    all_terms <- trimws(unlist(strsplit(as.character(column_data), ";")))
                    all_terms <- all_terms[all_terms != ""] # Remove empty strings from splitting
                    if(length(all_terms) == 0) {
                         cat(paste0(field_name_str, " 没有有效词项进行词频分析\n")); return(NULL)
                    }
                    term_freq <- sort(table(all_terms), decreasing = TRUE)
                    term_freq_df <- as.data.frame(term_freq)
                    names(term_freq_df) <- c("Term", "Frequency")
                    term_freq_df <- term_freq_df[term_freq_df$Frequency >= min_f, ]
                    
                    if(nrow(term_freq_df) > 0){
                        tf_path <- file.path(output_basedir, paste0("term_freq_", gsub("[^A-Za-z0-9]", "_", field_name_str), ".csv"))
                        write.csv(term_freq_df, tf_path, row.names = FALSE)
                        cat(paste0(field_name_str, " 词频已保存到: ", tf_path, "\n"))
                        
                        top_n_plot <- min(30, nrow(term_freq_df))
                        p <- ggplot(term_freq_df[1:top_n_plot, ], aes(x = reorder(Term, Frequency), y = Frequency)) +
                            geom_bar(stat = "identity", fill = "skyblue") + coord_flip() + theme_minimal() +
                            labs(title = paste0(field_name_str, " 词频 (Top ", top_n_plot, ", Freq >= ", min_f, ")"), x = "Term", y = "Frequency")
                        plot_tf_path <- file.path(output_basedir, paste0("plot_term_freq_", gsub("[^A-Za-z0-9]", "_", field_name_str), ".png"))
                        ggsave(plot_tf_path, plot = p, width = 10, height = 8, dpi = 300)
                        cat(paste0(field_name_str, " 词频图已保存到: ", plot_tf_path, "\n"))
                    } else {
                        cat(paste0(field_name_str, " 没有词项满足最小频率 ", min_f, "\n"))
                    }
                    return(term_freq_df)
                }
                analyze_term_freq(M$ID, "Keywords_Plus_ID", min_f = 10)
                analyze_term_freq(M$DE, "Author_Keywords_DE", min_f = 10)
            }, error = function(e_freq_alt) {
                cat("错误: 执行基本词频分析 (最终备选) 时发生错误: ", conditionMessage(e_freq_alt), "\n")
            })
        })
    })
} else {
  cat("警告: 未能准备好用于主题图的共现网络或M数据，跳过主题图及其主要后备分析。\n")
  cat("将直接尝试独立的词频分析...\n")
   tryCatch({ # Independent term frequency if TM pipeline fails early
        analyze_term_freq <- function(column_data, field_name_str, min_f = 5) { # Duplicated for independence
            if (is.null(column_data) || all(is.na(column_data) | column_data == "")) {
                cat(paste0(field_name_str, " 数据为空或全为NA/空，无法分析词频\n")); return(NULL)
            }
            all_terms <- trimws(unlist(strsplit(as.character(column_data), ";")))
            all_terms <- all_terms[all_terms != ""] 
            if(length(all_terms) == 0) {
                 cat(paste0(field_name_str, " 没有有效词项进行词频分析\n")); return(NULL)
            }
            term_freq <- sort(table(all_terms), decreasing = TRUE)
            term_freq_df <- as.data.frame(term_freq)
            names(term_freq_df) <- c("Term", "Frequency")
            term_freq_df <- term_freq_df[term_freq_df$Frequency >= min_f, ]
            
            if(nrow(term_freq_df) > 0){
                tf_path <- file.path(output_basedir, paste0("term_freq_indep_", gsub("[^A-Za-z0-9]", "_", field_name_str), ".csv"))
                write.csv(term_freq_df, tf_path, row.names = FALSE)
                cat(paste0(field_name_str, " (独立)词频已保存到: ", tf_path, "\n"))
            } else {
                cat(paste0(field_name_str, " (独立)没有词项满足最小频率 ", min_f, "\n"))
            }
            return(term_freq_df)
        }
        analyze_term_freq(M$ID, "Keywords_Plus_ID_Indep", min_f = 10)
        analyze_term_freq(M$DE, "Author_Keywords_DE_Indep", min_f = 10)
    }, error = function(e_freq_indep) {
        cat("错误: 执行独立词频分析时发生错误: ", conditionMessage(e_freq_indep), "\n")
    })
}


# --- 4.1.4 概念结构 - 多重对应分析 (MCA) - 基于 Keywords Plus (ID) ---
cat("\n--- 开始进行概念结构分析 (MCA - ID) ---\n")
if (!is.null(M$ID) && sum(!is.na(M$ID) & M$ID != "") > 10) {
  mca_id_rds_path <- file.path(output_basedir, "conceptual_structure_mca_id.rds")
  tryCatch({
      CS_ID_MCA <- conceptualStructure(M, field="ID", method="MCA",
                                   minDegree = 5, # Min frequency of keyword to be included
                                   clust = 5,     # Number of clusters for the plot
                                   stemming=FALSE,
                                   labelsize = 8,
                                   documents = 20 # Number of documents to plot
                                  )
      if (exists("CS_ID_MCA") && !is.null(CS_ID_MCA) && inherits(CS_ID_MCA[[1]], "ggplot")) {
          for (i in seq_along(CS_ID_MCA)) {
              if (inherits(CS_ID_MCA[[i]], "ggplot")) {
                  cs_mca_plot_path <- file.path(output_basedir, paste0("conceptual_structure_mca_id_plot_", i, ".png"))
                  ggsave(cs_mca_plot_path, plot = CS_ID_MCA[[i]], width = 10, height = 8, dpi = 300)
                  cat("概念结构 MCA (ID) 图 ", i, " 已保存到: ", cs_mca_plot_path, "\n")
              }
          }
          saveRDS(CS_ID_MCA, file = mca_id_rds_path)
          cat("概念结构 (MCA - ID) 对象已保存到:", mca_id_rds_path, "\n")
      } else {
          cat("警告: 概念结构 (MCA - ID) 未能生成预期的绘图对象列表。\n")
      }
  }, error = function(e) {
      cat("错误: 进行概念结构分析 (MCA - ID) 时发生错误: ", conditionMessage(e), "\n")
  })
} else {
  cat("警告: M$ID (Keywords Plus) 数据不足以进行概念结构 (MCA) 分析。\n")
}

# --- 4.2 智力结构 (Co-citation, Bibliographic coupling) ---
# (保持这部分代码与您原来的一致，因为错误日志未显示此处问题)
# 4.2.1 文献共被引网络 (References co-citation)
cat("\n--- 开始计算文献共被引网络 ---\n")
if (!is.null(M$CR) && sum(!is.na(M$CR) & M$CR != "") > 0) {
  tryCatch({
    NetMatrix_CR <- biblioNetwork(M, analysis = "co-citation", network = "references", sep = ";")
    if (exists("NetMatrix_CR") && !is.null(NetMatrix_CR) && ncol(NetMatrix_CR) > 0) {
      saveRDS(NetMatrix_CR, file = file.path(output_basedir, "net_cocitation_references.rds"))
      cat("文献共被引网络对象已保存到 net_cocitation_references.rds\n")
    } else { cat("警告: 未能成功生成文献共被引网络矩阵 (NetMatrix_CR)，或矩阵为空。\n") }
  }, error = function(e){cat("错误: 计算文献共被引网络: ", conditionMessage(e), "\n")})
} else { cat("警告: M$CR 数据不足，跳过文献共被引网络分析。\n") }

# 4.2.2 作者文献耦合网络
cat("\n--- 开始计算作者文献耦合网络 ---\n")
if (!is.null(M$AU) && sum(!is.na(M$AU) & M$AU != "") > 0 && !is.null(M$CR) && sum(!is.na(M$CR) & M$CR != "") > 0) {
  tryCatch({
    NetMatrix_Coupling_AU <- biblioNetwork(M, analysis = "coupling", network = "authors", sep = ";")
    if (exists("NetMatrix_Coupling_AU") && !is.null(NetMatrix_Coupling_AU) && ncol(NetMatrix_Coupling_AU) > 0) {
      saveRDS(NetMatrix_Coupling_AU, file.path(output_basedir, "net_coupling_authors.rds"))
      cat("作者文献耦合网络矩阵已保存为 RDS.\n")
    } else { cat("警告: 未能成功生成作者文献耦合网络矩阵 (NetMatrix_Coupling_AU)，或矩阵为空。\n") }
  }, error = function(e){cat("错误: 计算作者文献耦合网络: ", conditionMessage(e), "\n")})
} else { cat("警告: M$AU 或 M$CR 数据不足，跳过作者文献耦合分析。\n") }

# 4.2.3 期刊文献耦合网络
cat("\n--- 开始计算期刊文献耦合网络 ---\n")
if (!is.null(M$SO) && sum(!is.na(M$SO) & M$SO != "") > 0 && !is.null(M$CR) && sum(!is.na(M$CR) & M$CR != "") > 0) {
  tryCatch({
    NetMatrix_Coupling_SO <- biblioNetwork(M, analysis = "coupling", network = "sources", sep = ";")
    if (exists("NetMatrix_Coupling_SO") && !is.null(NetMatrix_Coupling_SO) && ncol(NetMatrix_Coupling_SO) > 0) {
      saveRDS(NetMatrix_Coupling_SO, file.path(output_basedir, "net_coupling_sources.rds"))
      cat("期刊文献耦合网络矩阵已保存为 RDS.\n")
    } else { cat("警告: 未能成功生成期刊文献耦合网络矩阵 (NetMatrix_Coupling_SO)，或矩阵为空。\n") }
  }, error = function(e){cat("错误: 计算期刊文献耦合网络: ", conditionMessage(e), "\n")})
} else { cat("警告: M$SO 或 M$CR 数据不足，跳过期刊文献耦合分析。\n") }

# --- 4.3 社会结构 (Collaboration analysis) ---
# (保持这部分代码与您原来的一致)
# 4.3.1 作者合作网络
cat("\n--- 开始计算作者合作网络 ---\n")
if (!is.null(M$AU) && sum(!is.na(M$AU) & M$AU != "") > 0) {
  tryCatch({
    NetMatrix_Collab_AU <- biblioNetwork(M, analysis = "collaboration", network = "authors", sep = ";")
    if (exists("NetMatrix_Collab_AU") && !is.null(NetMatrix_Collab_AU) && ncol(NetMatrix_Collab_AU) > 0) {
      saveRDS(NetMatrix_Collab_AU, file.path(output_basedir, "net_author_collaboration.rds"))
      cat("作者合作网络矩阵已保存为 RDS.\n")
    } else { cat("警告: 未能成功生成作者合作网络矩阵 (NetMatrix_Collab_AU)，或矩阵为空。\n") }
  }, error = function(e){cat("错误: 计算作者合作网络: ", conditionMessage(e), "\n")})
} else { cat("警告: M$AU 数据不足，跳过作者合作网络分析。\n") }

# 4.3.2 机构合作网络
cat("\n--- 开始计算机构合作网络 ---\n")
if (!is.null(M$C1) && sum(!is.na(M$C1) & M$C1 != "") > 0) {
  tryCatch({
    NetMatrix_Collab_Inst <- biblioNetwork(M, analysis = "collaboration", network = "institutions", sep = ";")
    if (exists("NetMatrix_Collab_Inst") && !is.null(NetMatrix_Collab_Inst) && ncol(NetMatrix_Collab_Inst) > 0) {
      saveRDS(NetMatrix_Collab_Inst, file.path(output_basedir, "net_institution_collaboration.rds"))
      cat("机构合作网络矩阵已保存为 RDS.\n")
    } else { cat("警告: 未能成功生成机构合作网络矩阵 (NetMatrix_Collab_Inst)，或矩阵为空。\n") }
  }, error = function(e){cat("错误: 计算机构合作网络: ", conditionMessage(e), "\n")})
} else { cat("警告: M$C1 数据不足，跳过机构合作网络分析。\n") }

# 4.3.3 国家合作网络
cat("\n--- 开始计算国家合作网络 ---\n")
NetMatrix_Collab_Country_final <- NULL
if (!is.null(results$CountryCollaboration) && !is.null(results$CountryCollaboration$NetMatrix) && nrow(results$CountryCollaboration$NetMatrix) > 0) {
  NetMatrix_Collab_Country_final <- results$CountryCollaboration$NetMatrix
  cat("使用 results$CountryCollaboration 中的国家合作网络矩阵。\n")
} else if (!is.null(M$AU_CO) && sum(!is.na(M$AU_CO) & M$AU_CO != "") > 0) {
  cat("尝试从 M$AU_CO 生成国家合作网络...\n")
  tryCatch({
    NetMatrix_Collab_Country_from_M <- biblioNetwork(M, analysis = "collaboration", network = "countries", sep = ";", field = "AU_CO")
    if (exists("NetMatrix_Collab_Country_from_M") && !is.null(NetMatrix_Collab_Country_from_M) && ncol(NetMatrix_Collab_Country_from_M) > 0) {
       NetMatrix_Collab_Country_final <- NetMatrix_Collab_Country_from_M
       cat("从 M$AU_CO 生成国家合作网络成功。\n")
    } else { cat("警告: 从 M$AU_CO 生成国家合作网络失败或矩阵为空。\n") }
  }, error = function(e) { cat("错误: 从 M$AU_CO 生成国家合作网络: ", conditionMessage(e), "\n") })
}
if(!is.null(NetMatrix_Collab_Country_final)){
    saveRDS(NetMatrix_Collab_Country_final, file.path(output_basedir, "net_country_collaboration.rds"))
    cat("国家合作网络矩阵已保存为 RDS.\n")
} else { cat("警告: 未能获取或生成国家合作网络矩阵。\n") }


# --- 5. 历史直接引文分析 (Historiograph) ---
cat("\n--- 开始进行历史直接引文分析 (Historiograph) ---\n")
if (!is.null(M$CR) && sum(!is.na(M$CR) & M$CR != "") > 0 &&
    !is.null(M$PY) && sum(!is.na(M$PY)) > 0 &&
    (!is.null(M$UT) || !is.null(M$DI)) &&
    exists("results") && !is.null(results) ) { # Ensure results object exists
  hist_network_rds_path <- file.path(output_basedir, "historiograph_network.rds")
  tryCatch({
      histResults <- histNetwork(results, n = 20, sep = ";")
      if (!is.null(histResults) && !is.null(histResults$NetMatrix) && nrow(histResults$NetMatrix) > 0) {
        saveRDS(histResults, file = hist_network_rds_path)
        cat("历史直接引文网络对象 (histResults) 已保存到:", hist_network_rds_path, "\n")
      } else { cat("警告: histNetwork 未能成功生成有效的历史网络数据。\n") }
  }, error = function(e) { cat("错误: 进行历史直接引文分析: ", conditionMessage(e), "\n") })
} else { cat("警告: M 中 CR, PY, UT/DI 字段数据不足，或 results 对象不存在，跳过历史直接引文分析。\n") }

cat("\n--- 研究流程脚本执行完毕 ---\n")

# --- 关于您脚本末尾的 M_filtered 代码 ---
# 您原来的代码:
# de_freq <- table(unlist(strsplit(M$DE, ";")))
# de_top <- names(de_freq[de_freq > 5])
# M_filtered <- M[M$DE %in% paste(de_top, collapse="|"), ] # 这行逻辑不正确

# 如果您的目的是筛选出 M 数据框中，其 DE 字段包含至少一个 de_top 中关键词的记录，
# 正确的实现方式之一如下 (取消注释以使用):
#
# if (exists("M") && !is.null(M$DE)) {
#   de_values <- M$DE[!is.na(M$DE) & M$DE != ""]
#   if (length(de_values) > 0) {
#     de_freq_table <- table(trimws(unlist(strsplit(de_values, ";"))))
#     de_freq_table <- de_freq_table[names(de_freq_table) != ""] # Remove empty keywords if any
#     de_top <- names(de_freq_table[de_freq_table > 5])
#
#     if (length(de_top) > 0) {
#       cat("\n筛选 M_filtered 使用的 Top DE 关键词 (频率 > 5):\n")
#       print(de_top)
#
#       # 构建正则表达式模式，匹配任何一个 de_top 中的关键词
#       # 使用 \\b确保匹配整个单词
#       # 使用 fixed = FALSE, perl = TRUE 或者 engine="RE2" (如果安装了stringi/stringr) 来处理特殊字符
#       # R base grepl 的默认正则引擎可能对某些模式处理不佳，但对简单 | 分隔的词语通常可以
#       pattern_de_top <- paste0("\\b(", paste(de_top, collapse = "|"), ")\\b")
#
#       # 进行筛选
#       # M_filtered_indices <- grepl(pattern_de_top, M$DE, ignore.case = TRUE) # ignore.case 可选
#       # M_filtered <- M[M_filtered_indices & !is.na(M_filtered_indices), ]
#
#       # 或者使用 sapply 的方式，更精确匹配分号分隔的关键词
#       M_filtered_indices <- sapply(M$DE, function(de_string) {
#         if (is.na(de_string) || de_string == "") return(FALSE)
#         any(trimws(strsplit(de_string, ";")[[1]]) %in% de_top)
#       })
#       M_filtered <- M[M_filtered_indices, ]
#
#       cat("原始文献数量:", nrow(M), "\n")
#       cat("筛选后文献数量 (M_filtered):", nrow(M_filtered), "\n")
#       # print(head(M_filtered[, c("TI", "DE")])) # 查看筛选结果
#     } else {
#       cat("没有作者关键词 (DE) 的频率大于5，无法进行 M_filtered 筛选。\n")
#       M_filtered <- M[FALSE, ] # 创建一个空的数据框结构
#     }
#   } else {
#     cat("M$DE 字段没有有效数据，无法进行 M_filtered 筛选。\n")
#     M_filtered <- M[FALSE, ]
#   }
# } else {
#   cat("M 数据框或 M$DE 字段不存在，无法进行 M_filtered 筛选。\n")
# }
#