% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/net2VOSviewer.R
\name{net2VOSviewer}
\alias{net2VOSviewer}
\title{Open a bibliometrix network in VosViewer}
\usage{
net2VOSviewer(net, vos.path = NULL)
}
\arguments{
\item{net}{is an object created by networkPlot function.}

\item{vos.path}{is a character indicating the full path where VOSviewer.jar is located.}
}
\value{
It write a .net file that can be open in VOSviewer
}
\description{
\code{net2VOSviewer} plots a network created with \code{\link{networkPlot}} using \href{https://www.vosviewer.com/}{VOSviewer} by <PERSON><PERSON> and <PERSON>.
}
\details{
The function \code{\link{networkPlot}} can plot a bibliographic network previously created by \code{\link{biblioNetwork}}.
The network map can be plotted using internal R routines or using \href{https://www.vosviewer.com/}{VOSviewer} by <PERSON><PERSON> and <PERSON><PERSON>.
}
\examples{
# EXAMPLE 

# VOSviewer.jar have to be present in the working folder

# data(scientometrics, package = "bibliometrixData")

# NetMatrix <- biblioNetwork(scientometrics, analysis = "co-citation", 
# network = "references", sep = ";")

# net <- networkPlot(NetMatrix, n = 30, type = "kamada", Title = "Co-Citation",labelsize=0.5) 

# net2VOSviewer(net)

}
\seealso{
\code{\link{biblioNetwork}} to compute a bibliographic network.

\code{\link{networkPlot}} to create and plot a network object
}
