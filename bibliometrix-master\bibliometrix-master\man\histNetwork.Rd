% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/histNetwork.R
\name{histNetwork}
\alias{histNetwork}
\title{Historical co-citation network}
\usage{
histNetwork(M, min.citations, sep = ";", network = TRUE, verbose = TRUE)
}
\arguments{
\item{M}{is a bibliographic data frame obtained by the converting function
\code{\link{convert2df}}. It is a data matrix with cases corresponding to
manuscripts and variables to Field Tag in the original SCOPUS, OpenAlex, Lens.org and Clarivate
Analytics Web of Science file.}

\item{min.citations}{DEPRECATED. New algorithm does not use this parameters. It will be remove in the next version of bibliometrix.}

\item{sep}{is the field separator character. This character separates strings
in CR column of the data frame. The default is \code{sep = ";"}.}

\item{network}{is logical. If TRUE, function calculates and returns also the direct citation network. If FALSE,
the function returns only the local citation table.}

\item{verbose}{is logical. If TRUE, results are printed on screen.}
}
\value{
\code{histNetwork} returns an object of \code{class} "list"
  containing the following components:

  \tabular{lll}{ NetMatrix \tab  \tab the historical co-citation network
  matrix\cr histData \tab      \tab the set of n most cited references\cr M
  \tab      \tab the bibliographic data frame}
}
\description{
\code{histNetwork} creates a historical citation network from a bibliographic
data frame.
}
\examples{

\dontrun{
data(management, package = "bibliometrixData")

histResults <- histNetwork(management, sep = ";")
}

}
\seealso{
\code{\link{convert2df}} to import and convert a supported
  export file in a bibliographic data frame.

\code{\link{summary}} to obtain a summary of the results.

\code{\link{plot}} to draw some useful plots of the results.

\code{\link{biblioNetwork}} to compute a bibliographic network.
}
