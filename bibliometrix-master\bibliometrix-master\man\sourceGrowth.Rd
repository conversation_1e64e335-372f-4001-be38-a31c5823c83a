% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/sourceGrowth.R
\name{sourceGrowth}
\alias{sourceGrowth}
\title{Number of documents published annually per Top Sources}
\usage{
sourceGrowth(M, top = 5, cdf = TRUE)
}
\arguments{
\item{M}{is a data frame obtained by the converting function \code{\link{convert2df}}.
It is a data matrix with cases corresponding to articles and variables to Field Tag in the original ISI or SCOPUS file.}

\item{top}{is a numeric. It indicates the number of top sources to analyze. The default value is 5.}

\item{cdf}{is a logical. If TRUE, the function calculates the cumulative occurrences distribution.}
}
\value{
an object of class \code{data.frame}
}
\description{
It calculates yearly published documents of the top sources.
}
\examples{

data(scientometrics, package = "bibliometrixData")
topSO=sourceGrowth(scientometrics, top=1, cdf=TRUE)
topSO

# Plotting results
\dontrun{
install.packages("reshape2")
library(reshape2)
library(ggplot2)
DF=melt(topSO, id='Year')
ggplot(DF,aes(Year,value, group=variable, color=variable))+geom_line()
}

}
