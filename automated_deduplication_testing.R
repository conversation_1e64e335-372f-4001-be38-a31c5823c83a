# automated_deduplication_testing.R
# 脚本目的: 自动化测试不同的文献去重策略，并将结果输出到文本文件供人工审查。
# 调试阶段：关注 convert2df 是否能成功执行

# --- 1. 初始化与加载数据 ---

# 1.1 加载必要的库
cat("加载必要的库...\\n")
suppressPackageStartupMessages(library(bibliometrix))
suppressPackageStartupMessages(library(tidyverse))
# writexl 不是此特定脚本的直接需求，但如果后续要导出 M_initial_for_testing，可以保留
# if (!requireNamespace("writexl", quietly = TRUE)) {
#   cat("writexl 包未安装，正在尝试安装...\\n")
#   install.packages("writexl")
# }
# library(writexl)
cat("库加载完成。\\n")

# 1.2 设置工作目录和数据路径
# !!! 请确保修改为您的实际路径 !!!
working_dir <- "C:/Users/<USER>/Desktop/article/数据处理部分" 
raw_data_dir <- "C:/Users/<USER>/Desktop/数据文件/citespace数据" 

cat(paste0("尝试设置工作目录到: ", working_dir, "\\n"))
tryCatch({
  setwd(working_dir)
  cat("当前工作目录已设置为:", getwd(), "\\n")
}, error = function(e){
  stop(paste0("无法设置工作目录: ", working_dir, "。错误: ", e$message))
})

cat(paste0("\\n检查原始数据目录: ", raw_data_dir, "\\n"))
if (!dir.exists(raw_data_dir)) {
  stop(paste0("错误: 指定的原始数据目录不存在或无法访问: ", raw_data_dir))
} else {
  cat("成功: 原始数据目录存在: ", raw_data_dir, "\\n")
  
  # 列出目录下的所有内容以供诊断
  cat("目录下的所有文件和文件夹 (前20个，如果有):\n")
  all_contents <- list.files(path = raw_data_dir, all.files = TRUE, full.names = FALSE) # all.files=TRUE 显示隐藏文件
  if(length(all_contents) == 0){
      cat("  注意: 目录为空。\n")
  } else {
      print(head(all_contents, 20))
  }
}

# 1.3 读取并转换 WoS 数据文件
cat(paste0("\\n在目录 '", raw_data_dir, "' 中查找 .txt 文件 (忽略大小写)...\\n"))
# 使用 ignore.case = TRUE 来确保匹配 .txt, .TXT, .Txt 等
# full.names = TRUE 确保得到完整路径
# recursive = FALSE (默认) 确保只查找当前目录，不查找子目录
wos_files <- list.files(path = raw_data_dir, 
                          pattern = "\\.txt$", # 匹配以 .txt 结尾的文件
                          full.names = TRUE, 
                          ignore.case = TRUE, 
                          recursive = FALSE) 

if (length(wos_files) == 0) {
  cat(paste0("警告: 在指定目录 '", raw_data_dir, "' 下没有直接找到任何以 '.txt' (忽略大小写) 结尾的文件。\n"))
  cat("请检查以下事项:\n")
  cat("  1. 路径 '", raw_data_dir, "' 是否正确。\n")
  cat("  2. 该路径下是否确实存放了您的 WoS .txt 数据文件。\n")
  cat("  3. 文件的扩展名是否确实是 .txt (或 .TXT 等变体)。\n")
  cat("  4. 文件是否直接在该目录下，而不是在子目录中 (当前脚本不搜索子目录)。\n")
  # 尝试列出所有文件，看是否有拼写或扩展名问题
  all_files_in_dir <- list.files(path = raw_data_dir, full.names = FALSE, recursive = FALSE)
  if(length(all_files_in_dir) > 0) {
    cat("\n目录中的文件列表 (供检查扩展名或拼写):\n")
    print(all_files_in_dir)
  } else {
    cat("\n目录中没有任何文件。\n")
  }
  stop("脚本停止，因为没有找到数据文件。请检查上述提示。")
} else {
  cat("成功: 找到", length(wos_files), "个 .txt 文件，准备进行转换...\\n")
  cat("找到的文件列表:\n")
  for(f_path in wos_files){
      cat("  ", basename(f_path), "\n")
  }
  
  # 调试建议：如果上面列出了多个文件，但 convert2df 崩溃，
  # 请尝试一次只处理一个文件，看看是否是特定文件导致的问题。
  # 例如，取消下面这行的注释，并指定一个文件名进行测试：
  # wos_files <- file.path(raw_data_dir, "your_single_file_name.txt") # 替换为您的单个文件名
  # if (!file.exists(wos_files)) stop(paste("指定的单个测试文件不存在:", wos_files))
  # cat(paste0("\n调试：现在只尝试转换单个文件: ", wos_files, "\n"))
}

cat("\n准备调用 bibliometrix::convert2df...\n")
cat("这将处理以下文件(或单个文件，如果上面的调试代码被激活):\n")
print(wos_files)
# 初始内存使用情况 (调用 convert2df 之前):
# print(gc()) # 打印内存使用并尝试垃圾回收

M_initial_for_testing <- NULL # 初始化为NULL
tryCatch({
  cat("正在执行 convert2df... 这可能需要一些时间...\n")
  # remove.duplicates = TRUE 是 convert2df 的默认行为
  M_initial_for_testing <- bibliometrix::convert2df(file = wos_files, dbsource = "wos", format = "plaintext") 
  cat("convert2df 执行成功!\n")
}, error = function(e) {
  cat("convert2df 执行时发生错误!\n")
  cat("错误信息:", e$message, "\n")
  M_initial_for_testing <<- NULL # 确保出错时 M_initial_for_testing 仍为 NULL 或之前的值
  stop(paste0("convert2df 失败: ", e$message))
})

if (!is.null(M_initial_for_testing)) {
  cat("初始转换完成 (convert2df)。\n")
  cat("数据框 M_initial_for_testing 包含", nrow(M_initial_for_testing), "条记录 和", ncol(M_initial_for_testing), "个字段。\n")
  # cat("M_initial_for_testing 对象大小:", format(object.size(M_initial_for_testing), units = "auto"), "\n")
  # cat("内存使用情况 (调用 convert2df 之后):\n")
  # print(gc())
  
  # 暂时保存转换后的数据，以防后续步骤崩溃可以快速恢复
  # saveRDS(M_initial_for_testing, file.path(working_dir, "M_initial_after_convert2df.rds"))
  # cat(paste0("已将 M_initial_for_testing 保存到: ", file.path(working_dir, "M_initial_after_convert2df.rds"), "\n"))
  # cat("如果脚本在后续步骤崩溃，您可以尝试使用 M_initial_for_testing <- readRDS('path/to/M_initial_after_convert2df.rds') 来加载它，跳过 convert2df。\n")

} else {
  cat("M_initial_for_testing 未能成功创建。脚本无法继续。\n")
  stop("由于 convert2df 未成功返回数据，脚本终止。请检查之前的错误信息。")
}

# --- 2. 自动化去重测试框架 ---
cat("\n--- 初始化自动化去重测试框架 ---\n")
dedup_output_dir <- file.path(working_dir, "deduplication_tests_output")
if (!dir.exists(dedup_output_dir)){
  dir.create(dedup_output_dir, recursive = TRUE)
  cat("创建去重测试输出目录:", dedup_output_dir, "\n")
} else {
  cat("去重测试输出目录已存在:", dedup_output_dir, "\n")
}
# run_deduplication_test 函数定义在下面

# --- 3. 执行系列去重测试 ---
# (之前的占位注释和禁用的测试调用块已移除, 实际测试调用在 run_deduplication_test 函数定义之后)

M <- M_initial_for_testing 
if(!is.null(M)){
    cat(paste0("\n主数据框 M 当前包含 ", nrow(M), " 条记录 (此为 convert2df 后的初始状态)。\n"))
} else {
    cat("\n主数据框 M 未能成功加载或创建。\n")
}

# 完整 run_deduplication_test 函数定义
run_deduplication_test <- function(data_input, field_to_match, is_exact_match, tolerance_value, strategy_name_suffix = "", output_dir_path) {
  
  # 构建策略名称和输出文件名
  strategy_name <- paste0(field_to_match, 
                          ifelse(is_exact_match, "_Exact", paste0("_Tol", gsub("\\.", "_", as.character(tolerance_value)))), 
                          strategy_name_suffix)
  output_file_path <- file.path(output_dir_path, paste0("report_", strategy_name, ".txt"))
  
  # 使用sink开始将输出重定向到文件 (覆盖模式)
  zz <- file(output_file_path, open = "wt") # open for writing text
  sink(zz)
  sink(zz, type = "message") # Redirect messages (like from cat) as well

  cat(paste0("--- 测试策略报告: ", strategy_name, " ---\\n"))
  cat(paste0("执行时间: ", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\\n"))
  cat(paste0("输入记录数: ", nrow(data_input), "\\n"))
  cat(paste0("匹配字段: '", field_to_match, "', Exact匹配: ", is_exact_match, 
             ", 相似度阈值(tol): ", ifelse(is_exact_match, "N/A", format(tolerance_value, nsmall=2)), "\\n\\n"))
  
  M_before_test_func <- data_input 
  
  # 确保关键字段是字符型 (UT 和要匹配的字段)
  if ("UT" %in% names(M_before_test_func)) M_before_test_func$UT <- as.character(M_before_test_func$UT)
  if (field_to_match %in% names(M_before_test_func)) M_before_test_func[[field_to_match]] <- as.character(M_before_test_func[[field_to_match]])
  # 其他常用字段也转换为字符以避免后续提取时出错
  common_fields <- c("TI", "AU", "AF", "PY", "SO", "AB", "DI")
  for(cf in common_fields){
      if (cf %in% names(M_before_test_func)) M_before_test_func[[cf]] <- as.character(M_before_test_func[[cf]])
  }

  M_to_process_func <- M_before_test_func
  M_skipped_func <- data.frame() # 初始化为空数据框
  
  if (!is_exact_match && field_to_match %in% names(M_before_test_func)) {
    condition_empty_na <- is.na(M_before_test_func[[field_to_match]]) | M_before_test_func[[field_to_match]] == ""
    if(any(condition_empty_na)){
        M_to_process_func <- M_before_test_func[!condition_empty_na, , drop = FALSE]
        M_skipped_func <- M_before_test_func[condition_empty_na, , drop = FALSE]
        cat(paste0("注意: 有 ", nrow(M_skipped_func), " 条记录因 '", field_to_match, "' 字段为空或NA而未直接参与此轮匹配。\\n"))
    }
  }

  M_after_processed_part_func <- M_to_process_func 
  if(nrow(M_to_process_func) > 1) { 
      cat(paste0("对 ", nrow(M_to_process_func), " 条记录执行 duplicatedMatching (Field='", field_to_match, "', exact=", is_exact_match, 
                 if(!is_exact_match) paste0(", tol=", tolerance_value) else "", ")...\n"))
      if(is_exact_match){
          M_after_processed_part_func <- duplicatedMatching(M_to_process_func, Field = field_to_match, exact = TRUE)
      } else {
          M_after_processed_part_func <- duplicatedMatching(M_to_process_func, Field = field_to_match, exact = FALSE, tol = tolerance_value)
      }
      cat("duplicatedMatching 执行完毕。\n")
  } else {
      cat(paste0("字段 '", field_to_match, "' 非空/NA的记录不足2条（仅 ", nrow(M_to_process_func) ," 条），跳过 duplicatedMatching。\n"))
  }

  M_after_test_func <- M_after_processed_part_func
  if (nrow(M_skipped_func) > 0) {
    if (nrow(M_after_processed_part_func) == 0 ) { 
        M_after_test_func <- M_skipped_func
    } else { # 尝试合并，确保列名对齐
        # 找出共同的列名
        cols_processed <- names(M_after_processed_part_func)
        cols_skipped <- names(M_skipped_func)
        common_cols <- intersect(cols_processed, cols_skipped)
        
        # 如果没有共同列或者关键列丢失，这可能是一个问题，但我们尝试合并
        if(length(common_cols) > 0){
             M_after_test_func <- rbind(M_after_processed_part_func[, common_cols, drop=FALSE], 
                                     M_skipped_func[, common_cols, drop=FALSE])
        } else {
             cat("警告: 合并被跳过记录时，处理后部分与跳过部分无共同列名。整体计数可能不准。\n")
             # 此时 M_after_test_func 仍然是 M_after_processed_part_func
        }
    }
  }
  
  removed_in_processed_part_count <- nrow(M_to_process_func) - nrow(M_after_processed_part_func)
  overall_removed_count <- nrow(M_before_test_func) - nrow(M_after_test_func)

  cat(paste0("\\n处理非空'", field_to_match, "'字段记录: 从 ", nrow(M_to_process_func) ," 条变为 ", nrow(M_after_processed_part_func) ," 条 (移除了 ", removed_in_processed_part_count, " 条)。\\n"))
  cat(paste0("整体数据框: 从 ", nrow(M_before_test_func) ," 条变为 ", nrow(M_after_test_func) ," 条 (总共移除了 ", overall_removed_count, " 条)。\\n\\n"))
  
  # 控制台摘要 (输出到标准输出，而不是被sink的文件)
  message(paste0("测试策略 '", strategy_name, "': 在处理部分移除了 ", removed_in_processed_part_count, " 条. 总移除 ", overall_removed_count, " 条. 报告: ", basename(output_file_path)))

  if (removed_in_processed_part_count > 0 && "UT" %in% names(M_to_process_func) && "UT" %in% names(M_after_processed_part_func)) {
    # 使用 M_to_process_func 和 M_after_processed_part_func 来找出被移除的记录的详情
    removed_articles_details_func <- dplyr::anti_join(M_to_process_func, M_after_processed_part_func, by = "UT")
    
    if (nrow(removed_articles_details_func) > 0) {
      cat("--- 被移除文献对详情 (基于参与匹配的部分，最多显示前200对) ---\n")
      limit_print_pairs <- min(nrow(removed_articles_details_func), 200) # 限制输出数量
      
      for (i_pair in 1:limit_print_pairs) {
        # 获取被移除记录的完整信息 (从 M_to_process_func, 即匹配前的状态)
        removed_ut_val <- as.character(removed_articles_details_func$UT[i_pair])
        # 确保 removed_record 只有一行
        removed_record_candidates <- M_to_process_func[M_to_process_func$UT == removed_ut_val, , drop=FALSE]
        if(nrow(removed_record_candidates)==0){
            cat(paste0("警告: 无法在 M_to_process_func 中找到 UT 为 ", removed_ut_val, " 的被移除记录以显示详情。\n"))
            next
        }
        removed_record <- removed_record_candidates[1, , drop=FALSE]


        cat(paste0("--- 识别出的重复文献 (\", i_pair, \" / \", nrow(removed_articles_details_func), \") ---\n"))
        cat("  [被移除文献详情]:\n")
        cat(paste0("    UT: ", removed_record$UT, "\n"))
        cat(paste0("    TI: '", removed_record$TI, "'\n"))
        cat(paste0("    PY: ", removed_record$PY, "\n"))
        cat(paste0("    AU: ", substr(removed_record$AU, 1, 150), ifelse(nchar(removed_record$AU)>150, "...", ""), "\n"))
        cat(paste0("    SO: ", removed_record$SO, "\n"))
        cat(paste0("    AB: ", substr(removed_record$AB, 1, 250), ifelse(nchar(removed_record$AB)>250, "...", ""), "\n"))

        # 查找对应的保留文献 (在 M_after_processed_part_func 中)
        field_content_removed <- removed_record[[field_to_match]]
        
        if (!is.na(field_content_removed) && field_content_removed != "") {
            retained_candidates <- M_after_processed_part_func[
                                        !is.na(M_after_processed_part_func[[field_to_match]]) & 
                                        M_after_processed_part_func[[field_to_match]] != "" & 
                                        M_after_processed_part_func$UT != removed_record$UT, , drop=FALSE] # 排除自身
            
            if(nrow(retained_candidates) > 0){
                best_similarity_val <- -1
                kept_record <- NULL # 初始化为NULL

                if(is_exact_match){
                    exact_matches_retained <- retained_candidates[as.character(retained_candidates[[field_to_match]]) == as.character(field_content_removed), , drop=FALSE]
                    if(nrow(exact_matches_retained) > 0) {
                        kept_record <- exact_matches_retained[1, , drop=FALSE] 
                        best_similarity_val <- 1 
                    }
                } else { # 模糊匹配
                    # 确保 retained_candidates[[field_to_match]] 是字符向量
                    comparison_vector_kept <- as.character(retained_candidates[[field_to_match]])
                    distances <- utils::adist(as.character(field_content_removed), comparison_vector_kept)[1, ]
                    
                    len_removed <- nchar(as.character(field_content_removed))
                    len_kept <- nchar(comparison_vector_kept)
                    max_lengths <- pmax(len_removed, len_kept)
                    max_lengths[max_lengths == 0] <- 1 
                    similarities <- 1 - (distances / max_lengths)
                    similarities[is.nan(similarities) | is.infinite(similarities)] <- 0 
                    
                    candidate_indices_kept <- which(similarities >= tolerance_value)
                    if (length(candidate_indices_kept) > 0) {
                        best_match_local_idx_kept <- which.max(similarities[candidate_indices_kept])
                        kept_record <- retained_candidates[candidate_indices_kept[best_match_local_idx_kept], , drop=FALSE]
                        best_similarity_val <- similarities[candidate_indices_kept[best_match_local_idx_kept]]
                    }
                }

                if(!is.null(kept_record) && nrow(kept_record) > 0){ # 确保kept_record不是NULL且有行
                    cat("  [可能的对应保留文献详情]:\n")
                    cat(paste0("    UT: ", kept_record$UT, " (匹配字段 '", field_to_match, "' 相似度: ", round(best_similarity_val, 4), ")\n"))
                    cat(paste0("    TI: '", kept_record$TI, "'\n"))
                    cat(paste0("    PY: ", kept_record$PY, "\n"))
                    cat(paste0("    AU: ", substr(kept_record$AU, 1, 150), ifelse(nchar(kept_record$AU)>150, "...", ""), "\n"))
                    cat(paste0("    SO: ", kept_record$SO, "\n"))
                    cat(paste0("    AB: ", substr(kept_record$AB, 1, 250), ifelse(nchar(kept_record$AB)>250, "...", ""), "\n"))
                } else { cat("  [可能的对应保留文献详情]: 未能找到满足条件的对应保留文献。\n")}
            } else {cat("  [可能的对应保留文献详情]: 保留数据中无其他有效记录可比较 (在排除自身后)。\n")}
        } else {cat("  [可能的对应保留文献详情]: 被移除文献的匹配字段 ('", field_to_match ,"') 为空或NA。\n")}
        cat(paste0("--- 结束重复对 (\", i_pair, \") ---\n"))
      } 
      if(nrow(removed_articles_details_func) > limit_print_pairs) cat("\\n... (还有更多被移除的文献对未在报告中逐条打印详情) ...\\n")
    } else {
      # This case means removed_in_processed_part_count > 0 but anti_join found nothing,
      # which could happen if UTs are not perfectly unique post-processing or other anomoly.
      cat("提示: 此策略在处理部分移除了记录，但未能通过 UT 明确识别被移除的具体文献及其对应保留文献的详情。\n")
      cat("这可能是由于UT不唯一，或在中间步骤丢失，或duplicatedMatching的内部逻辑导致UT不再是唯一键。\n")
      cat("移除数量 (removed_in_processed_part_count): ", removed_in_processed_part_count, "\n")
    }
  } else if (removed_in_processed_part_count > 0) {
      cat("提示: 此策略在处理部分移除了记录，但因缺少UT字段或其他原因，无法打印详细的文献对信息。\n")
  }

  cat(paste0("\n--- 测试策略报告结束: ", strategy_name, " ---\n"))
  
  # 关闭sink，恢复输出到控制台
  sink(type = "message")
  sink()
  close(zz)
  
  return(invisible(NULL)) 
}
# --- 测试函数定义结束 ---


# --- 3. 执行系列去重测试 ---
cat("\n--- 开始执行自动化去重对比测试 ---\n")
cat(paste0("所有测试报告将输出到目录: ", dedup_output_dir, "\n"))

# 测试1: 基于 UT 的精确匹配
cat("\n执行测试: UT_Exact\n")
run_deduplication_test(data_input = M_initial_for_testing, 
                       field_to_match = "UT", 
                       is_exact_match = TRUE, 
                       tolerance_value = NA, # Not used for exact match
                       output_dir_path = dedup_output_dir)

# 测试2: 基于 TI (标题) 的不同 tol 值
cat("\n执行测试系列: TI_Tol_X\n")
tolerances_ti <- c(0.99, 0.98, 0.95, 0.90)
for (tol_ti in tolerances_ti) {
  run_deduplication_test(data_input = M_initial_for_testing, 
                         field_to_match = "TI", 
                         is_exact_match = FALSE, 
                         tolerance_value = tol_ti,
                         output_dir_path = dedup_output_dir)
}

# 测试3: 基于 AB (摘要) 的不同 tol 值
cat("\n检查摘要 (AB) 字段填充情况...\n")
ab_non_empty_count <- sum(!is.na(M_initial_for_testing$AB) & M_initial_for_testing$AB != "", na.rm = TRUE)
ab_total_count <- nrow(M_initial_for_testing)
ab_fill_rate <- ifelse(ab_total_count > 0, (ab_non_empty_count / ab_total_count) * 100, 0)

cat(paste0("摘要 (AB) 字段在初始数据中有效记录数: ", ab_non_empty_count, " / ", ab_total_count, 
           " (填充比例: ", round(ab_fill_rate, 2), "%)\n"))

# 您可以调整此阈值，例如，如果填充率过低，测试可能意义不大
ab_test_threshold_percentage <- 30 
if (ab_fill_rate >= ab_test_threshold_percentage) {
  cat(paste0("摘要填充率 (\",round(ab_fill_rate, 2),\"%) >= ", ab_test_threshold_percentage, "%，开始基于AB的去重测试...\n"))
  tolerances_ab <- c(0.98, 0.95, 0.90)
  for (tol_ab in tolerances_ab) {
    run_deduplication_test(data_input = M_initial_for_testing, 
                           field_to_match = "AB", 
                           is_exact_match = FALSE, 
                           tolerance_value = tol_ab,
                           output_dir_path = dedup_output_dir)
  }
} else {
  cat(paste0("摘要填充率较低 (\",round(ab_fill_rate, 2),\"%)，低于 ", ab_test_threshold_percentage, "%，跳过基于AB的去重测试。\n"))
}

cat("\n--- 所有自动化去重对比测试执行完毕 ---\n")
cat(paste0("测试报告已输出到目录: ", dedup_output_dir, "\n"))
cat("请检查上述目录中的 .txt 文件以获取详细的去重结果。\n")
cat("注意: 主数据框 M_initial_for_testing 未被这些测试修改。\n")
cat("如需进行后续分析，请基于测试报告选择最佳策略，并手动对 M_initial_for_testing 应用该策略以生成最终的 M 进行下游分析。\n")

# 为了演示，后续代码如果需要一个 M 对象，它将是未经这些测试修改的初始数据
M <- M_initial_for_testing 
cat(paste0("\n主数据框 M 当前包含 ", nrow(M), " 条记录 (此为 convert2df 后的初始状态，未经额外测试去重)。\n"))

cat("\n--- 自动化去重测试脚本执行完毕 ---\n")