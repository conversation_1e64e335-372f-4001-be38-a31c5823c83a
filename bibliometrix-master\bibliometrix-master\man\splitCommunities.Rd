% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/splitCommunities.R
\name{splitCommunities}
\alias{splitCommunities}
\title{Splitting Network communities}
\usage{
splitCommunities(graph, n = NULL)
}
\arguments{
\item{graph}{is a network plot obtained by the function \code{\link{networkPlot}}.}

\item{n}{is an integer. It indicates the number of vertices to plot for each community.}
}
\value{
It is a network object of the class \code{igraph}
}
\description{
\code{networkPlot} Create a network plot with separated communities.
}
\details{
The function \code{\link{splitCommunities}} splits communities in separated subnetworks from a bibliographic network plot previously created by \code{\link{networkPlot}}.
}
\examples{
# EXAMPLE Keywordd co-occurrence network

data(management, package = "bibliometrixData")

NetMatrix <- biblioNetwork(management, analysis = "co-occurrences", 
network = "keywords", sep = ";")

net <- networkPlot(NetMatrix, n = 30, type = "auto", 
               Title = "Co-occurrence Network",labelsize=1, verbose=FALSE) 

graph <- splitCommunities(net$graph, n = 30)

}
\seealso{
\code{\link{biblioNetwork}} to compute a bibliographic network.

\code{\link{networkPlot}} to plot a bibliographic network.

\code{\link{net2VOSviewer}} to export and plot the network with VOSviewer software.

\code{\link{cocMatrix}} to compute a co-occurrence matrix.

\code{\link{biblioAnalysis}} to perform a bibliometric analysis.
}
