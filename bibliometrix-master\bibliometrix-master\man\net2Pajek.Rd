% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/net2Pajek.R
\name{net2Pajek}
\alias{net2Pajek}
\title{Save a network graph object as Pajek files}
\usage{
net2Pajek(net, filename = "my_pajek_network", path = NULL)
}
\arguments{
\item{net}{is a network graph object returned by the function \code{\link{networkPlot}}.}

\item{filename}{is a character. It indicates the filename for Pajek export files.}

\item{path}{is a character. It indicates the path where the files will be saved. When path="NULL, the files will be saved in the current folder. Default is NULL.}
}
\value{
The function returns no object but will save three Pajek files in the folder given in the "path" argument with the name "filename.clu," "filename.vec," and "filename.net."
}
\description{
The function \code{\link{net2Pajek}} save a bibliographic network previously created by \code{\link{networkPlot}} as pajek files.
}
\examples{
\dontrun{
data(management, package = "bibliometrixData")

NetMatrix <- biblioNetwork(management, analysis = "co-occurrences", 
network = "keywords", sep = ";")

net <- networkPlot(NetMatrix, n = 30, type = "auto", Title = "Co-occurrence Network",labelsize=1) 

net2Pajek(net, filename="pajekfiles", path=NULL)
}
}
\seealso{
\code{\link{net2VOSviewer}} to export and plot the network with VOSviewer software.
}
