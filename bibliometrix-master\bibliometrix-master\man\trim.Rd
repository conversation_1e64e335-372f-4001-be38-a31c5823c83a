% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/trim.R
\name{trim}
\alias{trim}
\title{Deleting leading and ending white spaces}
\usage{
trim(x)
}
\arguments{
\item{x}{is a \code{character} object.}
}
\value{
an object of class \code{character}
}
\description{
Deleting leading and ending white spaces from a \code{character} object.
}
\details{
\code{tableTag} is an internal routine of \code{bibliometrics} package.
}
\examples{

char <- c("  <PERSON>", "<PERSON>", " <PERSON>")
char
trim(char)

}
