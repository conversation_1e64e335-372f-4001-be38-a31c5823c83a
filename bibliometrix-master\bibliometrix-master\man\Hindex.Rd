% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/Hindex.R
\name{Hindex}
\alias{Hindex}
\title{h-index calculation}
\usage{
Hindex(M, field = "author", elements = NULL, sep = ";", years = Inf)
}
\arguments{
\item{M}{is a bibliographic data frame obtained by the converting function \code{\link{convert2df}}.
It is a data matrix with cases corresponding to manuscripts and variables to Field Tag in the original SCOPUS and Clarivate Analytics WoS file.}

\item{field}{is character. It can be equal to c("author", "source"). field indicates if H-index have to be calculated for a list of authors or for a list of sources. Default
value is \code{field = "author"}.}

\item{elements}{is a character vector. It contains the authors' names list or the source list for which you want to calculate the H-index. When the field is
"author", the argument has the form C("SURNAME1 N","SURNAME2 N",...), in other words, for each author: surname and initials separated by one blank space. If elements=NULL, the function calculates impact indices for all elements contained in the data frame.
i.e for the authors SEMPRONIO TIZIO CAIO and ARIA MASSIMO \code{elements} argument is \code{elements = c("SEMPRONIO TC", "ARIA M")}.}

\item{sep}{is the field separator character. This character separates authors in each string of AU column of the bibliographic data frame. The default is \code{sep = ";"}.}

\item{years}{is a integer. It indicates the number of years to consider for Hindex calculation. Default is Inf.}
}
\value{
an object of \code{class} "list". It contains two elements: H is a data frame with h-index, g-index and m-index for each author; CitationList is a list with the bibliographic collection for each author.
}
\description{
It calculates the authors' h-index and its variants.
}
\examples{

### EXAMPLE 1: ###
 
data(scientometrics, package = "bibliometrixData")

authors <- c("SMALL H", "CHEN DZ")

Hindex(scientometrics, field = "author", elements = authors, sep = ";")$H

Hindex(scientometrics, field = "source", elements = "SCIENTOMETRICS", sep = ";")$H

### EXAMPLE 2: Garfield h-index###
 
data(garfield, package = "bibliometrixData")

indices=Hindex(garfield, field = "author", elements = "GARFIELD E", years=Inf, sep = ";")

# h-index, g-index and m-index of Eugene Garfield
indices$H

# Papers and total citations
head(indices$CitationList[[1]])

}
\seealso{
\code{\link{convert2df}} to import and convert an WoS or SCOPUS Export file in a bibliographic data frame.

\code{\link{biblioAnalysis}} function for bibliometric analysis.

\code{\link{summary}} to obtain a summary of the results.

\code{\link{plot}} to draw some useful plots of the results.
}
