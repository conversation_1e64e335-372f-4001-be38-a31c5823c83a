% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/networkStat.R
\name{networkStat}
\alias{networkStat}
\title{Calculating network summary statistics}
\usage{
networkStat(object, stat = "network", type = "degree")
}
\arguments{
\item{object}{is a network matrix obtained by the function \code{\link{biblioNetwork}} or an graph object of the class \code{igraph}.}

\item{stat}{is a character. It indicates which statistics are to be calculated. \code{stat = "network"} calculates the statistics related to the network; 
\code{stat = "all"} calculates the statistics related to the network and the individual nodes that compose it. Default value is \code{stat = "network"}.}

\item{type}{is a character. It indicates which centrality index is calculated. type values can be c("degree", "closeness", "betweenness","eigenvector","pagerank","hub","authority", "all"). Default is "degree".}
}
\value{
It is a list containing the following elements:
\tabular{lll}{
\code{graph} \tab  \tab a network object of the class \code{igraph}\cr
\code{network} \tab  \tab a \code{communities} a list with the main statistics of the network\cr
\code{vertex} \tab  \tab a data frame with the main measures of centrality and prestige of vertices.\cr}
}
\description{
\code{networkStat} calculates main network statistics.
}
\details{
The function \code{\link{networkStat}} can calculate the main network statistics from a bibliographic network previously created by \code{\link{biblioNetwork}}.
}
\examples{
# EXAMPLE Co-citation network

# to run the example, please remove # from the beginning of the following lines
# data(scientometrics, package = "bibliometrixData")

# NetMatrix <- biblioNetwork(scientometrics, analysis = "co-citation", 
#      network = "references", sep = ";")

# netstat <- networkStat(NetMatrix, stat = "all", type = "degree")

}
\seealso{
\code{\link{biblioNetwork}} to compute a bibliographic network.

\code{\link{cocMatrix}} to compute a co-occurrence matrix.

\code{\link{biblioAnalysis}} to perform a bibliometric analysis.
}
