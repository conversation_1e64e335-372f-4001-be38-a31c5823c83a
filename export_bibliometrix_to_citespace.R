# export_bibliometrix_to_citespace.R
# 目的: 将 bibliometrix 数据框导出为 CiteSpace 兼容的 WoS 纯文本 (.txt) 格式

# 辅助函数：为 bibliometrix 字段获取对应的 WoS 标签
# 改编自用户提供的 A→B→C→A.r 脚本中的函数
# 此函数将 bibliometrix 数据框中的列名映射到标准的 WoS 两字符标签
get_wos_tag_for_field <- function(bib_field_name) {
  # 主要字段映射关系
  # 键是 bibliometrix 数据框中可能的列名，值是对应的 WoS 输出标签
  field_map <- list(
    "AU" = "AU",     # 作者
    "TI" = "TI",     # 标题
    "SO" = "SO",     # 出版物名称 (Source)
    "DT" = "DT",     # 文档类型 (Document Type)
    "DE" = "DE",     # 作者关键词 (Author Keywords)
    "ID" = "ID",     # 关键词Plus (Keywords Plus)
    "AB" = "AB",     # 摘要 (Abstract)
    "C1" = "C1",     # 作者地址 (Author Affiliations)
    "RP" = "RP",     # 通讯作者地址 (Reprint Address)
    "CR" = "CR",     # 引用文献 (Cited References)
    "TC" = "TC",     # 总被引次数 (Times Cited)
    "PY" = "PY",     # 出版年份 (Publication Year)
    "SC" = "SC",     # 研究领域 (Subject Category - Web of Science Categories or others)
    "UT" = "UT",     # 唯一标识符 (Accession Number)
    "DI" = "DI",     # DOI
    "LA" = "LA",     # 语言 (Language)
    "EM" = "EM",     # 电子邮件地址 (Email)
    "FU" = "FU",     # 基金资助信息 (Funding Agency and Grant Numbers)
    "FX" = "FX",     # 基金文本 (Funding Text)
    "NR" = "NR",     # 参考文献数量 (Cited Reference Count)
    "AF" = "AF",     # 作者全名 (Author Full Names)
    "CA" = "CA",     # 团体作者 (Group Authors)
    "J9" = "J9",     # 29字符源刊缩写 (Journal Abbreviation)
    "JI" = "JI",     # ISO源刊缩写 (ISO Journal Abbreviation)
    "VL" = "VL",     # 卷 (Volume)
    "IS" = "IS",     #期 (Issue)
    "BP" = "BP",     # 开始页 (Beginning Page)
    "EP" = "EP",     # 结束页 (Ending Page)
    "PG" = "PG",     # 页数 (Page Count)
    "WC" = "WC",     # Web of Science 分类 (WoS Categories)
    "SN" = "SN",     # ISSN
    "BN" = "BN",     # ISBN
    "AR" = "AR",     # 文章号 (Article Number)
    "GA" = "GA",     # 文献传递号 (Document Delivery Number)
    "PM" = "PM",     # PubMed ID
    "PU" = "PU",     # 出版社 (Publisher)
    "PI" = "PI",     # 出版社城市 (Publisher City)
    "PA" = "PA",     # 出版社地址 (Publisher Address)
    "PD" = "PD"      # 出版日期 (Publication Date)
    # 注意: "A1", "BA", "BF" 等在原脚本中存在，但可能不是 bibliometrix 直接输出的标准字段，
    # 或者需要特殊派生，此处暂未包含，以聚焦直接转换。
  )
  
  tag <- field_map[[bib_field_name]]
  
  if (is.null(tag)) {
    # 如果字段名不在映射中，但本身就是2字符大写（可能已是WoS标签），则直接使用
    if (nchar(bib_field_name) == 2 && grepl("^[A-Z0-9]{2}$", bib_field_name)) {
      return(bib_field_name)
    }
    return(NA_character_) # 对于未知或不需导出的字段返回NA
  }
  return(tag)
}

#' 将 bibliometrix 数据框导出为 CiteSpace 兼容的 WoS 纯文本文件
#'
#' @param bib_df R 数据框，格式应为 bibliometrix 包转换文献数据后的结果。
#' @param output_filepath 字符串，指定输出的 .txt 文件的完整路径和名称。
#' @param bib_fields_in_order 一个字符向量，指定 bibliometrix 数据框中的列名（字段）应按何顺序写入文件。
#'                              如果为 NULL，则会使用一个预定义的常用 WoS 字段顺序。
#'
#' @return 返回输出文件的路径（不可见）。
#' @export
#'
#' @examples
#' \dontrun{
#' # # 创建一个虚拟的 bibliometrix 数据框用于测试
#' M_dummy <- data.frame(
#'   AU = c("Smith J; Doe K", "Lee S"),
#'   TI = c("A Study of Things\nPart Two", "Another Study"),
#'   SO = c("Journal of Studies", "Annals of Research"),
#'   DE = c("Widgets; Gadgets", "Gizmos"),
#'   ID = c("Innovation; Technology", NA_character_),
#'   AB = c("This is an abstract.\nIt has two paragraphs.", "This is another abstract."),
#'   CR = c("AuthorA, 2020, J SOURCE, V1, P10\nAuthorB, 2019, BOOK TITLE", 
#'          "AuthorC, 2021, CONF PROC"),
#'   PY = c(2023, 2024),
#'   DT = c("Article", "Review"),
#'   C1 = c("[Smith J] University A; [Doe K] University B", "[Lee S] University C"),
#'   EM = c("<EMAIL>; <EMAIL>", "<EMAIL>"),
#'   FU = c("Grant X; Grant Y", "Grant Z"),
#'   FX = c("This work was funded by...\nAdditional notes.", NA_character_),
#'   RP = c("[Smith J] University A", "[Lee S] University C"),
#'   SC = c("Category1; Category2", "Category3"),
#'   WC = c("WoSCat1; WoSCat2", "WoSCat3"),
#'   UT = c("WOS:0001", "WOS:0002"),
#'   DI = c("10.1000/xyz123", "10.1000/abc456"),
#'   TC = c(10L,5L),
#'   NR = c(2L,1L),
#'   stringsAsFactors = FALSE
#' )
#' 
#' output_file_path <- file.path(tempdir(), "citespace_export_test.txt")
#' export_bibliometrix_to_citespace_txt(M_dummy, output_file_path)
#' cat("Test file written to:", output_file_path, "\n")
#'
#' # # 使用您实际的数据 (例如 M_initial_for_testing):
#' # if (exists("M_initial_for_testing") && 
#' #     !is.null(M_initial_for_testing) && 
#' #     nrow(M_initial_for_testing) > 0) {
#' #   actual_output_file <- "export_citespace_from_M_initial.txt"
#' #   # 确保 M_initial_for_testing 是您实际的 bibliometrix 数据框
#' #   export_bibliometrix_to_citespace_txt(M_initial_for_testing, actual_output_file)
#' # } else {
#' #   cat("M_initial_for_testing is not available or empty. Load your data first.\n")
#' # }
#' }
export_bibliometrix_to_citespace_txt <- function(bib_df, 
                                                 output_filepath, 
                                                 bib_fields_in_order = NULL) {

  # 定义默认的 bibliometrix 字段处理顺序 (对应 WoS 标签的常见顺序)
  if (is.null(bib_fields_in_order)) {
    bib_fields_in_order <- c(
      # PT (Publication Type) is handled specially first
      "AU", "AF", "CA", "TI", "SO", "LA", "DT", "DE", "ID", "AB", 
      "C1", "RP", "EM", "FU", "FX", "CR", "NR", "TC", "PU", "PI", "PA", 
      "SN", "BN", "J9", "JI", "PD", "PY", "VL", "IS", "PN", "SU", 
      "AR", "BP", "EP", "PG", "WC", "SC", "GA", "UT", "DI", "PM"
    )
  }

  # 定义不同类型字段的处理方式
  # 1. 字段值通过分号分隔，每个子项重复对应WoS标签换行输出
  semicolon_split_fields <- c("AU", "AF", "DE", "ID", "EM", "FU", "WC", "SC", "C1", "RP", "CA")
  # 2. 字段值内通过换行符分隔（如bibliometrix中CR字段），每个子项重复对应WoS标签换行输出
  newline_split_repeat_tag_fields <- c("CR")
  # 3. 字段值内若含换行符，则首行带WoS标签，后续行缩进3空格作为续行
  newline_indent_fields <- c("AB", "TI", "FX", "PA") # TI也可能从某些数据库导入时带有换行

  # 打开到输出文件的连接，使用UTF-8编码
  con <- file(output_filepath, "w", encoding = "UTF-8")
  
  # 写入WoS文件头
  writeLines("FN Clarivate Analytics Web of Science", con)
  writeLines("VR 1.0", con)

  # 遍历数据框中的每一条记录 (每一行)
  for (i in 1:nrow(bib_df)) {
    record <- bib_df[i, ]

    # --- PT (Publication Type) 标签处理 ---
    pt_val <- "J" # 默认为 Journal Article
    if ("DT" %in% names(record) && !is.na(record[["DT"]])) {
      dt_upper <- toupper(as.character(record[["DT"]]))
      if (any(sapply(c("ARTICLE", "JOURNAL"), function(s) grepl(s, dt_upper))) && 
          !any(sapply(c("REVIEW", "BOOK REVIEW"), function(s) grepl(s, dt_upper)))) pt_val <- "J"
      else if (grepl("BOOK REVIEW", dt_upper)) pt_val <- "J"
      else if (grepl("REVIEW", dt_upper) && !grepl("BOOK", dt_upper)) pt_val <- "J"
      else if (grepl("BOOK", dt_upper) && !grepl("CHAPTER", dt_upper)) pt_val <- "B"
      else if (grepl("BOOK CHAPTER", dt_upper)) pt_val <- "C"
      else if (any(sapply(c("PROCEEDINGS PAPER", "CONFERENCE"), function(s) grepl(s, dt_upper)))) pt_val <- "S"
      else if (grepl("EDITORIAL", dt_upper)) pt_val <- "J"
      else if (grepl("LETTER", dt_upper)) pt_val <- "J"
      else if (grepl("NOTE", dt_upper)) pt_val <- "J"
      else if (grepl("MEETING ABSTRACT", dt_upper)) pt_val <- "M"
      else if (grepl("PATENT", dt_upper)) pt_val <- "P"
    }
    writeLines(paste("PT", pt_val), con)

    # --- 其他字段处理 ---
    for (bib_col_name in bib_fields_in_order) {
      wos_tag <- get_wos_tag_for_field(bib_col_name)
      if (!is.na(wos_tag) && bib_col_name %in% names(record)) {
        value_original <- record[[bib_col_name]]
        if (!is.na(value_original) && length(value_original) > 0) {
            value_char <- as.character(value_original)
            value_trimmed <- trimws(value_char)
            if (value_trimmed != "" && toupper(value_trimmed) != "NA") {
              if (wos_tag %in% semicolon_split_fields) {
                items <- strsplit(value_trimmed, ";", fixed = TRUE)[[1]]
                for (item_raw in items) {
                  item_clean <- trimws(item_raw)
                  if (item_clean != "") {
                    writeLines(paste(wos_tag, item_clean), con)
                  }
                }
              } else if (wos_tag %in% newline_split_repeat_tag_fields) {
                items <- strsplit(value_trimmed, "\n", fixed = TRUE)[[1]]
                for (item_raw in items) {
                  item_clean <- trimws(item_raw)
                  if (item_clean != "") {
                    writeLines(paste(wos_tag, item_clean), con)
                  }
                }
              } else if (wos_tag %in% newline_indent_fields && grepl("\n", value_trimmed, fixed = TRUE)) {
                lines_content <- strsplit(value_trimmed, "\n", fixed = TRUE)[[1]]
                if (length(lines_content) > 0) {
                  first_line_clean <- trimws(lines_content[1])
                  if (first_line_clean != "") { 
                    writeLines(paste(wos_tag, first_line_clean), con)
                  }
                  if (length(lines_content) > 1) {
                    for (k_line in 2:length(lines_content)) {
                      continuation_line_clean <- trimws(lines_content[k_line])
                      if (continuation_line_clean != "") { 
                         writeLines(paste0("   ", continuation_line_clean), con)
                      } else if (k_line == 1 && first_line_clean == "" && length(lines_content) > 1) {
                         writeLines(paste(wos_tag, continuation_line_clean), con)
                      }
                    }
                  }
                }
              } else {
                writeLines(paste(wos_tag, value_trimmed), con)
              }
            }
        }
      }
    }
    writeLines("ER", con)
  }
  
  writeLines("EF", con)
  close(con)
  
  cat("CiteSpace TXT 文件已保存到:", output_filepath, "\n")
  return(invisible(output_filepath))
}

cat("脚本 'export_bibliometrix_to_citespace.R' 已定义。\n")
cat("包含函数: get_wos_tag_for_field(), export_bibliometrix_to_citespace_txt()\n")
cat("您可以使用 export_bibliometrix_to_citespace_txt(your_bibliometrix_df, 'your_output_path.txt') 来调用它。\n")

# 提醒：如果要在当前R会话中使用此文件中的函数，您需要:
# 1. 将此代码保存到您的工作目录下的 'export_bibliometrix_to_citespace.R' 文件中。
# 2. 然后在您的R控制台中执行: source("export_bibliometrix_to_citespace.R")
#    (如果文件在工作目录下，则不需要完整路径) 