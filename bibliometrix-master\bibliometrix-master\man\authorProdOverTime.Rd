% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/authorProdOverTime.R
\name{authorProdOverTime}
\alias{authorProdOverTime}
\title{Top-Authors' Productivity over Time}
\usage{
authorProdOverTime(M, k = 10, graph = TRUE)
}
\arguments{
\item{M}{is a bibliographic data frame obtained by \code{\link{convert2df}} function.}

\item{k}{is a integer. It is the number of top authors to analyze and plot. Default is \code{k = 10}.}

\item{graph}{is logical. If TRUE the function plots the author production over time graph. Default is \code{graph = TRUE}.}
}
\value{
The function \code{authorProdOverTime} returns a list containing two objects:
\tabular{lll}{
\code{dfAU}  \tab   \tab is a data frame\cr
\code{dfpapersAU}\tab    \tab is a data frame\cr
\code{graph}   \tab   \tab a ggplot object}
}
\description{
It calculates and plots the author production (in terms of number of publications) over the time.
}
\examples{
data(scientometrics, package = "bibliometrixData")
res <- authorProdOverTime(scientometrics, k=10)
print(res$dfAU)
plot(res$graph)

}
\seealso{
\code{\link{biblioAnalysis}} function for bibliometric analysis

\code{\link{summary}} method for class '\code{bibliometrix}'
}
