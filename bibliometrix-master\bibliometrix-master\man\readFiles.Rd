% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/readFiles.R
\name{readFiles}
\alias{readFiles}
\title{DEPRECATED: Load a sequence of ISI or SCOPUS Export files into a large character object}
\usage{
readFiles(...)
}
\arguments{
\item{...}{is a sequence of names of files downloaded from WOS.(in plain text or bibtex format) or SCOPUS Export file (exclusively in bibtex format).}
}
\value{
a character vector of length the number of lines read.
}
\description{
The function readFiled is deprecated. You can import and convert your export files directly using the function \code{\link{convert2df}}.
}
\examples{
# WoS or SCOPUS Export files can be read using \code{\link{readFiles}} function:

# largechar <- readFiles('filename1.txt','filename2.txt','filename3.txt')

# filename1.txt, filename2.txt and filename3.txt are ISI or SCOPUS Export file 
# in plain text or bibtex format.

# D <- readFiles('https://www.bibliometrix.org/datasets/bibliometrics_articles.txt')

}
\seealso{
\code{\link{convert2df}} for converting SCOPUS of ISI Export file into a dataframe
}
