% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/plotThematicEvolution.R
\name{plotThematicEvolution}
\alias{plotThematicEvolution}
\title{Plot a Thematic Evolution Analysis}
\usage{
plotThematicEvolution(Nodes, Edges, measure = "inclusion", min.flow = 0)
}
\arguments{
\item{Nodes}{is a list of nodes obtained by \code{\link{thematicEvolution}} function.}

\item{Edges}{is a list of edges obtained by \code{\link{thematicEvolution}} function.}

\item{measure}{is a character. It can be \code{measure=("inclusion","stability", "weighted")}.}

\item{min.flow}{is numerical. It indicates the minimum value of measure to plot a flow.}
}
\value{
a sankeyPlot
}
\description{
It plot a Thematic Evolution Analysis performed using the \code{\link{thematicEvolution}} function.
}
\examples{

\dontrun{
data(managemeent, package = "bibliometrixData")
years=c(2004,2015)

nexus <- thematicEvolution(management,field="ID",years=years,n=100,minFreq=2)

plotThematicEvolution(nexus$Nodes,nexus$Edges)
}

}
\seealso{
\code{\link{thematicMap}} function to create a thematic map based on co-word network analysis and clustering.

\code{\link{thematicMap}} function to perform a thematic evolution analysis.

\code{\link{networkPlot}} to plot a bibliographic network.
}
