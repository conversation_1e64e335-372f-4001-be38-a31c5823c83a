# =================================================================================================
# === 配置文件 ===
# =================================================================================================
# 版本: 1.0.0
# 描述: 包含所有配置参数

# API配置
api_config <- list(
  # OpenAlex API配置
  openalex = list(
    base_url = "https://api.openalex.org",
    email = "<EMAIL>",
    timeout = 30,
    max_retries = 3
  ),
  
  # Crossref API配置
  crossref = list(
    base_url = "https://api.crossref.org",
    email = "<EMAIL>",
    timeout = 30,
    max_retries = 3
  ),
  
  # Scopus API配置
  scopus = list(
    base_url = "https://api.scopus.com",
    api_key = "73e2b669b074369001b2ccc92d681be8",
    timeout = 30,
    max_retries = 3
  )
  
  # 其他API配置（暂时注释）
  # wos = list(...),
  # dimensions = list(...),
  # mag = list(...),
  # semantic_scholar = list(...),
  # google_scholar = list(...),
  # arxiv = list(...),
  # pubmed = list(...)
)

# 数据处理配置
data_processing_config <- list(
  # 数据清理配置
  cleaning = list(
    remove_special_chars = TRUE,
    standardize_separators = TRUE,
    handle_missing_values = TRUE
  ),
  
  # 数据验证配置
  validation = list(
    required_fields = c("AU", "TI", "SO", "PY", "TC", "CR"),
    min_year = 1900,
    max_year = as.numeric(format(Sys.Date(), "%Y"))
  ),
  
  # 数据转换配置
  conversion = list(
    standardize_authors = TRUE,
    standardize_institutions = TRUE,
    standardize_keywords = TRUE
  )
)

# 分析配置
analysis_config <- list(
  # 基本分析配置
  basic = list(
    min_citations = 0,
    min_year = 1900,
    max_year = as.numeric(format(Sys.Date(), "%Y"))
  ),
  
  # 网络分析配置
  network = list(
    min_edge_weight = 1,
    min_node_size = 1,
    layout = "fr"
  ),
  
  # 可视化配置
  visualization = list(
    theme = "theme_minimal",
    color_palette = "viridis",
    output_format = "png"
  )
)

# 输出配置
output_config <- list(
  # 报告配置
  report = list(
    output_dir = "reports",
    include_visualizations = TRUE,
    include_tables = TRUE,
    include_summary = TRUE
  ),
  
  # 日志配置
  logging = list(
    log_file = "logs/app.log",
    log_level = "INFO",
    include_timestamp = TRUE
  ),
  
  # 缓存配置
  cache = list(
    enabled = TRUE,
    cache_dir = "cache",
    max_size = 1000
  )
)

# 导出配置
export_config <- function() {
  list(
    api = api_config,
    data_processing = data_processing_config,
    analysis = analysis_config,
    output = output_config
  )
} 