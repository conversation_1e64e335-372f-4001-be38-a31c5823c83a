# =================================================================================================
# === 主程序 ===
# =================================================================================================
# 版本: 1.0.0
# 描述: 主程序入口，整合所有模块

# 加载必要的包
required_packages <- c(
  "tidyverse",
  "bibliometrix",
  "futile.logger"
)

# 检查并安装缺失的包
missing_packages <- required_packages[!required_packages %in% installed.packages()[,"Package"]]
if (length(missing_packages) > 0) {
  install.packages(missing_packages)
}

# 加载所有必要的包
for (pkg in required_packages) {
  suppressPackageStartupMessages(library(pkg, character.only = TRUE))
}

# 加载自定义模块
source("code/core/data_acquisition.R")
source("code/core/data_processing.R")
source("code/analysis/bibliometric_analysis.R")
source("code/utils/data_cleaning.R")
source("code/utils/error_handling.R")

# 初始化日志记录器
setup_logger("logs/app.log")

# 主函数
main <- function() {
  # 1. 获取数据
  flog.info("开始获取数据...")
  raw_data <- fetch_data_by_identifier(identifiers, source_type)
  
  # 2. 处理数据
  flog.info("开始处理数据...")
  processed_data <- process_data(raw_data, source_type)
  
  # 3. 执行分析
  flog.info("开始执行分析...")
  analysis_results <- perform_basic_analysis(processed_data$processed_data)
  
  # 4. 生成报告
  flog.info("开始生成报告...")
  generate_report(analysis_results)
  
  flog.info("处理完成")
}

# 生成报告
generate_report <- function(results) {
  # 1. 创建报告目录
  dir.create("reports", showWarnings = FALSE)
  
  # 2. 生成基本统计报告
  write_basic_stats(results, "reports/basic_stats.txt")
  
  # 3. 生成可视化报告
  generate_visualizations(results, "reports/visualizations")
  
  # 4. 生成详细分析报告
  write_detailed_analysis(results, "reports/detailed_analysis.txt")
}

# 程序入口
if (interactive()) {
  # 交互模式
  main()
} else {
  # 非交互模式
  args <- commandArgs(trailingOnly = TRUE)
  if (length(args) > 0) {
    # 处理命令行参数
    process_args(args)
  } else {
    # 使用默认参数
    main()
  }
} 