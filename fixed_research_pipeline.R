﻿# research_pipeline.R
# 杩欎釜鑴氭湰灏嗛€愭瀹炵幇鐮旂┒妗嗘灦 

# --- 1. 鏁版嵁鍔犺浇涓庢牸寮忚浆鎹?---

# 1.1 鍔犺浇蹇呰鐨勫簱
# 鍔犺浇 bibliometrix 鐢ㄤ簬鏂囩尞璁￠噺鍒嗘瀽锛宼idyverse 鐢ㄤ簬鏁版嵁澶勭悊鍜屾枃浠舵搷浣?
library(bibliometrix)
library(tidyverse)

# -- 鏂板浠ｇ爜寮€濮?--
# 妫€鏌ュ苟瀹夎 writexl 鍖?(鐢ㄤ簬鍐欏叆 Excel 鏂囦欢)
if (!requireNamespace("writexl", quietly = TRUE)) {
  cat("writexl 鍖呮湭瀹夎锛屾鍦ㄥ皾璇曞畨瑁?..\n")
  install.packages("writexl")
}
library(writexl)
# -- 鏂板浠ｇ爜缁撴潫 --

# 1.2 璁剧疆宸ヤ綔鐩綍鍜屾暟鎹矾寰?
# 璁剧疆褰撳墠宸ヤ綔鐩綍锛屾墍鏈夌浉瀵硅矾寰勯兘灏嗗熀浜庢鐩綍
# 娉ㄦ剰锛氳纭繚璺緞鍒嗛殧绗︽槸 / 鎴栬€?\\
working_dir <- "C:/Users/<USER>/Desktop/article/鏁版嵁澶勭悊閮ㄥ垎" 
setwd(working_dir)
cat("褰撳墠宸ヤ綔鐩綍宸茶缃负:", getwd(), "\n")

# 鎸囧畾鍖呭惈鍘熷 WoS 瀵煎嚭鏂囦欢 (txt鏍煎紡) 鐨勭洰褰?
# 娉ㄦ剰锛氳纭繚瀛樺偍 WoS 鏂囦欢鐨勬枃浠跺す璺緞姝ｇ‘
raw_data_dir <- "C:/Users/<USER>/Desktop/鏁版嵁鏂囦欢/citespace鏁版嵁"

# 妫€鏌ョ洰褰曟槸鍚﹀瓨鍦?
if (!dir.exists(raw_data_dir)) {
  stop("鎸囧畾鐨勫師濮嬫暟鎹洰褰曚笉瀛樺湪: ", raw_data_dir)
} else {
  cat("鎵惧埌鍘熷鏁版嵁鐩綍:", raw_data_dir, "\n")
}

# 1.3 璇诲彇骞惰浆鎹?WoS 鏁版嵁鏂囦欢
# 鑾峰彇鐩綍涓嬫墍鏈?.txt 鏂囦欢鐨勫畬鏁磋矾寰勫垪琛?
wos_files <- list.files(path = raw_data_dir, pattern = "\\.txt$", full.names = TRUE)

if (length(wos_files) == 0) {
  stop("鍦ㄦ寚瀹氱洰褰曚笅娌℃湁鎵惧埌 .txt 鏂囦欢: ", raw_data_dir)
} else {
  cat("鎵惧埌", length(wos_files), "涓?.txt 鏂囦欢锛屽噯澶囪繘琛岃浆鎹?..\n")
  print(basename(wos_files)) # 鎵撳嵃鎵惧埌鐨勬枃浠跺悕锛屾柟渚跨‘璁?
}

# 浣跨敤 bibliometrix::convert2df 璇诲彇鎵€鏈塼xt鏂囦欢骞跺悎骞?
# dbsource = "wos" 鎸囧畾鏁版嵁婧愪负 Web of Science
# format = "plaintext" 鎸囧畾鏂囦欢鏍煎紡涓虹函鏂囨湰 (閫氬父鐨刉oS瀵煎嚭鏍煎紡)
cat("\n寮€濮嬩娇鐢?bibliometrix 杩涜鏍煎紡杞崲...\n")
M <- convert2df(file = wos_files, dbsource = "wos", format = "plaintext")

# 妫€鏌ヨ浆鎹㈢粨鏋?
cat("杞崲瀹屾垚銆?鏁版嵁妗?M 鍖呭惈", nrow(M), "鏉¤褰?鍜?, ncol(M), "涓瓧娈点€俓n")

cat("\n--- 璇婃柇: M 瀵硅薄 --- \n")
cat("缁村害 (Dim_M): ", paste(dim(M), collapse = " x "), "\n")
cat("鍒楀悕 (Colnames_M): ", paste(colnames(M), collapse = ", "), "\n")
cat("鍓嶅嚑琛?AU (Authors_M_Head):\n")
print(head(M$AU))
cat("鍓嶅嚑琛?DE (AuthorKeywords_M_Head):\n")
print(head(M$DE))
cat("鍓嶅嚑琛?ID (KeywordsPlus_M_Head):\n")
print(head(M$ID))
cat("鍓嶅嚑琛?SO (Sources_M_Head):\n")
print(head(M$SO))
cat("闈炵┖ AU 鏁伴噺 (NonEmpty_AU_M): ", sum(!is.na(M$AU) & M$AU != ""), "\n")
cat("闈炵┖ DE 鏁伴噺 (NonEmpty_DE_M): ", sum(!is.na(M$DE) & M$DE != ""), "\n")
cat("闈炵┖ ID 鏁伴噺 (NonEmpty_ID_M): ", sum(!is.na(M$ID) & M$ID != ""), "\n")
cat("闈炵┖ SO 鏁伴噺 (NonEmpty_SO_M): ", sum(!is.na(M$SO) & M$SO != ""), "\n")
cat("--- 璇婃柇: M 瀵硅薄缁撴潫 --- \n\n")

# -- 鏂板浠ｇ爜寮€濮?--
# 灏嗚浆鎹㈠悗鐨勬暟鎹 M 淇濆瓨涓?Excel 鏂囦欢

# --- 澶勭悊瀹屽叏涓?NA 鐨勫垪锛岄槻姝?write_xlsx 鎶ラ敊 --- 
M_for_excel <- M # 鍒涘缓涓€涓壇鏈互閬垮厤淇敼鍘熷 M
all_na_cols <- sapply(M_for_excel, function(x) all(is.na(x)))
if (any(all_na_cols)) {
  cat("妫€娴嬪埌浠ヤ笅鍒楀畬鍏ㄧ敱 NA 缁勬垚锛屽皢鍏惰浆鎹负瀛楃鍨嬩互渚垮啓鍏?Excel:", 
      paste(names(M_for_excel)[all_na_cols], collapse=", "), "\n")
  for (col_name in names(M_for_excel)[all_na_cols]) {
    M_for_excel[[col_name]] <- as.character(M_for_excel[[col_name]])
  }
}
# --- 澶勭悊缁撴潫 ---

# --- 鏂板锛氬鐞嗚秴闀垮瓧绗︿覆锛岄槻姝?write_xlsx 鎶ラ敊 ---
excel_char_limit <- 32700 # Excel 闄愬埗澶х害 32767锛岀暀涓€鐐逛綑鍦?
truncated_cols <- character(0) # 璁板綍鍝簺鍒楄鎴柇浜?

# 閬嶅巻鎵€鏈夊瓧绗︾被鍨嬬殑鍒?
char_cols <- names(M_for_excel)[sapply(M_for_excel, is.character)]
for (col_name in char_cols) {
  # 璁＄畻姣忚鏂囨湰鐨勯暱搴?(na.rm=TRUE 蹇界暐NA)
  char_lengths <- nchar(M_for_excel[[col_name]], keepNA = FALSE)
  # 妫€鏌ユ槸鍚︽湁瓒呴暱鐨?
  if (any(char_lengths > excel_char_limit, na.rm = TRUE)) {
    truncated_cols <- c(truncated_cols, col_name)
    # 瀵硅秴闀跨殑瀛楃涓茶繘琛屾埅鏂?
    M_for_excel[[col_name]] <- ifelse(
      !is.na(M_for_excel[[col_name]]) & char_lengths > excel_char_limit,
      paste0(substr(M_for_excel[[col_name]], 1, excel_char_limit), "... [鎴柇]"),
      M_for_excel[[col_name]]
    )
  }
}
if (length(truncated_cols) > 0) {
  cat("璀﹀憡锛氭娴嬪埌浠ヤ笅鍒椾腑瀛樺湪瓒呴暱鏂囨湰 (瓒呰繃", excel_char_limit, "瀛楃)锛屽凡鎴柇浠ヤ究鍐欏叆 Excel:", 
      paste(unique(truncated_cols), collapse=", "), "\n")
}
# --- 澶勭悊缁撴潫 ---

output_xlsx_file <- file.path(working_dir, "WoS_converted_data.xlsx")
tryCatch({
  # 浣跨敤澶勭悊杩囩殑 M_for_excel 鍐欏叆
  write_xlsx(M_for_excel, path = output_xlsx_file)
  cat("宸插皢杞崲鍚庣殑鏁版嵁妗嗗悓鏃朵繚瀛樹负 Excel 鏂囦欢鍒?", output_xlsx_file, "\n")
}, error = function(e) {
  cat("閿欒锛氭棤娉曞皢鏁版嵁妗嗕繚瀛樹负 Excel 鏂囦欢銆傝妫€鏌?R 鏄惁鏈夊啓鍏ユ潈闄愪互鍙?'writexl' 鍖呮槸鍚︽甯稿伐浣溿€俓n閿欒淇℃伅:", conditionMessage(e), "\n")
})
# -- 鏂板浠ｇ爜缁撴潫 --

# 1.4 淇濆瓨杞崲鍚庣殑鏁版嵁妗?
# 灏嗚浆鎹㈠悗鐨勬暟鎹 M 淇濆瓨涓?RData 鏂囦欢锛屼互渚垮悗缁揩閫熷姞杞?
output_rdata_file <- file.path(working_dir, "WoS_converted_data.RData")
save(M, file = output_rdata_file)

cat("宸插皢杞崲鍚庣殑鏁版嵁妗嗕繚瀛樺埌:", output_rdata_file, "\n")

# 娓呯悊鍐呭瓨 (鍙€?
# rm(wos_files, raw_data_dir) 
# gc()

cat("\n--- 绗竴姝ワ細鏁版嵁鍔犺浇涓庢牸寮忚浆鎹㈠畬鎴?---\n")


# --- 2. 鏁版嵁鎺㈢储涓庡垵姝ョ己澶卞垎鏋?---

# 2.1 鍔犺浇涔嬪墠淇濆瓨鐨勬暟鎹?
# 纭繚宸ヤ綔鐩綍璁剧疆姝ｇ‘ (濡傛灉鑴氭湰鏄垎娈垫墽琛?
# setwd(working_dir) # 濡傛灉闇€瑕侀噸鏂拌缃?
load("WoS_converted_data.RData")
cat("\n宸插姞杞芥暟鎹 M銆俓n")

# 2.2 鏄剧ず鏁版嵁妗嗗熀鏈俊鎭?
cat("\n--- 鏁版嵁妗?M 鍩烘湰淇℃伅 ---\n")
cat("缁村害 (璁板綍鏁?x 瀛楁鏁?:", dim(M)[1], "x", dim(M)[2], "\n")

# 2.3 瀹氫箟 WoS 瀛楁涓枃鍚箟鏄犲皠
# 瀹屾暣鏄犲皠琛?(鎸佺画鏇存柊)
field_mapping <- data.frame(
  Tag = c("DT", "AU", "AF", "TI", "SO", "LA", "DE", "ID", "AB", "C1", "RP", "CR", "TC", 
          "PY", "SC", "UT", "DI", "WC", "J9", "JI", "PU", "PI", "PA", "SN", "BN", 
          "FU", "NR", "VL", "IS", "BP", "EP", "PG", "DA", "EM", "OI", "RI", "PM", 
          "OA", "HC", "HP", "Z9", "U1", "U2", "U3", "U4", "U5", "U6", "D2", "EA", 
          "EY", "DB", "AU_CO", "AU_UN", "AU1_CO", "AU1_UN", "SR", "LCS", "GCS", "MCP", 
          "SCP", "TI_TM", "AB_TM", "DE_TM", "ID_TM", "CO_CA", "N_GRANT",
          # -- WoS 鏍囧噯瀛楁 & bibliometrix 琛嶇敓/甯歌瀛楁 --
          "AR", "BA", "BE", "BF", "C3", "CA", "CL", "CT", "CY", # 宸叉湁琛ュ厖
          "EF", "EI", "ER", "FX", "GA", "HO", "PD", "PN", "PT", "SE", "SI", "SP", "SU", # 宸叉湁琛ュ厖
          "SR_FULL", # 宸叉湁琛ュ厖
          "WE", "C1raw", "AU_UN_NR" # 鏂板鏈槧灏勫瓧娈?
          ),
  Meaning = c("鏂囨。绫诲瀷", "浣滆€?, "浣滆€呭叏鍚?, "鏍囬", "鍑虹増鐗╂潵婧?, "璇█", "浣滆€呭叧閿瘝", 
              "鍏抽敭璇峆lus", "鎽樿", "浣滆€呭湴鍧€", "閫氳浣滆€呭湴鍧€", "寮曠敤鍙傝€冩枃鐚?, "琚紩棰戞", 
              "鍑虹増骞翠唤", "WoS瀛︾绫诲埆", "鍞竴鏍囪瘑绗?WoS)", "DOI", "Web of Science绫诲埆", 
              "鏈熷垔缂╁啓(J9)", "ISO鏈熷垔缂╁啓(JI)", "鍑虹増鍟?, "鍑虹増鍟嗗煄甯?, "鍑虹増鍟嗗湴鍧€", 
              "ISSN", "ISBN", "璧勫姪鏈烘瀯涓庣紪鍙?, "鍙傝€冩枃鐚暟閲?, "鍗峰彿", "鏈熷彿", "璧峰椤电爜", 
              "缁撴潫椤电爜", "椤垫暟", "鍑虹増鏃ユ湡(鏁版嵁搴撹褰?", "鐢靛瓙閭欢鍦板潃", "ORCID鏍囪瘑绗?, "ResearcherID", 
              "PubMed ID", "寮€鏀捐幏鍙栫姸鎬?, "楂樿寮曡鏂?, "鐑偣璁烘枃", "鎬诲紩鐢ㄦ鏁?WoS)", 
              "杩囧幓180澶╃殑浣跨敤璁℃暟", "鑷?013骞翠互鏉ョ殑浣跨敤璁℃暟", "鐢ㄦ埛瀹氫箟瀛楁3", "鐢ㄦ埛瀹氫箟瀛楁4", # U1, U2 鏇存柊
              "鐢ㄦ埛瀹氫箟瀛楁5", "鐢ㄦ埛瀹氫箟瀛楁6", "鐢靛瓙鍑虹増鏃ユ湡", "鎻愬墠璁块棶鏃ユ湡", 
              "鎻愬墠璁块棶骞翠唤", "鏁版嵁鏉ユ簮", "浣滆€呭浗瀹?, "浣滆€呮満鏋?, "绗竴浣滆€呭浗瀹?, 
              "绗竴浣滆€呮満鏋?, "绠€鐭紩鐢ㄦ牸寮?, "灞€閮ㄥ紩鐢ㄥ緱鍒?, "鍏ㄥ眬寮曠敤寰楀垎", "澶氬浗鍑虹増鐗?, 
              "鍗曞浗鍑虹増鐗?, "鏍囬鏈鐭╅樀", "鎽樿鏈鐭╅樀", "鍏抽敭璇嶆湳璇煩闃?, 
              "KeywordsPlus鏈鐭╅樀", "閫氳浣滆€呭浗瀹?, "璧勫姪椤圭洰鏁?,
              # -- WoS 鏍囧噯瀛楁 & bibliometrix 琛嶇敓/甯歌瀛楁 --
              "鏂囩珷缂栧彿", "鍥句功浣滆€?, "缂栬€?, "鍥句功浣滆€呭叏鍚?, "浼氳鏍囬(C3)", 
              "浼氳璧炲姪鑰?CA)", "浼氳鍦扮偣", "浼氳鏍囬(CT)", "浼氳鏃ユ湡", # 宸叉湁琛ュ厖
              "鏂囦欢缁撴潫绗?, "鐢靛瓙ISSN", "璁板綍缁撴潫绗?, "璧勫姪鏂囨湰", "鍥綋浣滆€?, "浼氳涓诲姙鏂?, 
              "鍑虹増鏃ユ湡(鏈?鏃?", "閮ㄥ垎鍙?, "鍑虹増鐗╃被鍨?, "涓涗功鏍囬", "鐗瑰垔/澧炲垔鏍囪瘑", "浼氳璧炲姪鑰?SP)", 
              "澧炲垔", # 宸叉湁琛ュ厖
              "瀹屾暣寮曟枃鏍煎紡", # 宸叉湁琛ュ厖
              "WoS 鐗堟湰", "鍘熷浣滆€呭湴鍧€", "浣滆€呮満鏋勬暟閲? # 鏂板鏈槧灏勫瓧娈靛惈涔?
              )
)

# 鑾峰彇 M 鐨勫疄闄呭垪鍚?
actual_colnames <- colnames(M)

# -- 绉婚櫎涓存椂浠ｇ爜寮€濮?--
# # 鎵惧嚭鍦?actual_colnames 涓絾涓嶅湪 field_mapping$Tag 涓殑瀛楁
# unmapped_tags <- setdiff(actual_colnames, field_mapping$Tag)
# if (length(unmapped_tags) > 0) {
#   cat("\n--- 浠ヤ笅瀛楁瀛樺湪浜庢暟鎹 M 涓紝浣嗘湭鍦?field_mapping 涓畾涔?---\n")
#   print(unmapped_tags)
# } else {
#   cat("\n--- 鎵€鏈?M 鏁版嵁妗嗕腑鐨勫瓧娈靛潎宸插湪 field_mapping 涓畾涔?---\n")
# }
# -- 绉婚櫎涓存椂浠ｇ爜缁撴潫 --

# 鍒涘缓鍖呭惈瀹為檯鍒楀悕鍜屼腑鏂囧惈涔夌殑鏁版嵁妗?
colnames_df <- data.frame(Tag = actual_colnames) %>%
  left_join(field_mapping, by = "Tag") %>%
  # 瀵逛簬鍦ㄦ槧灏勮〃涓壘涓嶅埌鐨勫瓧娈碉紝淇濈暀鍘?Tag锛孧eaning 璁句负鎻愮ず淇℃伅
  mutate(Meaning = ifelse(is.na(Meaning) & Tag %in% actual_colnames, 
                        paste0("-- (", Tag, ") 鏈湪棰勫畾涔夋槧灏勪腑 --"), 
                        Meaning))

cat("\n--- 鏁版嵁妗?M 瀛楁鍒楄〃 (Tag 涓?涓枃鍚箟) ---\n")
# 浣跨敤 print 鎺у埗杈撳嚭鏍煎紡锛岄伩鍏嶈繃瀹?
print(colnames_df, row.names = FALSE, right = FALSE)


# 2.4 鎵ц缂哄け鍒嗘瀽
# 鍔犺浇 naniar 鍖?(鐢ㄤ簬缂哄け鍊煎垎鏋?
# 鑴氭湰寮€澶村凡鍔犺浇 tidyverse锛岄€氬父鍖呭惈 dplyr 绛変緷璧?
if (!requireNamespace("naniar", quietly = TRUE)) {
  cat("naniar 鍖呮湭瀹夎锛屾鍦ㄥ皾璇曞畨瑁?..\n")
  install.packages("naniar")
}
library(naniar)

cat("\n--- 瀛楁缂哄け鍊煎垎鏋?(鎸夌己澶辨瘮渚嬮檷搴忔帓鍒? ---\n")
# 璁＄畻缂哄け鎽樿
missing_summary <- miss_var_summary(M) %>%
  arrange(desc(pct_miss)) %>% # 鎸夌己澶辨瘮渚嬮檷搴忔帓鍒?
  # 鍚堝苟涓枃鍚箟 (浣跨敤宸插垱寤虹殑 colnames_df)
  left_join(select(colnames_df, Tag, Meaning), by = c("variable" = "Tag")) %>%
  select(Tag = variable, Meaning, n_miss, pct_miss) # 閫夋嫨骞堕噸鎺掑簭鍒?

# -- 鏂板浠ｇ爜锛氱‘璁?missing_summary 鐨勫疄闄呰鏁?--
cat("\n纭锛氳绠楀緱鍒扮殑 missing_summary 瀵硅薄鍖呭惈", nrow(missing_summary), "琛?(瀛楁)銆俓n\n")
# -- 鏂板浠ｇ爜缁撴潫 --

# 鎵撳嵃瀹屾暣鐨勭己澶辨€荤粨
print(missing_summary, row.names = FALSE, right = FALSE, n = Inf)

cat("\n--- 绗簩姝ワ細鏁版嵁鎺㈢储涓庡垵姝ョ己澶卞垎鏋愬畬鎴?---\n")

# 娓呯悊鍐呭瓨 (鍙€?
# rm(missing_summary, colnames_df, actual_colnames, field_mapping)
# gc() 

# --- 3. 鎻忚堪鎬х粺璁″垎鏋?---

# 3.1 鎵ц鍩烘湰鐨勬枃鐚閲忓垎鏋?
# 浣跨敤 bibliometrix 鍖呯殑鏍稿績鍑芥暟 biblioAnalysis 璁＄畻鎻忚堪鎬х粺璁￠噺
# 杩欎釜鍑芥暟浼氳繑鍥炰竴涓寘鍚涓垎鏋愮粨鏋滅殑瀵硅薄
cat("\n--- 寮€濮嬫墽琛屽熀鏈殑鏂囩尞璁￠噺鍒嗘瀽 (biblioAnalysis) ---\n")
results <- biblioAnalysis(M)
cat("鍩烘湰鐨勬枃鐚閲忓垎鏋愯绠楀畬鎴愩€俓n")

cat("\n--- 璇婃柇: results 瀵硅薄 --- \n")
cat("results 瀵硅薄缁撴瀯 (Structure_results):\n")
str(results)
if ("Authors" %in% names(results)) {
    cat("results$Authors 鍓嶅嚑琛?(Authors_results_Head):\n")
    print(head(results$Authors))
} else {
    cat("results$Authors 涓嶅瓨鍦?(Authors_results_missing)\n")
}
if ("Sources" %in% names(results)) {
    cat("results$Sources 鍓嶅嚑琛?(Sources_results_Head):\n")
    print(head(results$Sources))
} else {
    cat("results$Sources 涓嶅瓨鍦?(Sources_results_missing)\n")
}
cat("--- 璇婃柇: results 瀵硅薄缁撴潫 --- \n\n")

# 3.2 鏌ョ湅鍒嗘瀽缁撴灉鎽樿
# 浣跨敤 summary() 鍑芥暟鍙互鏌ョ湅 biblioAnalysis 璁＄畻鍑虹殑涓昏鎸囨爣姒傝
# 'k' 鍙傛暟鎸囧畾浜嗗湪鍒楄〃涓樉绀哄灏戜釜鏈€楂橀鐨勯」鐩?(渚嬪锛屾帓鍚嶅墠10鐨勪綔鑰?
cat("\n--- 鏂囩尞璁￠噺鍒嗘瀽缁撴灉鎽樿 (Top 10) ---\n")
options(width = 150) # 灏濊瘯澧炲姞鎺у埗鍙拌緭鍑哄搴︿互渚挎煡鐪嬫憳瑕?
summary_results <- summary(results, k = 10) 
# print(summary_results) # summary() 浼氳嚜鍔ㄦ墦鍗帮紝閫氬父涓嶉渶瑕佹樉寮?print

cat("\n--- 璇婃柇: summary_results 瀵硅薄 --- \n")
cat("summary_results 瀵硅薄缁撴瀯 (Structure_summary_results):\n")
str(summary_results)
cat("summary_results$Authors 鍐呭 (Content_summary_Authors):\n")
print(summary_results$Authors)
cat("summary_results$AuthorsFrac 鍐呭 (Content_summary_AuthorsFrac):
")
print(summary_results$AuthorsFrac)
cat("summary_results$DE (Author Keywords) 鍐呭 (Content_summary_DE):
")
print(summary_results$DE)
cat("summary_results$ID (Keywords Plus) 鍐呭 (Content_summary_ID):
")
print(summary_results$ID)
cat("summary_results$Sources 鍐呭 (Content_summary_Sources):
")
print(summary_results$Sources)
cat("--- 璇婃柇: summary_results 瀵硅薄缁撴潫 --- \n\n")

options(width = 80) # 鎭㈠榛樿瀹藉害 (鍙€?


# (鍙€? 璁块棶鏇磋缁嗙殑缁撴灉锛屼緥濡傛煡鐪嬫渶楂樹骇浣滆€呯殑瀹屾暣鍒楄〃
# print(summary_results$Authors) # 鎵撳嵃鎵€鏈変綔鑰呯殑缁熻鏁版嵁
# print(summary_results$Sources) # 鎵撳嵃鎵€鏈夋潵婧愭湡鍒婄殑缁熻鏁版嵁

# 3.3 (鏂板) 淇濆瓨鎻忚堪鎬х粺璁¤〃鏍?
# 鍒涘缓杈撳嚭瀛愮洰褰?(濡傛灉涓嶅瓨鍦?
output_basedir <- file.path(working_dir, "baseline_analysis_output")
if (!dir.exists(output_basedir)) {
  dir.create(output_basedir, recursive = TRUE)
  cat("鍒涘缓杈撳嚭鐩綍:", output_basedir, "\n")
}

# 妫€鏌?summary_results 鍚勪釜鍏冪礌鏄惁瀛樺湪涓斾笉涓虹┖锛岀劧鍚庝繚瀛?
# Main Information
if (!is.null(summary_results$MainInformationDF)) { # MainInformationDF 鏄?summary 瀵硅薄鐨勬纭厓绱犲悕
  write.csv(summary_results$MainInformationDF, 
            file.path(output_basedir, "desc_main_info_from_summary.csv"), 
            row.names = TRUE) # 閫氬父MainInformation鐨勮鍚嶆湁鎰忎箟
} else {
  cat("璀﹀憡: summary_results$MainInformationDF 鏈壘鍒版垨涓虹┖锛屼笉淇濆瓨 desc_main_info_from_summary.csv\n")
}


# Top Authors
if (!is.null(summary_results$MostProdAuthors) && nrow(summary_results$MostProdAuthors) > 0) {
  top_authors_df <- summary_results$MostProdAuthors[, 1:2]
  # colnames(top_authors_df) <- c("Author", "Articles") # 鏍规嵁瀹為檯闇€瑕佽皟鏁村垪鍚?
  write.csv(top_authors_df, file.path(output_basedir, "desc_top_authors.csv"), row.names = FALSE)
} else {
  cat("璀﹀憡: summary_results$MostProdAuthors 涓虹┖鎴栦笉瀛樺湪, desc_top_authors.csv 灏嗕负绌恒€俓n")
  write.csv(data.frame(), file.path(output_basedir, "desc_top_authors.csv"), row.names = FALSE)
}

# Top Authors Fractionalized
if (!is.null(summary_results$MostProdAuthors) && ncol(summary_results$MostProdAuthors) >= 4 && nrow(summary_results$MostProdAuthors) > 0) {
  top_authors_frac_df <- summary_results$MostProdAuthors[, 3:4]
  # colnames(top_authors_frac_df) <- c("Author_Frac", "Articles_Frac") # 鏍规嵁瀹為檯闇€瑕佽皟鏁村垪鍚?
  write.csv(top_authors_frac_df, file.path(output_basedir, "desc_top_authors_frac.csv"), row.names = FALSE)
} else {
  cat("璀﹀憡: summary_results$MostProdAuthors (鍒嗘暟鍖栭儴鍒? 涓虹┖銆佷笉瀛樺湪鎴栧垪鏁颁笉瓒? desc_top_authors_frac.csv 灏嗕负绌恒€俓n")
  write.csv(data.frame(), file.path(output_basedir, "desc_top_authors_frac.csv"), row.names = FALSE)
}

# Top Author Keywords (DE)
if (!is.null(summary_results$MostRelKeywords) && ncol(summary_results$MostRelKeywords) >= 2 && nrow(summary_results$MostRelKeywords) > 0) {
  top_de_df <- summary_results$MostRelKeywords[, 1:2]
  # colnames(top_de_df) <- c("Author_Keyword_DE", "Articles") # 鏍规嵁瀹為檯闇€瑕佽皟鏁村垪鍚?
  write.csv(top_de_df, file.path(output_basedir, "desc_top_author_keywords.csv"), row.names = FALSE)
} else {
  cat("璀﹀憡: summary_results$MostRelKeywords (DE閮ㄥ垎) 涓虹┖銆佷笉瀛樺湪鎴栧垪鏁颁笉瓒? desc_top_author_keywords.csv 灏嗕负绌恒€俓n")
  write.csv(data.frame(), file.path(output_basedir, "desc_top_author_keywords.csv"), row.names = FALSE)
}

# Top Keywords Plus (ID)
if (!is.null(summary_results$MostRelKeywords) && ncol(summary_results$MostRelKeywords) >= 4 && nrow(summary_results$MostRelKeywords) > 0) {
  top_id_df <- summary_results$MostRelKeywords[, 3:4]
  # colnames(top_id_df) <- c("Keyword_Plus_ID", "Articles") # 鏍规嵁瀹為檯闇€瑕佽皟鏁村垪鍚?
  write.csv(top_id_df, file.path(output_basedir, "desc_top_keywords_plus.csv"), row.names = FALSE)
} else {
  cat("璀﹀憡: summary_results$MostRelKeywords (ID閮ㄥ垎) 涓虹┖銆佷笉瀛樺湪鎴栧垪鏁颁笉瓒? desc_top_keywords_plus.csv 灏嗕负绌恒€俓n")
  write.csv(data.frame(), file.path(output_basedir, "desc_top_keywords_plus.csv"), row.names = FALSE)
}

# Top Sources
if (!is.null(summary_results$MostRelSources) && nrow(summary_results$MostRelSources) > 0) {
  write.csv(summary_results$MostRelSources, file.path(output_basedir, "desc_top_sources.csv"), row.names = FALSE)
} else {
  cat("璀﹀憡: summary_results$MostRelSources 涓虹┖鎴栦笉瀛樺湪, desc_top_sources.csv 灏嗕负绌恒€俓n")
  write.csv(data.frame(), file.path(output_basedir, "desc_top_sources.csv"), row.names = FALSE)
}


cat("\n--- 鎻忚堪鎬х粺璁¤〃鏍间繚瀛樺皾璇曞畬鎴?---\n")
# 鍚庣画鍙互鏍规嵁 summary_results 鐨勭粨鏋勬坊鍔犳洿澶氳〃鏍肩殑淇濆瓨
# 渚嬪 Most Cited Countries, Most Cited Documents, etc.

# --- 4. 鐭ヨ瘑缁撴瀯鍒嗘瀽 ---

# 4.1 姒傚康缁撴瀯 (Co-occurrence, Co-word analysis)
# 鍒嗘瀽鍏抽敭璇嶅叡鐜扮綉缁滐紝渚嬪浣跨敤浣滆€呭叧閿瘝 (DE) 鎴栧叧閿瘝Plus (ID)

# 4.1.1 浣滆€呭叧閿瘝鍏辩幇缃戠粶 (DE)
cat("\n--- 寮€濮嬭绠椾綔鑰呭叧閿瘝 (DE) 鍏辩幇缃戠粶 ---\n")
# NetMatrix <- biblioNetwork(M, analysis = "co-occurrences", network = "author_keywords", sep = ";")
# 妫€鏌?M$DE 鏄惁瀛樺湪涓斿寘鍚湁鏁堟暟鎹?
if (!is.null(M$DE) && sum(!is.na(M$DE) & M$DE != "") > 0) {
  
  cat("Preprocessing M$DE for biblioNetwork...\n")
  # 绠€鍗曠殑棰勫鐞嗭細纭繚鏄瓧绗﹀瀷锛屾浛鎹?NA 涓虹┖瀛楃涓诧紝鍘婚櫎棣栧熬绌烘牸
  M_DE_processed <- as.character(M$DE)
  M_DE_processed[is.na(M_DE_processed)] <- ""
  M_DE_processed <- trimws(M_DE_processed)
  
  # 妫€鏌ュ垎鍙风殑浣跨敤锛氱粺璁℃瘡涓褰曚腑鍏抽敭璇嶆暟閲?
  cat("Unique DE terms (first 20 after splitting and unlisting):\n")
  print(head(unique(trimws(unlist(strsplit(M_DE_processed, ";")))), 20))
  empty_DE_terms <- sum(trimws(unlist(strsplit(M_DE_processed, ";"))) == "")
  cat("Number of empty DE terms after splitting:", empty_DE_terms, "\n")

  # 鍒涘缓涓€涓复鏃剁殑鏁版嵁妗嗙敤浜?biblioNetwork锛屽彧鍖呭惈澶勭悊杩囩殑 DE 鍜屽繀瑕佺殑鍏朵粬鍒?(濡?PY, UT/DI)
  # biblioNetwork 鍙兘鍙渶瑕佹枃鐚湰韬紝浣嗕互闃蹭竾涓€锛屾垜浠敤鍘熷 M 鐨勭粨鏋?
  M_temp_DE <- M
  M_temp_DE$DE <- M_DE_processed

  cat("Attempting: NetMatrix_DE <- biblioNetwork(M_temp_DE, analysis = \"co-occurrence\", network = \"author_keywords\", sep = \";\")\n")
  NetMatrix_DE <- NULL # Initialize
  
  tryCatch({
    cat("Attempting direct biblioNetwork for DE first...\n")
    NetMatrix_DE <- biblioNetwork(M_temp_DE, analysis = "co-occurrence", network = "author_keywords", sep = ";")
    cat("biblioNetwork for DE completed successfully (direct attempt).\n")
  }, error = function(e_bn) {
    cat("ERROR during direct biblioNetwork for DE: ", conditionMessage(e_bn), "\n")
    cat("Now attempting cocMatrix for DE as an alternative...\n")
    tryCatch({
      NetMatrix_DE <<- cocMatrix(M_temp_DE, Field = "DE", sep = ";")
      cat("cocMatrix for DE completed successfully.\n")
      # --- 娣诲姞瀵?cocMatrix 杩斿洖鍊肩殑鍗虫椂妫€鏌?--- 
      if (is.null(NetMatrix_DE)){
          cat("Immediate check after cocMatrix for DE: Result is NULL.\n")
      } else {
          cat("Immediate check after cocMatrix for DE: Result is NOT NULL. Class: ", paste(class(NetMatrix_DE), collapse=", "), "\n")
          cat("Immediate check after cocMatrix for DE: Dimensions: ", ifelse(is.null(dim(NetMatrix_DE)), "NULL/Not matrix", paste(dim(NetMatrix_DE), collapse=" x ")), "\n")
      }
      # --- 妫€鏌ョ粨鏉?--- 
    }, error = function(e_coc) {
      cat("ERROR during cocMatrix for DE: ", conditionMessage(e_coc), "\n")
      NetMatrix_DE <<- NULL # Ensure it's NULL if both attempts fail
    })
  })
  
  if (!is.null(NetMatrix_DE)){
    cat("Class of NetMatrix_DE (after attempts): ", paste(class(NetMatrix_DE), collapse=", "), "\n")
    cat("Dimensions of NetMatrix_DE (rows x cols) (after attempts): ", ifelse(is.null(dim(NetMatrix_DE)), "NULL or Not a matrix/array", paste(dim(NetMatrix_DE), collapse = " x ")), "\n")
    if (is.matrix(NetMatrix_DE) || is.data.frame(NetMatrix_DE)) {
      if (!is.null(colnames(NetMatrix_DE))) {
        cat("First few colnames of NetMatrix_DE (after attempts): ", paste(head(colnames(NetMatrix_DE), 6), collapse=", "), "\n")
      } else {
        cat("NetMatrix_DE (after attempts) has NULL colnames.\n")
      }
    }
  } else {
    cat("NetMatrix_DE is NULL after all attempts.\n")
  }
  
  # 鍙€夛細淇濆瓨缃戠粶鐭╅樀
  # 娉ㄦ剰锛氬綋鍓?cocMatrix 鍚庡鏂规鐢熸垚鐨勬槸鏂囨。-璇嶈鐭╅樀 (DTM)锛岃€岄潪璇嶈鍏辩幇缃戠粶銆?
  if (!is.null(NetMatrix_DE) && inherits(NetMatrix_DE, "Matrix") && nrow(NetMatrix_DE) > 0 && ncol(NetMatrix_DE) > 0) {
    # --- 淇敼涓?saveRDS --- 
    saveRDS(NetMatrix_DE, file.path(output_basedir, "net_cooccurrence_author_keywords_DTM.rds")) # 鍚庣紑娣诲姞 DTM 鎻愮ず
    cat("浣滆€呭叧閿瘝鐨勬枃妗?璇嶈鐭╅樀 (DTM, 鏉ヨ嚜 cocMatrix 鍚庡) 宸蹭繚瀛樹负 RDS 鍒?net_cooccurrence_author_keywords_DTM.rds\n")
    # --- 淇敼缁撴潫 --- 
  } else {
    cat("璀﹀憡: 鏈兘鎴愬姛鐢熸垚鎴栭獙璇佷綔鑰呭叧閿瘝鐩稿叧鐭╅樀 (NetMatrix_DE)锛屾垨鐭╅樀涓虹┖/涓嶆纭€俠iblioNetwork 鐩存帴璋冪敤澶辫触锛宑ocMatrix 鍚庡鍙兘鏈骇鐢熼鏈熺被鍨嬬殑鍏辩幇缃戠粶銆俓n")
  }
} else {
  cat("璀﹀憡: M$DE (浣滆€呭叧閿瘝) 涓虹┖鎴栦笉鍖呭惈瓒冲鏁版嵁锛岃烦杩囦綔鑰呭叧閿瘝鍏辩幇缃戠粶鍒嗘瀽銆俓n")
}


# 4.1.2 Keywords Plus 鍏辩幇缃戠粶 (ID)
cat("\n--- 寮€濮嬭绠?Keywords Plus (ID) 鍏辩幇缃戠粶 ---\n")
if (!is.null(M$ID) && sum(!is.na(M$ID) & M$ID != "") > 0) {

  cat("Preprocessing M$ID for biblioNetwork...\n")
  M_ID_processed <- as.character(M$ID)
  M_ID_processed[is.na(M_ID_processed)] <- ""
  M_ID_processed <- trimws(M_ID_processed)

  # --- 鍚敤 ID 鍏抽敭璇嶆鏌?--- 
  cat("Unique ID terms (first 20 after splitting and unlisting):\n")
  print(head(unique(trimws(unlist(strsplit(M_ID_processed, ";")))), 20))
  empty_ID_terms <- sum(trimws(unlist(strsplit(M_ID_processed, ";"))) == "")
  cat("Number of empty ID terms after splitting:", empty_ID_terms, "\n")
  # --- 妫€鏌ョ粨鏉?--- 

  M_temp_ID <- M
  M_temp_ID$ID <- M_ID_processed

  cat("Attempting: NetMatrix_ID <- biblioNetwork(M_temp_ID, analysis = \"co-occurrence\", network = \"keywords_plus\", sep = \";\")\n")
  NetMatrix_ID <- NULL # Initialize

  tryCatch({
    cat("Attempting direct biblioNetwork for ID first...\n")
    NetMatrix_ID <- biblioNetwork(M_temp_ID, analysis = "co-occurrence", network = "keywords_plus", sep = ";")
    cat("biblioNetwork for ID completed successfully (direct attempt).\n")
  }, error = function(e_bn_id) {
    cat("ERROR during direct biblioNetwork for ID: ", conditionMessage(e_bn_id), "\n")
    cat("Now attempting cocMatrix for ID as an alternative...\n")
    tryCatch({
      NetMatrix_ID <<- cocMatrix(M_temp_ID, Field = "ID", sep = ";")
      cat("cocMatrix for ID completed successfully.\n")
      # --- 娣诲姞瀵?cocMatrix 杩斿洖鍊肩殑鍗虫椂妫€鏌?--- 
      if (is.null(NetMatrix_ID)){
          cat("Immediate check after cocMatrix for ID: Result is NULL.\n")
      } else {
          cat("Immediate check after cocMatrix for ID: Result is NOT NULL. Class: ", paste(class(NetMatrix_ID), collapse=", "), "\n")
          cat("Immediate check after cocMatrix for ID: Dimensions: ", ifelse(is.null(dim(NetMatrix_ID)), "NULL/Not matrix", paste(dim(NetMatrix_ID), collapse=" x ")), "\n")
      }
      # --- 妫€鏌ョ粨鏉?--- 
    }, error = function(e_coc_id) {
      cat("ERROR during cocMatrix for ID: ", conditionMessage(e_coc_id), "\n")
      NetMatrix_ID <<- NULL # Ensure it's NULL if both attempts fail
    })
  })

  if (!is.null(NetMatrix_ID)){
    cat("Class of NetMatrix_ID (after attempts): ", paste(class(NetMatrix_ID), collapse=", "), "\n")
    cat("Dimensions of NetMatrix_ID (rows x cols) (after attempts): ", ifelse(is.null(dim(NetMatrix_ID)), "NULL or Not a matrix/array", paste(dim(NetMatrix_ID), collapse = " x ")), "\n")
    if (is.matrix(NetMatrix_ID) || is.data.frame(NetMatrix_ID)) {
      if (!is.null(colnames(NetMatrix_ID))) {
        cat("First few colnames of NetMatrix_ID (after attempts): ", paste(head(colnames(NetMatrix_ID), 6), collapse=", "), "\n")
      } else {
        cat("NetMatrix_ID (after attempts) has NULL colnames.\n")
      }
    }
  } else {
    cat("NetMatrix_ID is NULL after all attempts.\n")
  }
  
  # 娉ㄦ剰锛氬綋鍓?cocMatrix 鍚庡鏂规鐢熸垚鐨勬槸鏂囨。-璇嶈鐭╅樀 (DTM)锛岃€岄潪璇嶈鍏辩幇缃戠粶銆?
  if (exists("NetMatrix_ID") && !is.null(NetMatrix_ID) && inherits(NetMatrix_ID, "Matrix") && ncol(NetMatrix_ID) > 0 && nrow(NetMatrix_ID) > 0) {
    # --- 淇敼涓?saveRDS --- 
    saveRDS(NetMatrix_ID, file.path(output_basedir, "net_cooccurrence_keywords_plus_DTM.rds")) # 鍚庣紑娣诲姞 DTM 鎻愮ず
    cat("Keywords Plus 鐨勬枃妗?璇嶈鐭╅樀 (DTM, 鏉ヨ嚜 cocMatrix 鍚庡) 宸蹭繚瀛樹负 RDS 鍒?net_cooccurrence_keywords_plus_DTM.rds\n")
    # --- 淇敼缁撴潫 --- 
  } else {
    cat("璀﹀憡: 鏈兘鎴愬姛鐢熸垚鎴栭獙璇?Keywords Plus 鐩稿叧鐭╅樀 (NetMatrix_ID)锛屾垨鐭╅樀涓虹┖/涓嶆纭€俠iblioNetwork 鐩存帴璋冪敤澶辫触锛宑ocMatrix 鍚庡鍙兘鏈骇鐢熼鏈熺被鍨嬬殑鍏辩幇缃戠粶銆俓n")
  }
} else {
  cat("璀﹀憡: M$ID (Keywords Plus) 涓虹┖鎴栦笉鍖呭惈瓒冲鏁版嵁锛岃烦杩?Keywords Plus 鍏辩幇缃戠粶鍒嗘瀽銆俓n")
}


# 4.1.3 涓婚鍥?(Thematic Map) - 鍩轰簬 Keywords Plus (ID)
cat("\n--- 寮€濮嬬敓鎴愪富棰樺浘 (Thematic Map - ID) ---\n")
if (exists("NetMatrix_ID") && !is.null(NetMatrix_ID) && ncol(NetMatrix_ID) > 0) {
  # 浣跨敤涔嬪墠鐢熸垚鐨?Keywords Plus 鍏辩幇缃戠粶
  # minWords: 褰㈡垚鑱氱被鐨勬渶灏忚瘝棰?(鍙互璋冩暣)
  # nWords: 鍥句腑鏄剧ず鐨勮瘝鏁伴噺 (鍙互璋冩暣)
  # clustering: 鑱氱被绠楁硶 (louvain, walktrap, infomap, etc.)
  # 娉ㄦ剰锛歍hematicMap 鍙兘闇€瑕佽緝澶氳皟鏁存墠鑳藉緱鍒扮悊鎯崇粨鏋?
  # ThematicMap_ID <- thematicMap(net = NetMatrix_ID, field = "keywords", minfreq = 5, n.labels = 5, repel = TRUE)
  # 绠€鍖栬皟鐢紝浣跨敤 thematicMap 鐩存帴浠?M 鍜?ID 瀛楁鐢熸垚
  # M 鏄枃鐚暟鎹锛宖ield 鏄鍒嗘瀽鐨勫瓧娈碉紝n 鏄瘡涓薄闄愭爣绛炬暟锛宮infreq 鏄瘝鐨勬渶灏忛鐜?
  # ThematicMap_obj <- thematicMap(M, field = "ID", n = 4, minfreq = 5, stemming = FALSE, size = 0.7, repel = TRUE)
  
  # 灏濊瘯浣跨敤鏇寸ǔ鍋ョ殑鏂瑰紡锛屽厛鍒涘缓缃戠粶锛屽啀鍋氫富棰樺浘
  # 纭繚 NetMatrix_ID 鏄竴涓湁鏁堢殑缃戠粶瀵硅薄
  # thematicMap 閫氬父鏈熸湜涓€涓?biblioNetwork 鐨勭粨鏋滐紝鎴栬€呯洿鎺ヤ粠 M 鍒涘缓
  
  # 灏濊瘯鐩存帴浠?M 鍒涘缓 thematicMap锛屽鏋淣etMatrix_ID鏈夐棶棰?
  if (!is.null(M$ID) && sum(!is.na(M$ID) & M$ID != "") > 5) { # 鑷冲皯鏈夊嚑涓狪D
      # 灏?thematic map 鐨勭粨鏋滀繚瀛樺埌 RDS 鏂囦欢锛屽洜涓虹粯鍥惧璞″彲鑳藉鏉?
      thematic_map_id_rds_path <- file.path(output_basedir, "thematic_map_id.rds")
      plot_thematic_map_id_path <- file.path(output_basedir, "plot_thematic_map_id.png")

      tryCatch({
          # 棣栧厛瀵规暟鎹繘琛岄澶勭悊锛屾竻闄ゅ彲鑳界殑闂
          cat("寮€濮嬪皾璇曟墽琛屼富棰樺浘鍒嗘瀽锛堥澶勭悊鐗堟湰锛?..\n")
          
          # 鍒涘缓涓€涓彧鍖呭惈蹇呰瀛楁鐨勪复鏃舵暟鎹
          M_clean <- M[, c("DI", "PY", "SO", "AU", "TI")]
          
          # 娣诲姞绛涢€夎繃鐨勫叧閿瘝瀛楁
          if (!is.null(M$ID) && sum(!is.na(M$ID) & M$ID != "") > 10) {
              M_clean$ID <- M$ID
              field_to_use <- "ID"
          } else if (!is.null(M$DE) && sum(!is.na(M$DE) & M$DE != "") > 10) {
              M_clean$DE <- M$DE
              field_to_use <- "DE" 
          } else {
              stop("ID鍜孌E瀛楁鏁版嵁鍧囦笉瓒充互杩涜涓婚鍥惧垎鏋?)
          }
          
          # 纭繚鏂囩尞璁￠噺蹇呴渶鐨勫叾浠栧瓧娈靛瓨鍦?
          M_clean$AU_CO <- if (!is.null(M$AU_CO)) M$AU_CO else "UNKNOWN"
          
          # 鐢ㄦ渶绠€鍗曠殑鍙傛暟璁剧疆璋冪敤thematicMap
          Map_simple <- thematicMap(
              M_clean,
              field = field_to_use,
              n = 2,           # 姣忎釜涓婚鏄剧ず鐨勫叧閿瘝鏁伴噺
              minfreq = 15,    # 鍏抽敭璇嶆渶灏忛鐜?
              size = 0.5,      # 姘旀场澶у皬
              repel = TRUE,    # 鏍囩涓嶉噸鍙?
              remove.terms = NULL,
              stemming = FALSE,
              n.labels = 1,    # 闄嶄綆鏍囩鏁伴噺
              cluster = "walktrap", # 鏇寸ǔ瀹氱殑绀惧尯妫€娴嬬畻娉?
              community.repulsion = 0.1 # 鍑忓皬绀惧尯闂存帓鏂ュ姏
          )
          
          if (!is.null(Map_simple) && !is.null(Map_simple$map)) {
              # 淇濆瓨缁樺浘瀵硅薄
              plot_thematic_map_simple_path <- file.path(output_basedir, "plot_thematic_map_simple.png")
              ggsave(plot_thematic_map_simple_path, plot = Map_simple$map, width = 10, height = 8, dpi = 300)
              cat("绠€鍖栦富棰樺浘宸蹭繚瀛樹负鍥惧儚鍒?", plot_thematic_map_simple_path, "\n")
              
              # 淇濆瓨涓婚鍥惧璞?
              simple_map_rds_path <- file.path(output_basedir, "thematic_map_simple.rds")
              saveRDS(Map_simple, file = simple_map_rds_path)
              cat("绠€鍖栦富棰樺浘瀵硅薄宸蹭繚瀛樺埌:", simple_map_rds_path, "\n")
          } else {
              cat("璀﹀憡: 绠€鍖栫増thematicMap鏈兘鎴愬姛鐢熸垚缁樺浘瀵硅薄銆俓n")
          }
      }, error = function(e) {
          cat("閿欒: 鐢熸垚绠€鍖栦富棰樺浘鏃跺彂鐢熼敊璇? ", conditionMessage(e), "\n")
          cat("灏濊瘯浣跨敤瀹屽叏涓嶅悓鐨勫垎鏋愭柟娉曚綔涓烘浛浠?..\n")
          
          # 瀹屽叏涓嶅悓鐨勬浛浠ｆ柟妗堬細浣跨敤 conceptualStructure 鏇夸唬 thematicMap
          tryCatch({
              cat("灏濊瘯浣跨敤 conceptualStructure 浣滀负鏇夸唬鍒嗘瀽鏂规硶...\n")
              
              # 鍒涘缓涓€涓洿绮剧畝鐨勪复鏃舵暟鎹
              M_minimal <- M[1:min(1000, nrow(M)), ] # 闄愬埗琛屾暟浠ユ彁楂樻€ц兘
              
              # 瀵笵E鍜孖D杩涜棰勫鐞嗭紝绉婚櫎NA鍜岀┖瀛楃涓?
              if (sum(!is.na(M_minimal$DE) & M_minimal$DE != "") > 30) {
                  field_for_cs <- "DE"
                  cat("浣跨敤浣滆€呭叧閿瘝(DE)鎵ц姒傚康缁撴瀯鍒嗘瀽...\n")
              } else if (sum(!is.na(M_minimal$ID) & M_minimal$ID != "") > 30) {
                  field_for_cs <- "ID"
                  cat("浣跨敤鍏抽敭璇峆lus(ID)鎵ц姒傚康缁撴瀯鍒嗘瀽...\n")
              } else {
                  field_for_cs <- "TI"
                  cat("鍏抽敭璇嶆暟鎹笉瓒筹紝浣跨敤鏍囬(TI)鎵ц姒傚康缁撴瀯鍒嗘瀽...\n")
              }
              
              # 浣跨敤鏈€绠€鍗曞拰鏈€绋冲仴鐨勮缃?
              CS <- conceptualStructure(
                  M_minimal,
                  field = field_for_cs,
                  method = "MCA",     # 澶氶噸瀵瑰簲鍒嗘瀽锛屾瘮PCA鏇撮€傚悎鏂囨湰鏁版嵁
                  minDegree = 5,      # 鍑忓皯鍏抽敭璇嶆暟閲忥紝鎻愰珮绋冲畾鎬?
                  clust = 5,          # 绨囩殑鏁伴噺
                  stemming = FALSE,   # 涓嶄娇鐢ㄨ瘝骞叉彁鍙栵紝閬垮厤澧炲姞澶嶆潅鎬?
                  labelsize = 10,     # 鏍囩澶у皬
                  documents = 50      # 闄愬埗鏂囨。鏁伴噺锛屾彁楂樻€ц兘
              )
              
              # 淇濆瓨缁撴灉
              conceptual_structure_path <- file.path(output_basedir, "conceptual_structure.png")
              if (exists("CS") && !is.null(CS)) {
                  # 瀵逛簬conceptualStructure锛孋S鏈韩灏辨槸涓€涓猤gplot瀵硅薄鍒楄〃
                  if (is.list(CS) && length(CS) > 0 && inherits(CS[[1]], "ggplot")) {
                      for (i in 1:length(CS)) {
                          if (inherits(CS[[i]], "ggplot")) {
                              conceptual_plot_path <- file.path(output_basedir, paste0("conceptual_structure_plot_", i, ".png"))
                              ggsave(conceptual_plot_path, plot = CS[[i]], width = 10, height = 8, dpi = 300)
                              cat("姒傚康缁撴瀯鍥?, i, "宸蹭繚瀛樺埌:", conceptual_plot_path, "\n")
                          }
                      }
                      
                      # 淇濆瓨瀹屾暣CS瀵硅薄浠ヤ緵杩涗竴姝ュ垎鏋?
                      saveRDS(CS, file.path(output_basedir, "conceptual_structure.rds"))
                      cat("瀹屾暣姒傚康缁撴瀯瀵硅薄宸蹭繚瀛樺埌:", file.path(output_basedir, "conceptual_structure.rds"), "\n")
                  } else {
                      cat("璀﹀憡: conceptualStructure杩斿洖鐨勫璞＄粨鏋勪笉鏄鏈熺殑ggplot鍒楄〃銆俓n")
                  }
              } else {
                  cat("璀﹀憡: conceptualStructure鏈兘鐢熸垚鏈夋晥杈撳嚭銆俓n")
              }
          }, error = function(e_cs) {
              cat("閿欒: 鎵цconceptualStructure鏃跺彂鐢熼敊璇? ", conditionMessage(e_cs), "\n")
              cat("灏嗚烦杩囦富棰樺浘鍜屾蹇电粨鏋勫垎鏋愶紝缁х画鎵ц鍏朵粬鍒嗘瀽...\n")
              
              # 鏈€鍚庣殑澶囬€夋柟妗堬細鎵ц绠€鍗曠殑璇嶉鍒嗘瀽
              tryCatch({
                  cat("姝ｅ湪鎵ц绠€鍗曠殑璇嶉鍒嗘瀽浣滀负鏈€缁堝閫夋柟妗?..\n")
                  
                  # 鍑嗗璇嶉鍒嗘瀽鍑芥暟
                  analyze_term_freq <- function(column_data, field_name, min_freq = 5) {
                      if (is.null(column_data) || all(is.na(column_data))) {
                          cat(paste0("鏃犳硶鎵ц", field_name, "鐨勮瘝棰戝垎鏋愶細鏁版嵁涓虹┖鎴栧叏涓篘A\n"))
                          return(NULL)
                      }
                      
                      # 灏嗘暟鎹媶鍒嗘垚鍗曚釜璇嶆眹
                      all_terms <- unlist(strsplit(column_data[!is.na(column_data)], ";"))
                      all_terms <- trimws(all_terms)  # 鍘婚櫎鍓嶅悗绌烘牸
                      
                      # 缁熻璇嶉
                      term_freq <- table(all_terms)
                      term_freq_df <- as.data.frame(term_freq)
                      names(term_freq_df) <- c("Term", "Frequency")
                      
                      # 鎸夐鐜囨帓搴忓苟绛涢€?
                      term_freq_df <- term_freq_df[order(term_freq_df$Frequency, decreasing = TRUE), ]
                      term_freq_df <- term_freq_df[term_freq_df$Frequency >= min_freq, ]
                      
                      # 杈撳嚭缁撴灉
                      term_freq_path <- file.path(output_basedir, paste0("term_frequency_", tolower(field_name), ".csv"))
                      write.csv(term_freq_df, term_freq_path, row.names = FALSE)
                      cat(paste0(field_name, "璇嶉鍒嗘瀽缁撴灉宸蹭繚瀛樺埌: ", term_freq_path, "\n"))
                      
                      # 濡傛灉鏈夎冻澶熺殑璇嶆眹锛屽垱寤虹畝鍗曠殑鏉″舰鍥?
                      if (nrow(term_freq_df) > 0) {
                          top_n <- min(30, nrow(term_freq_df))
                          top_terms <- term_freq_df[1:top_n, ]
                          
                          # 鍒涘缓鏉″舰鍥?
                          p <- ggplot(top_terms, aes(x = reorder(Term, Frequency), y = Frequency)) +
                              geom_bar(stat = "identity", fill = "steelblue") +
                              coord_flip() +
                              theme_minimal() +
                              labs(title = paste0(field_name, "璇嶉鍒嗘瀽 (Top ", top_n, ")"),
                                   x = "鏈", y = "棰戠巼") +
                              theme(axis.text.y = element_text(size = 8))
                          
                          # 淇濆瓨鍥捐〃
                          plot_path <- file.path(output_basedir, paste0("term_frequency_", tolower(field_name), "_plot.png"))
                          ggsave(plot_path, plot = p, width = 10, height = 8, dpi = 300)
                          cat(paste0(field_name, "璇嶉鍒嗘瀽鍥惧凡淇濆瓨鍒? ", plot_path, "\n"))
                      }
                      
                      return(term_freq_df)
                  }
                  
                  # 鍒嗗埆瀵笵E鍜孖D鎵ц璇嶉鍒嗘瀽
                  de_freq <- analyze_term_freq(M$DE, "浣滆€呭叧閿瘝(DE)", min_freq = 5)
                  id_freq <- analyze_term_freq(M$ID, "鍏抽敭璇峆lus(ID)", min_freq = 5)
                  
                  # 濡傛灉鏈夊繀瑕侊紝涔熷彲浠ュ垎鏋愭爣棰?
                  if ((is.null(de_freq) || nrow(de_freq) < 10) && (is.null(id_freq) || nrow(id_freq) < 10)) {
                      cat("鍏抽敭璇嶅垎鏋愮粨鏋滆緝灏戯紝灏濊瘯鍒嗘瀽鏍囬...\n")
                      title_freq <- analyze_term_freq(M$TI, "鏍囬(TI)", min_freq = 3)
                  }
                  
                  cat("鍩烘湰璇嶉鍒嗘瀽瀹屾垚銆俓n")
              }, error = function(e_freq) {
                  cat("閿欒: 鎵ц鍩烘湰璇嶉鍒嗘瀽鏃跺彂鐢熼敊璇? ", conditionMessage(e_freq), "\n")
                  cat("鎵€鏈夊閫夋柟妗堝潎宸插け璐ワ紝缁х画鎵ц涓嬩竴姝ュ垎鏋愩€俓n")
              })
  # MCA 鍒嗘瀽鍏抽敭璇嶇殑鍏宠仈缁撴瀯
  # field: 瑕佸垎鏋愮殑瀛楁 ("ID", "DE", "TI", "AB")
  # ngrams: 鏄惁鑰冭檻 n-gram (1=鍗曚釜璇? 2=鍙岃瘝缁勫悎绛?
  # minWordFreq: 璇嶇殑鏈€灏忛鐜?
  # k.max: 鑱氱被鐨勬渶澶ф暟閲?(鐢ㄤ簬纭畾鏈€浣宠仛绫绘暟)
  # stemming: 鏄惁杩涜璇嶅共鎻愬彇
  
  # 灏嗙粨鏋滀繚瀛樺埌 RDS 鏂囦欢锛屽洜涓篗CA缁撴灉鍙兘寰堝ぇ涓斿鏉?
  mca_id_rds_path <- file.path(output_basedir, "conceptual_structure_mca_id.rds")
  
  tryCatch({
      CS_ID <- conceptualStructure(M, field="ID", method="MCA", 
                                   stemming=FALSE, labels = 10,
                                   ngrams = 1,
                                   documents = 10 # 鍥句腑鏄剧ず鐨勬枃鐚暟閲?
                                  )
      # conceptualStructure 杩斿洖鐨勫璞″彲浠ョ洿鎺?plot
      # plot(CS_ID) 
      
      # 淇濆瓨 CS_ID 瀵硅薄
      saveRDS(CS_ID, file = mca_id_rds_path)
      cat("姒傚康缁撴瀯 (MCA - ID) 瀵硅薄宸蹭繚瀛樺埌:", mca_id_rds_path, "\n")
      
      # 鍙互灏濊瘯鐢熸垚骞朵繚瀛樼粯鍥撅紝浣?MCA 鐨勫浘鍙兘闇€瑕佷氦浜掑紡璋冩暣
      # plot_mca_id_path <- file.path(output_basedir, "plot_conceptual_structure_mca_id.png")
      # png(plot_mca_id_path, width=1200, height=1000, res=150)
      # tryCatch({
      #   plot(CS_ID) # 榛樿浼氫骇鐢熶袱涓浘
      # }, finally = {
      #   dev.off()
      # })
      # cat("姒傚康缁撴瀯 (MCA - ID) 灏濊瘯淇濆瓨缁樺浘鍒?", plot_mca_id_path, "\n")
      
  }, error = function(e) {
      cat("閿欒: 杩涜姒傚康缁撴瀯鍒嗘瀽 (MCA - ID) 鏃跺彂鐢熼敊璇? ", conditionMessage(e), "\n")
  })
} else {
  cat("璀﹀憡: M$ID (Keywords Plus) 鏁版嵁涓嶈冻浠ヨ繘琛屾蹇电粨鏋?(MCA) 鍒嗘瀽銆俓n")
}

# 濡傛灉涓婅堪浠ｇ爜浠嶇劧澶辫触锛屾彁渚涗竴涓畬鍏ㄤ笉鍚岀殑鏇夸唬鏂规
# 娣诲姞涓€涓柊鐨勫嚱鏁拌皟鐢紝浣跨敤 conceptualStructure 浣滀负鏇夸唬鏂规
cat("\n--- 灏濊瘯鎵ц姒傚康鍥犲瓙鍒嗘瀽 (浠ｆ浛涓婚鍥? ---\n")
tryCatch({
    # conceptualStructure 鏄?bibliometrix 涓殑鍙︿竴绉嶆蹇靛垎鏋愭柟娉?
    # 瀹冧娇鐢ㄥ洜瀛愬垎鏋愯€屼笉鏄綉缁滆仛绫伙紝鍙兘瀵?NA 鍊兼洿鍔犲仴澹?
    CS_factorial <- conceptualStructure(M, 
                                       field = "ID", 
                                       method = "CA",  # 浣跨敤瀵瑰簲鍒嗘瀽鑰屼笉鏄?MCA
                                       minDegree = 10, # 鏈€灏忓叡鐜伴鐜?
                                       clust = 5,      # 鑱氱被鏁伴噺
                                       stemming = FALSE,
                                       k.max = 8,      # 鏈€澶ц€冭檻鐨勮仛绫绘暟
                                       labelsize = 10)
    
    # 淇濆瓨缁撴灉 
    factorial_rds_path <- file.path(output_basedir, "factorial_analysis_ID.rds")
    saveRDS(CS_factorial, file = factorial_rds_path)
    cat("姒傚康鍥犲瓙鍒嗘瀽缁撴灉宸蹭繚瀛樺埌:", factorial_rds_path, "\n")
    
}, error = function(e) {
    cat("灏濊瘯鎵ц姒傚康鍥犲瓙鍒嗘瀽鏃朵篃鍙戠敓閿欒:", conditionMessage(e), "\n")
    
    # 濡傛灉 ID 瀛楁鐨勫垎鏋愬け璐ワ紝灏濊瘯 DE 瀛楁
    cat("灏濊瘯浣跨敤 DE 瀛楁杩涜姒傚康鍥犲瓙鍒嗘瀽...\n")
    tryCatch({
        CS_factorial_DE <- conceptualStructure(M, 
                                             field = "DE", 
                                             method = "CA",
                                             minDegree = 10,
                                             clust = 5,
                                             stemming = FALSE,
                                             k.max = 8,
                                             labelsize = 10)
        
        # 淇濆瓨缁撴灉
        factorial_de_rds_path <- file.path(output_basedir, "factorial_analysis_DE.rds")
        saveRDS(CS_factorial_DE, file = factorial_de_rds_path)
        cat("浣跨敤 DE 瀛楁鐨勬蹇靛洜瀛愬垎鏋愮粨鏋滃凡淇濆瓨鍒?", factorial_de_rds_path, "\n")
    }, error = function(e2) {
        cat("浣跨敤 DE 瀛楁杩涜姒傚康鍥犲瓙鍒嗘瀽涔熷け璐ヤ簡:", conditionMessage(e2), "\n")
    })
})

# 4.2 鏅哄姏缁撴瀯 (Co-citation, Bibliographic coupling)

# 4.2.1 鏂囩尞鍏辫寮曠綉缁?(References co-citation)
cat("\n--- 寮€濮嬭绠楁枃鐚叡琚紩缃戠粶 ---\n")
# 妫€鏌?M$CR 鏄惁瀛樺湪涓斿寘鍚湁鏁堟暟鎹?
if (!is.null(M$CR) && sum(!is.na(M$CR) & M$CR != "") > 0) {
  NetMatrix_CR <- biblioNetwork(M, analysis = "co-citation", network = "references", sep = ";")
  
  if (exists("NetMatrix_CR") && !is.null(NetMatrix_CR) && ncol(NetMatrix_CR) > 0) {
    # 鍏辫寮曠綉缁滃彲鑳介潪甯稿ぇ锛岄€氬父涓嶇洿鎺ヤ繚瀛樹负CSV锛岃€屾槸鐢ㄤ簬鍚庣画鍒嗘瀽鎴栫粯鍥?
    # networkPlot(NetMatrix_CR, n = 20, Title = "Co-citation Network - References", type = "fruchterman", size=T, remove.multiple=F, labelsize=0.7,label.n=20,label.cex=F)
    
    # 淇濆瓨缃戠粶瀵硅薄鏈韩鍒?RDS锛屼互渚垮悗缁娇鐢?VOSviewer 鎴?Gephi
    saveRDS(NetMatrix_CR, file = file.path(output_basedir, "net_cocitation_references.rds"))
    cat("鏂囩尞鍏辫寮曠綉缁滃璞″凡淇濆瓨鍒?net_cocitation_references.rds\n")
  } else {
    cat("璀﹀憡: 鏈兘鎴愬姛鐢熸垚鏂囩尞鍏辫寮曠綉缁滅煩闃?(NetMatrix_CR)锛屾垨鐭╅樀涓虹┖銆俓n")
  }
} else {
  cat("璀﹀憡: M$CR (Cited References) 涓虹┖鎴栦笉鍖呭惈瓒冲鏁版嵁锛岃烦杩囨枃鐚叡琚紩缃戠粶鍒嗘瀽銆俓n")
}


# 4.2.2 浣滆€呮枃鐚€﹀悎缃戠粶 (Authors bibliographic coupling)
cat("\n--- 寮€濮嬭绠椾綔鑰呮枃鐚€﹀悎缃戠粶 ---\n")
# 妫€鏌?M$AU 鍜?M$CR 鏄惁瀛樺湪
if (!is.null(M$AU) && sum(!is.na(M$AU) & M$AU != "") > 0 && 
    !is.null(M$CR) && sum(!is.na(M$CR) & M$CR != "") > 0) {
  NetMatrix_Coupling_AU <- biblioNetwork(M, analysis = "coupling", network = "authors", sep = ";")
  
  if (exists("NetMatrix_Coupling_AU") && !is.null(NetMatrix_Coupling_AU) && ncol(NetMatrix_Coupling_AU) > 0) {
    # --- 淇敼涓?saveRDS --- 
    saveRDS(NetMatrix_Coupling_AU, file.path(output_basedir, "net_coupling_authors.rds"))
    cat("浣滆€呮枃鐚€﹀悎缃戠粶鐭╅樀宸蹭繚瀛樹负 RDS 鍒?net_coupling_authors.rds\n")
    # --- 淇敼缁撴潫 --- 
    # net_coupling_au <- networkPlot(NetMatrix_Coupling_AU, n = 20, Title = "Bibliographic Coupling - Authors", type = "fruchterman", size=T,remove.multiple=F,labelsize=0.7,label.n=20,label.cex=F)
  } else {
    cat("璀﹀憡: 鏈兘鎴愬姛鐢熸垚浣滆€呮枃鐚€﹀悎缃戠粶鐭╅樀 (NetMatrix_Coupling_AU)锛屾垨鐭╅樀涓虹┖銆俓n")
  }
} else {
  cat("璀﹀憡: M$AU (Authors) 鎴?M$CR (Cited References) 鏁版嵁涓嶈冻锛岃烦杩囦綔鑰呮枃鐚€﹀悎鍒嗘瀽銆俓n")
}

# 4.2.3 鏈熷垔鏂囩尞鑰﹀悎缃戠粶 (Sources bibliographic coupling)
cat("\n--- 寮€濮嬭绠楁湡鍒婃枃鐚€﹀悎缃戠粶 ---\n")
# 妫€鏌?M$SO 鍜?M$CR 鏄惁瀛樺湪
if (!is.null(M$SO) && sum(!is.na(M$SO) & M$SO != "") > 0 &&
    !is.null(M$CR) && sum(!is.na(M$CR) & M$CR != "") > 0) {
  NetMatrix_Coupling_SO <- biblioNetwork(M, analysis = "coupling", network = "sources", sep = ";")
  
  if (exists("NetMatrix_Coupling_SO") && !is.null(NetMatrix_Coupling_SO) && ncol(NetMatrix_Coupling_SO) > 0) {
    # --- 淇敼涓?saveRDS --- 
    saveRDS(NetMatrix_Coupling_SO, file.path(output_basedir, "net_coupling_sources.rds"))
    cat("鏈熷垔鏂囩尞鑰﹀悎缃戠粶鐭╅樀宸蹭繚瀛樹负 RDS 鍒?net_coupling_sources.rds\n")
    # --- 淇敼缁撴潫 --- 
    # net_coupling_so <- networkPlot(NetMatrix_Coupling_SO, n = 20, Title = "Bibliographic Coupling - Sources", type = "fruchterman", size=T,remove.multiple=F,labelsize=0.7,label.n=20,label.cex=F)
  } else {
    cat("璀﹀憡: 鏈兘鎴愬姛鐢熸垚鏈熷垔鏂囩尞鑰﹀悎缃戠粶鐭╅樀 (NetMatrix_Coupling_SO)锛屾垨鐭╅樀涓虹┖銆俓n")
  }
} else {
  cat("璀﹀憡: M$SO (Sources) 鎴?M$CR (Cited References) 鏁版嵁涓嶈冻锛岃烦杩囨湡鍒婃枃鐚€﹀悎鍒嗘瀽銆俓n")
}


# 4.3 绀句細缁撴瀯 (Collaboration analysis)

# 4.3.1 浣滆€呭悎浣滅綉缁?(Author collaboration)
cat("\n--- 寮€濮嬭绠椾綔鑰呭悎浣滅綉缁?---\n")
# 妫€鏌?M$AU 鏄惁瀛樺湪
if (!is.null(M$AU) && sum(!is.na(M$AU) & M$AU != "") > 0) {
  NetMatrix_Collab_AU <- biblioNetwork(M, analysis = "collaboration", network = "authors", sep = ";")
  
  if (exists("NetMatrix_Collab_AU") && !is.null(NetMatrix_Collab_AU) && ncol(NetMatrix_Collab_AU) > 0) {
    # --- 淇敼涓?saveRDS --- 
    saveRDS(NetMatrix_Collab_AU, file.path(output_basedir, "net_author_collaboration.rds"))
    cat("浣滆€呭悎浣滅綉缁滅煩闃靛凡淇濆瓨涓?RDS 鍒?net_author_collaboration.rds\n")
    # --- 淇敼缁撴潫 --- 
    # net_collab_au <- networkPlot(NetMatrix_Collab_AU, n = 50, Title = "Collaboration Network - Authors", type = "fruchterman", size=T,remove.multiple=F,labelsize=0.7,label.n=30,label.cex=F)
  } else {
    cat("璀﹀憡: 鏈兘鎴愬姛鐢熸垚浣滆€呭悎浣滅綉缁滅煩闃?(NetMatrix_Collab_AU)锛屾垨鐭╅樀涓虹┖銆俓n")
  }
} else {
  cat("璀﹀憡: M$AU (Authors) 鏁版嵁涓嶈冻锛岃烦杩囦綔鑰呭悎浣滅綉缁滃垎鏋愩€俓n")
}


# 4.3.2 鏈烘瀯鍚堜綔缃戠粶 (Institution collaboration)
# 娉ㄦ剰锛氭満鏋勫悕绉伴渶瑕佹竻娲楀拰鏍囧噯鍖栨墠鑳借幏寰楁湁鎰忎箟鐨勭粨鏋?
# bibliometrix 榛樿浣跨敤 C1 瀛楁锛堜綔鑰呭湴鍧€锛変腑鐨勬満鏋勪俊鎭?
cat("\n--- 寮€濮嬭绠楁満鏋勫悎浣滅綉缁?---\n")
# 妫€鏌?M$C1 鏄惁瀛樺湪 (鎴栬€?M$AU_UN 濡傛灉宸茬粡澶勭悊杩囨満鏋勪俊鎭?
# 鍋囪浣跨敤 M$C1锛宐ibliometrix 浼氬皾璇曚粠涓彁鍙栨満鏋?
if (!is.null(M$C1) && sum(!is.na(M$C1) & M$C1 != "") > 0) {
  # 鏈烘瀯鍚堜綔鍒嗘瀽閫氬父闇€瑕佹洿绮剧粏鐨勬満鏋勫悕绉版彁鍙栧拰鍚堝苟锛岃繖閲屼娇鐢?bibliometrix 鐨勯粯璁よ涓?
  # 鍙互鑰冭檻 metaTagExtraction(M, Field = "AU_UN", sep = ";") 鏉ラ澶勭悊鏈烘瀯
  NetMatrix_Collab_Inst <- biblioNetwork(M, analysis = "collaboration", network = "institutions", sep = ";")
  
  if (exists("NetMatrix_Collab_Inst") && !is.null(NetMatrix_Collab_Inst) && ncol(NetMatrix_Collab_Inst) > 0) {
    # --- 淇敼涓?saveRDS --- 
    saveRDS(NetMatrix_Collab_Inst, file.path(output_basedir, "net_institution_collaboration.rds"))
    cat("鏈烘瀯鍚堜綔缃戠粶鐭╅樀宸蹭繚瀛樹负 RDS 鍒?net_institution_collaboration.rds\n")
    # --- 淇敼缁撴潫 --- 
    # net_collab_inst <- networkPlot(NetMatrix_Collab_Inst, n = 20, Title = "Collaboration Network - Institutions", type = "fruchterman", size=T,remove.multiple=F,labelsize=0.7,label.n=20,label.cex=F)
  } else {
    cat("璀﹀憡: 鏈兘鎴愬姛鐢熸垚鏈烘瀯鍚堜綔缃戠粶鐭╅樀 (NetMatrix_Collab_Inst)锛屾垨鐭╅樀涓虹┖銆俓n")
  }
} else {
  cat("璀﹀憡: M$C1 (Author Affiliations) 鏁版嵁涓嶈冻鎴栫己澶憋紝璺宠繃鏈烘瀯鍚堜綔缃戠粶鍒嗘瀽銆俓n")
}

# 4.3.3 鍥藉鍚堜綔缃戠粶 (Country collaboration)
# 浣跨敤 SCP (Single Country Publications) 鍜?MCP (Multiple Country Publications) 鎸囨爣
# 涔熷彲浠ラ€氳繃 networkPlot(NetMatrix_Country) 杩涜鍙鍖?
cat("\n--- 寮€濮嬭绠楀浗瀹跺悎浣滅綉缁?---\n")
# 鍥藉鍚堜綔鍒嗘瀽閫氬父鍩轰簬绗竴浣滆€呭浗瀹?(AU1_CO) 鎴栨墍鏈変綔鑰呭浗瀹?(AU_CO)
# 闇€瑕佺‘淇濊繖浜涘瓧娈靛湪 M 涓瓨鍦ㄤ笖宸查€氳繃 metaTagExtraction 姝ｇ‘濉厖
# 渚嬪锛歁 <- metaTagExtraction(M, Field = "AU_CO", sep = ";")

# 妫€鏌?M$AU_CO (浣滆€呭浗瀹讹紝闇€瑕侀澶勭悊) 鎴?results$CountryCollaboration (濡傛灉 biblioAnalysis 鐢熸垚浜?
# 杩欓噷鎴戜滑鍋囪 results 瀵硅薄涓寘鍚簡鍥藉鍚堜綔鐨勪俊鎭?(閫氬父鏄湁鐨?
if (!is.null(results$CountryCollaboration) && nrow(results$CountryCollaboration$NetMatrix) > 0) {
  NetMatrix_Collab_Country <- results$CountryCollaboration$NetMatrix
  # 濡傛灉闇€瑕丼CP/MCP锛岄€氬父鍦?summary_results 鎴?results 鐨勫叾浠栭儴鍒?
  
  # --- 淇敼涓?saveRDS --- 
  saveRDS(NetMatrix_Collab_Country, file.path(output_basedir, "net_country_collaboration.rds"))
  cat("鍥藉鍚堜綔缃戠粶鐭╅樀宸蹭繚瀛樹负 RDS 鍒?net_country_collaboration.rds\n")
  # --- 淇敼缁撴潫 --- 
} else if (!is.null(M$AU_CO) && sum(!is.na(M$AU_CO) & M$AU_CO != "") > 0) {
  # 濡傛灉 results 涓病鏈夛紝灏濊瘯浠?M$AU_CO 鐢熸垚 (闇€瑕?M$AU_CO 鏄纭牸寮?
  cat("灏濊瘯浠?M$AU_CO 鐢熸垚鍥藉鍚堜綔缃戠粶...\n")
  tryCatch({
    NetMatrix_Collab_Country_from_M <- biblioNetwork(M, analysis = "collaboration", network = "countries", sep = ";", field = "AU_CO")
    if (exists("NetMatrix_Collab_Country_from_M") && !is.null(NetMatrix_Collab_Country_from_M) && ncol(NetMatrix_Collab_Country_from_M) > 0) {
       # --- 淇敼涓?saveRDS --- 
       saveRDS(NetMatrix_Collab_Country_from_M, file.path(output_basedir, "net_country_collaboration.rds"))
       cat("鍥藉鍚堜綔缃戠粶鐭╅樀 (浠?M$AU_CO 鐢熸垚) 宸蹭繚瀛樹负 RDS 鍒?net_country_collaboration.rds\n")
       # --- 淇敼缁撴潫 --- 
    } else {
       cat("璀﹀憡: 浠?M$AU_CO 鐢熸垚鍥藉鍚堜綔缃戠粶澶辫触鎴栫煩闃典负绌恒€俓n")
    }
  }, error = function(e) {
    cat("閿欒: 浠?M$AU_CO 鐢熸垚鍥藉鍚堜綔缃戠粶鏃跺彂鐢熼敊璇? ", conditionMessage(e), "\n")
  })
} else {
   cat("璀﹀憡: results$CountryCollaboration 鍜?M$AU_CO 鍧囦笉瓒充互杩涜鍥藉鍚堜綔缃戠粶鍒嗘瀽銆俓n")
}


# --- 5. 鍘嗗彶鐩存帴寮曟枃鍒嗘瀽 (Historiograph) ---
cat("\n--- 寮€濮嬭繘琛屽巻鍙茬洿鎺ュ紩鏂囧垎鏋?(Historiograph) ---\n")
# 闇€瑕?M 涓寘鍚?PY (鍑虹増骞翠唤), CR (寮曟枃鍒楄〃), 鍜?UT/DI (鍞竴鏍囪瘑绗?
# localCitationNetwork = TRUE 琛ㄧず鍙€冭檻鏈湴鏀惰棌涓殑鏂囩尞
# globalCitationNetwork = FALSE (濡傛灉鍙兂鐪嬫湰鍦?
if (!is.null(M$CR) && sum(!is.na(M$CR) & M$CR != "") > 0 &&
    !is.null(M$PY) && sum(!is.na(M$PY)) > 0 &&
    (!is.null(M$UT) || !is.null(M$DI)) ) {
      
  # 浣跨敤 histNetwork 鑾峰彇鍘嗗彶缃戠粶鏁版嵁
  # results 鍙傛暟鏄?biblioAnalysis 鐨勭粨鏋?
  # n 琛ㄧず瑕侀€夋嫨鐨勬枃鐚暟閲?(鍩轰簬鏈湴寮曠敤娆℃暟 LCS)
  # sep 鏄弬鑰冩枃鐚殑鍒嗛殧绗?
  hist_network_rds_path <- file.path(output_basedir, "historiograph_network.rds")
  
  tryCatch({
      histResults <- histNetwork(results, n = 20, sep = ";") # 浣跨敤 results 瀵硅薄
      
      if (!is.null(histResults) && !is.null(histResults$NetMatrix) && nrow(histResults$NetMatrix) > 0) {
        # histPlot 鍙互鐢ㄦ潵鍙鍖栧巻鍙茬綉缁?
        # histPlot(histResults, n=15, size = 5, labelsize=3)
        
        # 淇濆瓨 histResults 瀵硅薄锛屽畠鍖呭惈浜嗙綉缁滅煩闃点€佹枃鐚垪琛ㄧ瓑
        saveRDS(histResults, file = hist_network_rds_path)
        cat("鍘嗗彶鐩存帴寮曟枃缃戠粶瀵硅薄 (histResults) 宸蹭繚瀛樺埌:", hist_network_rds_path, "\n")
      } else {
        cat("璀﹀憡: histNetwork 鏈兘鎴愬姛鐢熸垚鏈夋晥鐨勫巻鍙茬綉缁滄暟鎹€俓n")
      }
  }, error = function(e) {
      cat("閿欒: 杩涜鍘嗗彶鐩存帴寮曟枃鍒嗘瀽鏃跺彂鐢熼敊璇? ", conditionMessage(e), "\n")
  })
} else {
  cat("璀﹀憡: M 涓?CR, PY, UT/DI 瀛楁鏁版嵁涓嶈冻锛岃烦杩囧巻鍙茬洿鎺ュ紩鏂囧垎鏋愩€俓n")
}


cat("\n--- 鐮旂┒娴佺▼鑴氭湰鎵ц瀹屾瘯 ---\n")

# 鍚庣画鍙坊鍔犳洿澶氬垎鏋愭ā鍧?..
# 渚嬪锛?
# - 涓夊瓧娈靛垎鏋?(author-keyword-source)
# - 鍥犲瓙鍒嗘瀽 (Factorial Analysis for co-word data)
# - 瓒嬪娍涓婚鍒嗘瀽 (Trend Topics using fieldByYear)

# 娓呯悊鍐呭瓨 (鍙€?
# rm(list = ls())
# gc()

# 濡傛灉鍦?RStudio 涓繍琛岋紝浠ヤ笅鍛戒护鍙互鍦ㄨ剼鏈墽琛屽畬姣曞悗娓呴櫎鎺у埗鍙?
# cat("\014") 

