% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/localCitations.R
\name{localCitations}
\alias{localCitations}
\title{Author local citations}
\usage{
localCitations(M, fast.search = FALSE, sep = ";", verbose = FALSE)
}
\arguments{
\item{M}{is a bibliographic data frame obtained by the converting function \code{\link{convert2df}}.
It is a data matrix with cases corresponding to manuscripts and variables to Field Tag in the original SCOPUS and Clarivate Analytics WoS file.}

\item{fast.search}{is logical. If true, the function calculates local citations only for 25 percent top cited documents.}

\item{sep}{is the field separator character. This character separates citations in each string of CR column of the bibliographic data frame. The default is \code{sep = ";"}.}

\item{verbose}{is a logical.  If TRUE, results are printed on screen.}
}
\value{
an object of \code{class} "list" containing author local citations and document local citations.
}
\description{
It calculates local citations (LCS) of authors and documents of a bibliographic collection.
}
\details{
Local citations measure how many times an author (or a document) included in this collection have been cited by the documents also included in the collection.
}
\examples{
 
data(scientometrics, package = "bibliometrixData")

CR <- localCitations(scientometrics, sep = ";")

CR$Authors[1:10,]
CR$Papers[1:10,]

}
\seealso{
\code{\link{citations}} function for citation frequency distribution.

\code{\link{biblioAnalysis}} function for bibliometric analysis.

\code{\link{summary}} to obtain a summary of the results.

\code{\link{plot}} to draw some useful plots of the results.
}
