# research_pipeline.R
# 这个脚本将逐步实现研究框架 

# --- 1. 数据加载与格式转换 ---

# 1.1 加载必要的库
# 加载 bibliometrix 用于文献计量分析，tidyverse 用于数据处理和文件操作
library(bibliometrix)
library(tidyverse)

# -- 新增代码开始 --
# 检查并安装 writexl 包 (用于写入 Excel 文件)
if (!requireNamespace("writexl", quietly = TRUE)) {
  cat("writexl 包未安装，正在尝试安装...\n")
  install.packages("writexl")
}
library(writexl)
# -- 新增代码结束 --

# 1.2 设置工作目录和数据路径
# 设置当前工作目录，所有相对路径都将基于此目录
# 注意：请确保路径分隔符是 / 或者 \\
working_dir <- "C:/Users/<USER>/Desktop/article/数据处理部分" 
setwd(working_dir)
cat("当前工作目录已设置为:", getwd(), "\n")

# 指定包含原始 WoS 导出文件 (txt格式) 的目录
# 注意：请确保存储 WoS 文件的文件夹路径正确
raw_data_dir <- "C:/Users/<USER>/Desktop/数据文件/citespace数据"

# 检查目录是否存在
if (!dir.exists(raw_data_dir)) {
  stop("指定的原始数据目录不存在: ", raw_data_dir)
} else {
  cat("找到原始数据目录:", raw_data_dir, "\n")
}

# 1.3 读取并转换 WoS 数据文件
# 获取目录下所有 .txt 文件的完整路径列表
wos_files <- list.files(path = raw_data_dir, pattern = "\\.txt$", full.names = TRUE)

if (length(wos_files) == 0) {
  stop("在指定目录下没有找到 .txt 文件: ", raw_data_dir)
} else {
  cat("找到", length(wos_files), "个 .txt 文件，准备进行转换...\n")
  print(basename(wos_files)) # 打印找到的文件名，方便确认
}

# 使用 bibliometrix::convert2df 读取所有txt文件并合并
# dbsource = "wos" 指定数据源为 Web of Science
# format = "plaintext" 指定文件格式为纯文本 (通常的WoS导出格式)
cat("\n开始使用 bibliometrix 进行格式转换...\n")
M <- convert2df(file = wos_files, dbsource = "wos", format = "plaintext")

# 检查转换结果
cat("转换完成。 数据框 M 包含", nrow(M), "条记录 和", ncol(M), "个字段。\n")

cat("\n--- 诊断: M 对象 --- \n")
cat("维度 (Dim_M): ", paste(dim(M), collapse = " x "), "\n")
cat("列名 (Colnames_M): ", paste(colnames(M), collapse = ", "), "\n")
cat("前几行 AU (Authors_M_Head):\n")
print(head(M$AU))
cat("前几行 DE (AuthorKeywords_M_Head):\n")
print(head(M$DE))
cat("前几行 ID (KeywordsPlus_M_Head):\n")
print(head(M$ID))
cat("前几行 SO (Sources_M_Head):\n")
print(head(M$SO))
cat("非空 AU 数量 (NonEmpty_AU_M): ", sum(!is.na(M$AU) & M$AU != ""), "\n")
cat("非空 DE 数量 (NonEmpty_DE_M): ", sum(!is.na(M$DE) & M$DE != ""), "\n")
cat("非空 ID 数量 (NonEmpty_ID_M): ", sum(!is.na(M$ID) & M$ID != ""), "\n")
cat("非空 SO 数量 (NonEmpty_SO_M): ", sum(!is.na(M$SO) & M$SO != ""), "\n")
cat("--- 诊断: M 对象结束 --- \n\n")
 
# -- 新增代码开始 --
# 将转换后的数据框 M 保存为 Excel 文件

# --- 处理完全为 NA 的列，防止 write_xlsx 报错 --- 
M_for_excel <- M # 创建一个副本以避免修改原始 M
all_na_cols <- sapply(M_for_excel, function(x) all(is.na(x)))
if (any(all_na_cols)) {
  cat("检测到以下列完全由 NA 组成，将其转换为字符型以便写入 Excel:", 
      paste(names(M_for_excel)[all_na_cols], collapse=", "), "\n")
  for (col_name in names(M_for_excel)[all_na_cols]) {
    M_for_excel[[col_name]] <- as.character(M_for_excel[[col_name]])
  }
}
# --- 处理结束 ---

# --- 新增：处理超长字符串，防止 write_xlsx 报错 ---
excel_char_limit <- 32700 # Excel 限制大约 32767，留一点余地
truncated_cols <- character(0) # 记录哪些列被截断了

# 遍历所有字符类型的列
char_cols <- names(M_for_excel)[sapply(M_for_excel, is.character)]
for (col_name in char_cols) {
  # 计算每行文本的长度 (na.rm=TRUE 忽略NA)
  char_lengths <- nchar(M_for_excel[[col_name]], keepNA = FALSE)
  # 检查是否有超长的
  if (any(char_lengths > excel_char_limit, na.rm = TRUE)) {
    truncated_cols <- c(truncated_cols, col_name)
    # 对超长的字符串进行截断
    M_for_excel[[col_name]] <- ifelse(
      !is.na(M_for_excel[[col_name]]) & char_lengths > excel_char_limit,
      paste0(substr(M_for_excel[[col_name]], 1, excel_char_limit), "... [截断]"),
      M_for_excel[[col_name]]
    )
  }
}
if (length(truncated_cols) > 0) {
  cat("警告：检测到以下列中存在超长文本 (超过", excel_char_limit, "字符)，已截断以便写入 Excel:", 
      paste(unique(truncated_cols), collapse=", "), "\n")
}
# --- 处理结束 ---

output_xlsx_file <- file.path(working_dir, "WoS_converted_data.xlsx")
tryCatch({
  # 使用处理过的 M_for_excel 写入
  write_xlsx(M_for_excel, path = output_xlsx_file)
  cat("已将转换后的数据框同时保存为 Excel 文件到:", output_xlsx_file, "\n")
}, error = function(e) {
  cat("错误：无法将数据框保存为 Excel 文件。请检查 R 是否有写入权限以及 'writexl' 包是否正常工作。\n错误信息:", conditionMessage(e), "\n")
})
# -- 新增代码结束 --

# 1.4 保存转换后的数据框
# 将转换后的数据框 M 保存为 RData 文件，以便后续快速加载
output_rdata_file <- file.path(working_dir, "WoS_converted_data.RData")
save(M, file = output_rdata_file)

cat("已将转换后的数据框保存到:", output_rdata_file, "\n")

# 清理内存 (可选)
# rm(wos_files, raw_data_dir) 
# gc()

cat("\n--- 第一步：数据加载与格式转换完成 ---\n")


# --- 2. 数据探索与初步缺失分析 ---

# 2.1 加载之前保存的数据
# 确保工作目录设置正确 (如果脚本是分段执行)
# setwd(working_dir) # 如果需要重新设置
load("WoS_converted_data.RData")
cat("\n已加载数据框 M。\n")

# 2.2 显示数据框基本信息
cat("\n--- 数据框 M 基本信息 ---\n")
cat("维度 (记录数 x 字段数):", dim(M)[1], "x", dim(M)[2], "\n")

# 2.3 定义 WoS 字段中文含义映射
# 完整映射表 (持续更新)
field_mapping <- data.frame(
  Tag = c("DT", "AU", "AF", "TI", "SO", "LA", "DE", "ID", "AB", "C1", "RP", "CR", "TC", 
          "PY", "SC", "UT", "DI", "WC", "J9", "JI", "PU", "PI", "PA", "SN", "BN", 
          "FU", "NR", "VL", "IS", "BP", "EP", "PG", "DA", "EM", "OI", "RI", "PM", 
          "OA", "HC", "HP", "Z9", "U1", "U2", "U3", "U4", "U5", "U6", "D2", "EA", 
          "EY", "DB", "AU_CO", "AU_UN", "AU1_CO", "AU1_UN", "SR", "LCS", "GCS", "MCP", 
          "SCP", "TI_TM", "AB_TM", "DE_TM", "ID_TM", "CO_CA", "N_GRANT",
          # -- WoS 标准字段 & bibliometrix 衍生/常见字段 --
          "AR", "BA", "BE", "BF", "C3", "CA", "CL", "CT", "CY", # 已有补充
          "EF", "EI", "ER", "FX", "GA", "HO", "PD", "PN", "PT", "SE", "SI", "SP", "SU", # 已有补充
          "SR_FULL", # 已有补充
          "WE", "C1raw", "AU_UN_NR" # 新增未映射字段
          ),
  Meaning = c("文档类型", "作者", "作者全名", "标题", "出版物来源", "语言", "作者关键词", 
              "关键词Plus", "摘要", "作者地址", "通讯作者地址", "引用参考文献", "被引频次", 
              "出版年份", "WoS学科类别", "唯一标识符(WoS)", "DOI", "Web of Science类别", 
              "期刊缩写(J9)", "ISO期刊缩写(JI)", "出版商", "出版商城市", "出版商地址", 
              "ISSN", "ISBN", "资助机构与编号", "参考文献数量", "卷号", "期号", "起始页码", 
              "结束页码", "页数", "出版日期(数据库记录)", "电子邮件地址", "ORCID标识符", "ResearcherID", 
              "PubMed ID", "开放获取状态", "高被引论文", "热点论文", "总引用次数(WoS)", 
              "过去180天的使用计数", "自2013年以来的使用计数", "用户定义字段3", "用户定义字段4", # U1, U2 更新
              "用户定义字段5", "用户定义字段6", "电子出版日期", "提前访问日期", 
              "提前访问年份", "数据来源", "作者国家", "作者机构", "第一作者国家", 
              "第一作者机构", "简短引用格式", "局部引用得分", "全局引用得分", "多国出版物", 
              "单国出版物", "标题术语矩阵", "摘要术语矩阵", "关键词术语矩阵", 
              "KeywordsPlus术语矩阵", "通讯作者国家", "资助项目数",
              # -- WoS 标准字段 & bibliometrix 衍生/常见字段 --
              "文章编号", "图书作者", "编者", "图书作者全名", "会议标题(C3)", 
              "会议赞助者(CA)", "会议地点", "会议标题(CT)", "会议日期", # 已有补充
              "文件结束符", "电子ISSN", "记录结束符", "资助文本", "团体作者", "会议主办方", 
              "出版日期(月/日)", "部分号", "出版物类型", "丛书标题", "特刊/增刊标识", "会议赞助者(SP)", 
              "增刊", # 已有补充
              "完整引文格式", # 已有补充
              "WoS 版本", "原始作者地址", "作者机构数量" # 新增未映射字段含义
              )
)

# 获取 M 的实际列名
actual_colnames <- colnames(M)

# -- 移除临时代码开始 --
# # 找出在 actual_colnames 中但不在 field_mapping$Tag 中的字段
# unmapped_tags <- setdiff(actual_colnames, field_mapping$Tag)
# if (length(unmapped_tags) > 0) {
#   cat("\n--- 以下字段存在于数据框 M 中，但未在 field_mapping 中定义 ---\n")
#   print(unmapped_tags)
# } else {
#   cat("\n--- 所有 M 数据框中的字段均已在 field_mapping 中定义 ---\n")
# }
# -- 移除临时代码结束 --

# 创建包含实际列名和中文含义的数据框
colnames_df <- data.frame(Tag = actual_colnames) %>%
  left_join(field_mapping, by = "Tag") %>%
  # 对于在映射表中找不到的字段，保留原 Tag，Meaning 设为提示信息
  mutate(Meaning = ifelse(is.na(Meaning) & Tag %in% actual_colnames, 
                        paste0("-- (", Tag, ") 未在预定义映射中 --"), 
                        Meaning))

cat("\n--- 数据框 M 字段列表 (Tag 与 中文含义) ---\n")
# 使用 print 控制输出格式，避免过宽
print(colnames_df, row.names = FALSE, right = FALSE)


# 2.4 执行缺失分析
# 加载 naniar 包 (用于缺失值分析)
# 脚本开头已加载 tidyverse，通常包含 dplyr 等依赖
if (!requireNamespace("naniar", quietly = TRUE)) {
  cat("naniar 包未安装，正在尝试安装...\n")
  install.packages("naniar")
}
library(naniar)

cat("\n--- 字段缺失值分析 (按缺失比例降序排列) ---\n")
# 计算缺失摘要
missing_summary <- miss_var_summary(M) %>%
  arrange(desc(pct_miss)) %>% # 按缺失比例降序排列
  # 合并中文含义 (使用已创建的 colnames_df)
  left_join(select(colnames_df, Tag, Meaning), by = c("variable" = "Tag")) %>%
  select(Tag = variable, Meaning, n_miss, pct_miss) # 选择并重排序列

# -- 新增代码：确认 missing_summary 的实际行数 --
cat("\n确认：计算得到的 missing_summary 对象包含", nrow(missing_summary), "行 (字段)。\n\n")
# -- 新增代码结束 --

# 打印完整的缺失总结
print(missing_summary, row.names = FALSE, right = FALSE, n = Inf)

cat("\n--- 第二步：数据探索与初步缺失分析完成 ---\n")

# 清理内存 (可选)
# rm(missing_summary, colnames_df, actual_colnames, field_mapping)
# gc() 

# --- 3. 描述性统计分析 ---

# 3.1 执行基本的文献计量分析
# 使用 bibliometrix 包的核心函数 biblioAnalysis 计算描述性统计量
# 这个函数会返回一个包含多个分析结果的对象
cat("\n--- 开始执行基本的文献计量分析 (biblioAnalysis) ---\n")
results <- biblioAnalysis(M)
cat("基本的文献计量分析计算完成。\n")

cat("\n--- 诊断: results 对象 --- \n")
cat("results 对象结构 (Structure_results):\n")
str(results)
if ("Authors" %in% names(results)) {
    cat("results$Authors 前几行 (Authors_results_Head):\n")
    print(head(results$Authors))
} else {
    cat("results$Authors 不存在 (Authors_results_missing)\n")
}
if ("Sources" %in% names(results)) {
    cat("results$Sources 前几行 (Sources_results_Head):\n")
    print(head(results$Sources))
} else {
    cat("results$Sources 不存在 (Sources_results_missing)\n")
}
cat("--- 诊断: results 对象结束 --- \n\n")

# 3.2 查看分析结果摘要
# 使用 summary() 函数可以查看 biblioAnalysis 计算出的主要指标概览
# 'k' 参数指定了在列表中显示多少个最高频的项目 (例如，排名前10的作者)
cat("\n--- 文献计量分析结果摘要 (Top 10) ---\n")
options(width = 150) # 尝试增加控制台输出宽度以便查看摘要
summary_results <- summary(results, k = 10) 
# print(summary_results) # summary() 会自动打印，通常不需要显式 print

cat("\n--- 诊断: summary_results 对象 --- \n")
cat("summary_results 对象结构 (Structure_summary_results):\n")
str(summary_results)
cat("summary_results$Authors 内容 (Content_summary_Authors):\n")
print(summary_results$Authors)
cat("summary_results$AuthorsFrac 内容 (Content_summary_AuthorsFrac):
")
print(summary_results$AuthorsFrac)
cat("summary_results$DE (Author Keywords) 内容 (Content_summary_DE):
")
print(summary_results$DE)
cat("summary_results$ID (Keywords Plus) 内容 (Content_summary_ID):
")
print(summary_results$ID)
cat("summary_results$Sources 内容 (Content_summary_Sources):
")
print(summary_results$Sources)
cat("--- 诊断: summary_results 对象结束 --- \n\n")

options(width = 80) # 恢复默认宽度 (可选)


# (可选) 访问更详细的结果，例如查看最高产作者的完整列表
# print(summary_results$Authors) # 打印所有作者的统计数据
# print(summary_results$Sources) # 打印所有来源期刊的统计数据

# 3.3 (新增) 保存描述性统计表格
# 创建输出子目录 (如果不存在)
output_basedir <- file.path(working_dir, "baseline_analysis_output")
if (!dir.exists(output_basedir)) {
  dir.create(output_basedir, recursive = TRUE)
  cat("创建输出目录:", output_basedir, "\n")
}

# 检查 summary_results 各个元素是否存在且不为空，然后保存
# Main Information
if (!is.null(summary_results$MainInformationDF)) { # MainInformationDF 是 summary 对象的正确元素名
  write.csv(summary_results$MainInformationDF, 
            file.path(output_basedir, "desc_main_info_from_summary.csv"), 
            row.names = TRUE) # 通常MainInformation的行名有意义
} else {
  cat("警告: summary_results$MainInformationDF 未找到或为空，不保存 desc_main_info_from_summary.csv\n")
}


# Top Authors
if (!is.null(summary_results$MostProdAuthors) && nrow(summary_results$MostProdAuthors) > 0) {
  top_authors_df <- summary_results$MostProdAuthors[, 1:2]
  # colnames(top_authors_df) <- c("Author", "Articles") # 根据实际需要调整列名
  write.csv(top_authors_df, file.path(output_basedir, "desc_top_authors.csv"), row.names = FALSE)
} else {
  cat("警告: summary_results$MostProdAuthors 为空或不存在, desc_top_authors.csv 将为空。\n")
  write.csv(data.frame(), file.path(output_basedir, "desc_top_authors.csv"), row.names = FALSE)
}

# Top Authors Fractionalized
if (!is.null(summary_results$MostProdAuthors) && ncol(summary_results$MostProdAuthors) >= 4 && nrow(summary_results$MostProdAuthors) > 0) {
  top_authors_frac_df <- summary_results$MostProdAuthors[, 3:4]
  # colnames(top_authors_frac_df) <- c("Author_Frac", "Articles_Frac") # 根据实际需要调整列名
  write.csv(top_authors_frac_df, file.path(output_basedir, "desc_top_authors_frac.csv"), row.names = FALSE)
} else {
  cat("警告: summary_results$MostProdAuthors (分数化部分) 为空、不存在或列数不足, desc_top_authors_frac.csv 将为空。\n")
  write.csv(data.frame(), file.path(output_basedir, "desc_top_authors_frac.csv"), row.names = FALSE)
}

# Top Author Keywords (DE)
if (!is.null(summary_results$MostRelKeywords) && ncol(summary_results$MostRelKeywords) >= 2 && nrow(summary_results$MostRelKeywords) > 0) {
  top_de_df <- summary_results$MostRelKeywords[, 1:2]
  # colnames(top_de_df) <- c("Author_Keyword_DE", "Articles") # 根据实际需要调整列名
  write.csv(top_de_df, file.path(output_basedir, "desc_top_author_keywords.csv"), row.names = FALSE)
} else {
  cat("警告: summary_results$MostRelKeywords (DE部分) 为空、不存在或列数不足, desc_top_author_keywords.csv 将为空。\n")
  write.csv(data.frame(), file.path(output_basedir, "desc_top_author_keywords.csv"), row.names = FALSE)
}

# Top Keywords Plus (ID)
if (!is.null(summary_results$MostRelKeywords) && ncol(summary_results$MostRelKeywords) >= 4 && nrow(summary_results$MostRelKeywords) > 0) {
  top_id_df <- summary_results$MostRelKeywords[, 3:4]
  # colnames(top_id_df) <- c("Keyword_Plus_ID", "Articles") # 根据实际需要调整列名
  write.csv(top_id_df, file.path(output_basedir, "desc_top_keywords_plus.csv"), row.names = FALSE)
} else {
  cat("警告: summary_results$MostRelKeywords (ID部分) 为空、不存在或列数不足, desc_top_keywords_plus.csv 将为空。\n")
  write.csv(data.frame(), file.path(output_basedir, "desc_top_keywords_plus.csv"), row.names = FALSE)
}

# Top Sources
if (!is.null(summary_results$MostRelSources) && nrow(summary_results$MostRelSources) > 0) {
  write.csv(summary_results$MostRelSources, file.path(output_basedir, "desc_top_sources.csv"), row.names = FALSE)
} else {
  cat("警告: summary_results$MostRelSources 为空或不存在, desc_top_sources.csv 将为空。\n")
  write.csv(data.frame(), file.path(output_basedir, "desc_top_sources.csv"), row.names = FALSE)
}


cat("\n--- 描述性统计表格保存尝试完成 ---\n")
# 后续可以根据 summary_results 的结构添加更多表格的保存
# 例如 Most Cited Countries, Most Cited Documents, etc.

# --- 4. 知识结构分析 ---

# 4.1 概念结构 (Co-occurrence, Co-word analysis)
# 分析关键词共现网络，例如使用作者关键词 (DE) 或关键词Plus (ID)

# 4.1.1 作者关键词共现网络 (DE)
cat("\n--- 开始计算作者关键词 (DE) 共现网络 ---\n")
# NetMatrix <- biblioNetwork(M, analysis = "co-occurrences", network = "author_keywords", sep = ";")
# 检查 M$DE 是否存在且包含有效数据
if (!is.null(M$DE) && sum(!is.na(M$DE) & M$DE != "") > 0) {
  
  cat("Preprocessing M$DE for biblioNetwork...\n")
  # 简单的预处理：确保是字符型，替换 NA 为空字符串，去除首尾空格
  M_DE_processed <- as.character(M$DE)
  M_DE_processed[is.na(M_DE_processed)] <- ""
  M_DE_processed <- trimws(M_DE_processed)
  
  # 检查分号的使用：统计每个记录中关键词数量
  cat("Unique DE terms (first 20 after splitting and unlisting):\n")
  print(head(unique(trimws(unlist(strsplit(M_DE_processed, ";")))), 20))
  empty_DE_terms <- sum(trimws(unlist(strsplit(M_DE_processed, ";"))) == "")
  cat("Number of empty DE terms after splitting:", empty_DE_terms, "\n")

  # 创建一个临时的数据框用于 biblioNetwork，只包含处理过的 DE 和必要的其他列 (如 PY, UT/DI)
  # biblioNetwork 可能只需要文献本身，但以防万一，我们用原始 M 的结构
  M_temp_DE <- M
  M_temp_DE$DE <- M_DE_processed

  cat("Attempting: NetMatrix_DE <- biblioNetwork(M_temp_DE, analysis = \"co-occurrence\", network = \"author_keywords\", sep = \";\")\n")
  NetMatrix_DE <- NULL # Initialize
  
  tryCatch({
    cat("Attempting direct biblioNetwork for DE first...\n")
    NetMatrix_DE <- biblioNetwork(M_temp_DE, analysis = "co-occurrence", network = "author_keywords", sep = ";")
    cat("biblioNetwork for DE completed successfully (direct attempt).\n")
  }, error = function(e_bn) {
    cat("ERROR during direct biblioNetwork for DE: ", conditionMessage(e_bn), "\n")
    cat("Now attempting cocMatrix for DE as an alternative...\n")
    tryCatch({
      NetMatrix_DE <<- cocMatrix(M_temp_DE, Field = "DE", sep = ";")
      cat("cocMatrix for DE completed successfully.\n")
      # --- 添加对 cocMatrix 返回值的即时检查 --- 
      if (is.null(NetMatrix_DE)){
          cat("Immediate check after cocMatrix for DE: Result is NULL.\n")
      } else {
          cat("Immediate check after cocMatrix for DE: Result is NOT NULL. Class: ", paste(class(NetMatrix_DE), collapse=", "), "\n")
          cat("Immediate check after cocMatrix for DE: Dimensions: ", ifelse(is.null(dim(NetMatrix_DE)), "NULL/Not matrix", paste(dim(NetMatrix_DE), collapse=" x ")), "\n")
      }
      # --- 检查结束 --- 
    }, error = function(e_coc) {
      cat("ERROR during cocMatrix for DE: ", conditionMessage(e_coc), "\n")
      NetMatrix_DE <<- NULL # Ensure it's NULL if both attempts fail
    })
  })
  
  if (!is.null(NetMatrix_DE)){
    cat("Class of NetMatrix_DE (after attempts): ", paste(class(NetMatrix_DE), collapse=", "), "\n")
    cat("Dimensions of NetMatrix_DE (rows x cols) (after attempts): ", ifelse(is.null(dim(NetMatrix_DE)), "NULL or Not a matrix/array", paste(dim(NetMatrix_DE), collapse = " x ")), "\n")
    if (is.matrix(NetMatrix_DE) || is.data.frame(NetMatrix_DE)) {
      if (!is.null(colnames(NetMatrix_DE))) {
        cat("First few colnames of NetMatrix_DE (after attempts): ", paste(head(colnames(NetMatrix_DE), 6), collapse=", "), "\n")
      } else {
        cat("NetMatrix_DE (after attempts) has NULL colnames.\n")
      }
    }
  } else {
    cat("NetMatrix_DE is NULL after all attempts.\n")
  }
  
  # 可选：保存网络矩阵
  # 注意：当前 cocMatrix 后备方案生成的是文档-词语矩阵 (DTM)，而非词语共现网络。
  if (!is.null(NetMatrix_DE) && inherits(NetMatrix_DE, "Matrix") && nrow(NetMatrix_DE) > 0 && ncol(NetMatrix_DE) > 0) {
    # --- 修改为 saveRDS --- 
    saveRDS(NetMatrix_DE, file.path(output_basedir, "net_cooccurrence_author_keywords_DTM.rds")) # 后缀添加 DTM 提示
    cat("作者关键词的文档-词语矩阵 (DTM, 来自 cocMatrix 后备) 已保存为 RDS 到 net_cooccurrence_author_keywords_DTM.rds\n")
    # --- 修改结束 --- 
  } else {
    cat("警告: 未能成功生成或验证作者关键词相关矩阵 (NetMatrix_DE)，或矩阵为空/不正确。biblioNetwork 直接调用失败，cocMatrix 后备可能未产生预期类型的共现网络。\n")
  }
} else {
  cat("警告: M$DE (作者关键词) 为空或不包含足够数据，跳过作者关键词共现网络分析。\n")
}


# 4.1.2 Keywords Plus 共现网络 (ID)
cat("\n--- 开始计算 Keywords Plus (ID) 共现网络 ---\n")
if (!is.null(M$ID) && sum(!is.na(M$ID) & M$ID != "") > 0) {

  cat("Preprocessing M$ID for biblioNetwork...\n")
  M_ID_processed <- as.character(M$ID)
  M_ID_processed[is.na(M_ID_processed)] <- ""
  M_ID_processed <- trimws(M_ID_processed)

  # --- 启用 ID 关键词检查 --- 
  cat("Unique ID terms (first 20 after splitting and unlisting):\n")
  print(head(unique(trimws(unlist(strsplit(M_ID_processed, ";")))), 20))
  empty_ID_terms <- sum(trimws(unlist(strsplit(M_ID_processed, ";"))) == "")
  cat("Number of empty ID terms after splitting:", empty_ID_terms, "\n")
  # --- 检查结束 --- 

  M_temp_ID <- M
  M_temp_ID$ID <- M_ID_processed

  cat("Attempting: NetMatrix_ID <- biblioNetwork(M_temp_ID, analysis = \"co-occurrence\", network = \"keywords_plus\", sep = \";\")\n")
  NetMatrix_ID <- NULL # Initialize

  tryCatch({
    cat("Attempting direct biblioNetwork for ID first...\n")
    NetMatrix_ID <- biblioNetwork(M_temp_ID, analysis = "co-occurrence", network = "keywords_plus", sep = ";")
    cat("biblioNetwork for ID completed successfully (direct attempt).\n")
  }, error = function(e_bn_id) {
    cat("ERROR during direct biblioNetwork for ID: ", conditionMessage(e_bn_id), "\n")
    cat("Now attempting cocMatrix for ID as an alternative...\n")
    tryCatch({
      NetMatrix_ID <<- cocMatrix(M_temp_ID, Field = "ID", sep = ";")
      cat("cocMatrix for ID completed successfully.\n")
      # --- 添加对 cocMatrix 返回值的即时检查 --- 
      if (is.null(NetMatrix_ID)){
          cat("Immediate check after cocMatrix for ID: Result is NULL.\n")
      } else {
          cat("Immediate check after cocMatrix for ID: Result is NOT NULL. Class: ", paste(class(NetMatrix_ID), collapse=", "), "\n")
          cat("Immediate check after cocMatrix for ID: Dimensions: ", ifelse(is.null(dim(NetMatrix_ID)), "NULL/Not matrix", paste(dim(NetMatrix_ID), collapse=" x ")), "\n")
      }
      # --- 检查结束 --- 
    }, error = function(e_coc_id) {
      cat("ERROR during cocMatrix for ID: ", conditionMessage(e_coc_id), "\n")
      NetMatrix_ID <<- NULL # Ensure it's NULL if both attempts fail
    })
  })

  if (!is.null(NetMatrix_ID)){
    cat("Class of NetMatrix_ID (after attempts): ", paste(class(NetMatrix_ID), collapse=", "), "\n")
    cat("Dimensions of NetMatrix_ID (rows x cols) (after attempts): ", ifelse(is.null(dim(NetMatrix_ID)), "NULL or Not a matrix/array", paste(dim(NetMatrix_ID), collapse = " x ")), "\n")
    if (is.matrix(NetMatrix_ID) || is.data.frame(NetMatrix_ID)) {
      if (!is.null(colnames(NetMatrix_ID))) {
        cat("First few colnames of NetMatrix_ID (after attempts): ", paste(head(colnames(NetMatrix_ID), 6), collapse=", "), "\n")
      } else {
        cat("NetMatrix_ID (after attempts) has NULL colnames.\n")
      }
    }
  } else {
    cat("NetMatrix_ID is NULL after all attempts.\n")
  }
  
  # 注意：当前 cocMatrix 后备方案生成的是文档-词语矩阵 (DTM)，而非词语共现网络。
  if (exists("NetMatrix_ID") && !is.null(NetMatrix_ID) && inherits(NetMatrix_ID, "Matrix") && ncol(NetMatrix_ID) > 0 && nrow(NetMatrix_ID) > 0) {
    # --- 修改为 saveRDS --- 
    saveRDS(NetMatrix_ID, file.path(output_basedir, "net_cooccurrence_keywords_plus_DTM.rds")) # 后缀添加 DTM 提示
    cat("Keywords Plus 的文档-词语矩阵 (DTM, 来自 cocMatrix 后备) 已保存为 RDS 到 net_cooccurrence_keywords_plus_DTM.rds\n")
    # --- 修改结束 --- 
  } else {
    cat("警告: 未能成功生成或验证 Keywords Plus 相关矩阵 (NetMatrix_ID)，或矩阵为空/不正确。biblioNetwork 直接调用失败，cocMatrix 后备可能未产生预期类型的共现网络。\n")
  }
} else {
  cat("警告: M$ID (Keywords Plus) 为空或不包含足够数据，跳过 Keywords Plus 共现网络分析。\n")
}


# 4.1.3 主题图 (Thematic Map) - 基于 Keywords Plus (ID)
cat("\n--- 开始生成主题图 (Thematic Map - ID) ---\n")

tryCatch({
  # 直接使用thematicMap函数从原始数据M生成主题图
  Map <- thematicMap(M, 
                    field = "ID",      # 使用关键词字段
                    n = 250,           # 选择前250个高频关键词
                    minfreq = 4,       # 最小频率为4
                    stemming = FALSE,  # 不使用词干提取
                    size = 0.7,        # 点的大小
                    n.labels = 5,      # 每个簇显示5个标签
                    repel = TRUE)      # 避免标签重叠
  
  # 绘制并保存结果
  if (!is.null(Map) && !is.null(Map$map)) {
    # 保存主题图
    png(file.path(output_basedir, "thematic_map_id.png"), 
        width = 3000, height = 2500, res = 300)
    plot(Map$map)
    dev.off()
    
    # 保存聚类信息
    saveRDS(Map, file.path(output_basedir, "thematic_map_id.rds"))
    cat("主题图成功生成并保存\n")
    
    # 可选：显示每个聚类的词汇
    Clusters <- Map$words[order(Map$words$Cluster, -Map$words$Occurrences), ]
    top_words <- Clusters %>%
      group_by(Cluster_Label) %>%
      top_n(5, Occurrences)
    write.csv(top_words, file.path(output_basedir, "thematic_map_clusters.csv"), 
              row.names = FALSE)
  }
}, error = function(e) {
  cat("生成主题图时出错:", conditionMessage(e), "\n")
  
  # 尝试使用更简单的参数
  tryCatch({
    cat("尝试使用简化参数重新生成主题图...\n")
    Map <- thematicMap(M, field = "ID", n = 100, minfreq = 3,
                      stemming = FALSE, size = 0.7)
    
    # 保存结果
    if (!is.null(Map) && !is.null(Map$map)) {
      png(file.path(output_basedir, "thematic_map_id_simple.png"), 
          width = 2400, height = 2000, res = 300)
      plot(Map$map)
      dev.off()
      saveRDS(Map, file.path(output_basedir, "thematic_map_id_simple.rds"))
      cat("使用简化参数成功生成主题图\n")
    }
  }, error = function(e2) {
    cat("使用简化参数生成主题图也失败:", conditionMessage(e2), "\n")
    cat("建议检查数据集M中的关键词(ID)字段是否包含足够数据\n")
  })
})

# 4.1.4 概念结构 - 多重对应分析 (MCA) - 基于 Keywords Plus (ID)
cat("\n--- 开始进行概念结构分析 (MCA - ID) ---\n")
if (!is.null(M$ID) && sum(!is.na(M$ID) & M$ID != "") > 10) { # 需要足够多的关键词数据
  # MCA 分析关键词的关联结构
  # field: 要分析的字段 ("ID", "DE", "TI", "AB")
  # ngrams: 是否考虑 n-gram (1=单个词, 2=双词组合等)
  # minWordFreq: 词的最小频率
  # k.max: 聚类的最大数量 (用于确定最佳聚类数)
  # stemming: 是否进行词干提取
  
  # 将结果保存到 RDS 文件，因为MCA结果可能很大且复杂
  mca_id_rds_path <- file.path(output_basedir, "conceptual_structure_mca_id.rds")
  
  tryCatch({
      CS_ID <- conceptualStructure(M, field="ID", method="MCA", 
                                   stemming=FALSE, labels = 10,
                                   ngrams = 1,
                                   documents = 10 # 图中显示的文献数量
                                  )
      # conceptualStructure 返回的对象可以直接 plot
      # plot(CS_ID) 
      
      # 保存 CS_ID 对象
      saveRDS(CS_ID, file = mca_id_rds_path)
      cat("概念结构 (MCA - ID) 对象已保存到:", mca_id_rds_path, "\n")
      
      # 可以尝试生成并保存绘图，但 MCA 的图可能需要交互式调整
      # plot_mca_id_path <- file.path(output_basedir, "plot_conceptual_structure_mca_id.png")
      # png(plot_mca_id_path, width=1200, height=1000, res=150)
      # tryCatch({
      #   plot(CS_ID) # 默认会产生两个图
      # }, finally = {
      #   dev.off()
      # })
      # cat("概念结构 (MCA - ID) 尝试保存绘图到:", plot_mca_id_path, "\n")
      
  }, error = function(e) {
      cat("错误: 进行概念结构分析 (MCA - ID) 时发生错误: ", conditionMessage(e), "\n")
  })
} else {
  cat("警告: M$ID (Keywords Plus) 数据不足以进行概念结构 (MCA) 分析。\n")
}

# 如果上述代码仍然失败，提供一个完全不同的替代方案
# 添加一个新的函数调用，使用 conceptualStructure 作为替代方案
cat("\n--- 尝试执行概念因子分析 (代替主题图) ---\n")
tryCatch({
    # conceptualStructure 是 bibliometrix 中的另一种概念分析方法
    # 它使用因子分析而不是网络聚类，可能对 NA 值更加健壮
    CS_factorial <- conceptualStructure(M, 
                                       field = "ID", 
                                       method = "CA",  # 使用对应分析而不是 MCA
                                       minDegree = 10, # 最小共现频率
                                       clust = 5,      # 聚类数量
                                       stemming = FALSE,
                                       k.max = 8,      # 最大考虑的聚类数
                                       labelsize = 10)
    
    # 保存结果 
    factorial_rds_path <- file.path(output_basedir, "factorial_analysis_ID.rds")
    saveRDS(CS_factorial, file = factorial_rds_path)
    cat("概念因子分析结果已保存到:", factorial_rds_path, "\n")
    
}, error = function(e) {
    cat("尝试执行概念因子分析时也发生错误:", conditionMessage(e), "\n")
    
    # 如果 ID 字段的分析失败，尝试 DE 字段
    cat("尝试使用 DE 字段进行概念因子分析...\n")
    tryCatch({
        CS_factorial_DE <- conceptualStructure(M, 
                                             field = "DE", 
                                             method = "CA",
                                             minDegree = 10,
                                             clust = 5,
                                             stemming = FALSE,
                                             k.max = 8,
                                             labelsize = 10)
        
        # 保存结果
        factorial_de_rds_path <- file.path(output_basedir, "factorial_analysis_DE.rds")
        saveRDS(CS_factorial_DE, file = factorial_de_rds_path)
        cat("使用 DE 字段的概念因子分析结果已保存到:", factorial_de_rds_path, "\n")
    }, error = function(e2) {
        cat("使用 DE 字段进行概念因子分析也失败了:", conditionMessage(e2), "\n")
    })
})

# 4.2 智力结构 (Co-citation, Bibliographic coupling)

# 4.2.1 文献共被引网络 (References co-citation)
cat("\n--- 开始计算文献共被引网络 ---\n")
# 检查 M$CR 是否存在且包含有效数据
if (!is.null(M$CR) && sum(!is.na(M$CR) & M$CR != "") > 0) {
  NetMatrix_CR <- biblioNetwork(M, analysis = "co-citation", network = "references", sep = ";")
  
  if (exists("NetMatrix_CR") && !is.null(NetMatrix_CR) && ncol(NetMatrix_CR) > 0) {
    # 共被引网络可能非常大，通常不直接保存为CSV，而是用于后续分析或绘图
    # networkPlot(NetMatrix_CR, n = 20, Title = "Co-citation Network - References", type = "fruchterman", size=T, remove.multiple=F, labelsize=0.7,label.n=20,label.cex=F)
    
    # 保存网络对象本身到 RDS，以便后续使用 VOSviewer 或 Gephi
    saveRDS(NetMatrix_CR, file = file.path(output_basedir, "net_cocitation_references.rds"))
    cat("文献共被引网络对象已保存到 net_cocitation_references.rds\n")
  } else {
    cat("警告: 未能成功生成文献共被引网络矩阵 (NetMatrix_CR)，或矩阵为空。\n")
  }
} else {
  cat("警告: M$CR (Cited References) 为空或不包含足够数据，跳过文献共被引网络分析。\n")
}


# 4.2.2 作者文献耦合网络 (Authors bibliographic coupling)
cat("\n--- 开始计算作者文献耦合网络 ---\n")
# 检查 M$AU 和 M$CR 是否存在
if (!is.null(M$AU) && sum(!is.na(M$AU) & M$AU != "") > 0 && 
    !is.null(M$CR) && sum(!is.na(M$CR) & M$CR != "") > 0) {
  NetMatrix_Coupling_AU <- biblioNetwork(M, analysis = "coupling", network = "authors", sep = ";")
  
  if (exists("NetMatrix_Coupling_AU") && !is.null(NetMatrix_Coupling_AU) && ncol(NetMatrix_Coupling_AU) > 0) {
    # --- 修改为 saveRDS --- 
    saveRDS(NetMatrix_Coupling_AU, file.path(output_basedir, "net_coupling_authors.rds"))
    cat("作者文献耦合网络矩阵已保存为 RDS 到 net_coupling_authors.rds\n")
    # --- 修改结束 --- 
    # net_coupling_au <- networkPlot(NetMatrix_Coupling_AU, n = 20, Title = "Bibliographic Coupling - Authors", type = "fruchterman", size=T,remove.multiple=F,labelsize=0.7,label.n=20,label.cex=F)
  } else {
    cat("警告: 未能成功生成作者文献耦合网络矩阵 (NetMatrix_Coupling_AU)，或矩阵为空。\n")
  }
} else {
  cat("警告: M$AU (Authors) 或 M$CR (Cited References) 数据不足，跳过作者文献耦合分析。\n")
}

# 4.2.3 期刊文献耦合网络 (Sources bibliographic coupling)
cat("\n--- 开始计算期刊文献耦合网络 ---\n")
# 检查 M$SO 和 M$CR 是否存在
if (!is.null(M$SO) && sum(!is.na(M$SO) & M$SO != "") > 0 &&
    !is.null(M$CR) && sum(!is.na(M$CR) & M$CR != "") > 0) {
  NetMatrix_Coupling_SO <- biblioNetwork(M, analysis = "coupling", network = "sources", sep = ";")
  
  if (exists("NetMatrix_Coupling_SO") && !is.null(NetMatrix_Coupling_SO) && ncol(NetMatrix_Coupling_SO) > 0) {
    # --- 修改为 saveRDS --- 
    saveRDS(NetMatrix_Coupling_SO, file.path(output_basedir, "net_coupling_sources.rds"))
    cat("期刊文献耦合网络矩阵已保存为 RDS 到 net_coupling_sources.rds\n")
    # --- 修改结束 --- 
    # net_coupling_so <- networkPlot(NetMatrix_Coupling_SO, n = 20, Title = "Bibliographic Coupling - Sources", type = "fruchterman", size=T,remove.multiple=F,labelsize=0.7,label.n=20,label.cex=F)
  } else {
    cat("警告: 未能成功生成期刊文献耦合网络矩阵 (NetMatrix_Coupling_SO)，或矩阵为空。\n")
  }
} else {
  cat("警告: M$SO (Sources) 或 M$CR (Cited References) 数据不足，跳过期刊文献耦合分析。\n")
}


# 4.3 社会结构 (Collaboration analysis)

# 4.3.1 作者合作网络 (Author collaboration)
cat("\n--- 开始计算作者合作网络 ---\n")
# 检查 M$AU 是否存在
if (!is.null(M$AU) && sum(!is.na(M$AU) & M$AU != "") > 0) {
  NetMatrix_Collab_AU <- biblioNetwork(M, analysis = "collaboration", network = "authors", sep = ";")
  
  if (exists("NetMatrix_Collab_AU") && !is.null(NetMatrix_Collab_AU) && ncol(NetMatrix_Collab_AU) > 0) {
    # --- 修改为 saveRDS --- 
    saveRDS(NetMatrix_Collab_AU, file.path(output_basedir, "net_author_collaboration.rds"))
    cat("作者合作网络矩阵已保存为 RDS 到 net_author_collaboration.rds\n")
    # --- 修改结束 --- 
    # net_collab_au <- networkPlot(NetMatrix_Collab_AU, n = 50, Title = "Collaboration Network - Authors", type = "fruchterman", size=T,remove.multiple=F,labelsize=0.7,label.n=30,label.cex=F)
  } else {
    cat("警告: 未能成功生成作者合作网络矩阵 (NetMatrix_Collab_AU)，或矩阵为空。\n")
  }
} else {
  cat("警告: M$AU (Authors) 数据不足，跳过作者合作网络分析。\n")
}


# 4.3.2 机构合作网络 (Institution collaboration)
# 注意：机构名称需要清洗和标准化才能获得有意义的结果
# bibliometrix 默认使用 C1 字段（作者地址）中的机构信息
cat("\n--- 开始计算机构合作网络 ---\n")
# 检查 M$C1 是否存在 (或者 M$AU_UN 如果已经处理过机构信息)
# 假设使用 M$C1，bibliometrix 会尝试从中提取机构
if (!is.null(M$C1) && sum(!is.na(M$C1) & M$C1 != "") > 0) {
  # 机构合作分析通常需要更精细的机构名称提取和合并，这里使用 bibliometrix 的默认行为
  # 可以考虑 metaTagExtraction(M, Field = "AU_UN", sep = ";") 来预处理机构
  NetMatrix_Collab_Inst <- biblioNetwork(M, analysis = "collaboration", network = "institutions", sep = ";")
  
  if (exists("NetMatrix_Collab_Inst") && !is.null(NetMatrix_Collab_Inst) && ncol(NetMatrix_Collab_Inst) > 0) {
    # --- 修改为 saveRDS --- 
    saveRDS(NetMatrix_Collab_Inst, file.path(output_basedir, "net_institution_collaboration.rds"))
    cat("机构合作网络矩阵已保存为 RDS 到 net_institution_collaboration.rds\n")
    # --- 修改结束 --- 
    # net_collab_inst <- networkPlot(NetMatrix_Collab_Inst, n = 20, Title = "Collaboration Network - Institutions", type = "fruchterman", size=T,remove.multiple=F,labelsize=0.7,label.n=20,label.cex=F)
  } else {
    cat("警告: 未能成功生成机构合作网络矩阵 (NetMatrix_Collab_Inst)，或矩阵为空。\n")
  }
} else {
  cat("警告: M$C1 (Author Affiliations) 数据不足或缺失，跳过机构合作网络分析。\n")
}

# 4.3.3 国家合作网络 (Country collaboration)
# 使用 SCP (Single Country Publications) 和 MCP (Multiple Country Publications) 指标
# 也可以通过 networkPlot(NetMatrix_Country) 进行可视化
cat("\n--- 开始计算国家合作网络 ---\n")
# 国家合作分析通常基于第一作者国家 (AU1_CO) 或所有作者国家 (AU_CO)
# 需要确保这些字段在 M 中存在且已通过 metaTagExtraction 正确填充
# 例如：M <- metaTagExtraction(M, Field = "AU_CO", sep = ";")

# 检查 M$AU_CO (作者国家，需要预处理) 或 results$CountryCollaboration (如果 biblioAnalysis 生成了)
# 这里我们假设 results 对象中包含了国家合作的信息 (通常是有的)
if (!is.null(results$CountryCollaboration) && nrow(results$CountryCollaboration$NetMatrix) > 0) {
  NetMatrix_Collab_Country <- results$CountryCollaboration$NetMatrix
  # 如果需要SCP/MCP，通常在 summary_results 或 results 的其他部分
  
  # --- 修改为 saveRDS --- 
  saveRDS(NetMatrix_Collab_Country, file.path(output_basedir, "net_country_collaboration.rds"))
  cat("国家合作网络矩阵已保存为 RDS 到 net_country_collaboration.rds\n")
  # --- 修改结束 --- 
} else if (!is.null(M$AU_CO) && sum(!is.na(M$AU_CO) & M$AU_CO != "") > 0) {
  # 如果 results 中没有，尝试从 M$AU_CO 生成 (需要 M$AU_CO 是正确格式)
  cat("尝试从 M$AU_CO 生成国家合作网络...\n")
  tryCatch({
    NetMatrix_Collab_Country_from_M <- biblioNetwork(M, analysis = "collaboration", network = "countries", sep = ";", field = "AU_CO")
    if (exists("NetMatrix_Collab_Country_from_M") && !is.null(NetMatrix_Collab_Country_from_M) && ncol(NetMatrix_Collab_Country_from_M) > 0) {
       # --- 修改为 saveRDS --- 
       saveRDS(NetMatrix_Collab_Country_from_M, file.path(output_basedir, "net_country_collaboration.rds"))
       cat("国家合作网络矩阵 (从 M$AU_CO 生成) 已保存为 RDS 到 net_country_collaboration.rds\n")
       # --- 修改结束 --- 
    } else {
       cat("警告: 从 M$AU_CO 生成国家合作网络失败或矩阵为空。\n")
    }
  }, error = function(e) {
    cat("错误: 从 M$AU_CO 生成国家合作网络时发生错误: ", conditionMessage(e), "\n")
  })
} else {
   cat("警告: results$CountryCollaboration 和 M$AU_CO 均不足以进行国家合作网络分析。\n")
}


# --- 5. 历史直接引文分析 (Historiograph) ---
cat("\n--- 开始进行历史直接引文分析 (Historiograph) ---\n")
# 需要 M 中包含 PY (出版年份), CR (引文列表), 和 UT/DI (唯一标识符)
# localCitationNetwork = TRUE 表示只考虑本地收藏中的文献
# globalCitationNetwork = FALSE (如果只想看本地)
if (!is.null(M$CR) && sum(!is.na(M$CR) & M$CR != "") > 0 &&
    !is.null(M$PY) && sum(!is.na(M$PY)) > 0 &&
    (!is.null(M$UT) || !is.null(M$DI)) ) {
      
  # 使用 histNetwork 获取历史网络数据
  # results 参数是 biblioAnalysis 的结果
  # n 表示要选择的文献数量 (基于本地引用次数 LCS)
  # sep 是参考文献的分隔符
  hist_network_rds_path <- file.path(output_basedir, "historiograph_network.rds")
  
  tryCatch({
      histResults <- histNetwork(results, n = 20, sep = ";") # 使用 results 对象
      
      if (!is.null(histResults) && !is.null(histResults$NetMatrix) && nrow(histResults$NetMatrix) > 0) {
        # histPlot 可以用来可视化历史网络
        # histPlot(histResults, n=15, size = 5, labelsize=3)
        
        # 保存 histResults 对象，它包含了网络矩阵、文献列表等
        saveRDS(histResults, file = hist_network_rds_path)
        cat("历史直接引文网络对象 (histResults) 已保存到:", hist_network_rds_path, "\n")
      } else {
        cat("警告: histNetwork 未能成功生成有效的历史网络数据。\n")
      }
  }, error = function(e) {
      cat("错误: 进行历史直接引文分析时发生错误: ", conditionMessage(e), "\n")
  })
} else {
  cat("警告: M 中 CR, PY, UT/DI 字段数据不足，跳过历史直接引文分析。\n")
}


cat("\n--- 研究流程脚本执行完毕 ---\n")

# 后续可添加更多分析模块...
# 例如：
# - 三字段分析 (author-keyword-source)
# - 因子分析 (Factorial Analysis for co-word data)
# - 趋势主题分析 (Trend Topics using fieldByYear)

# 清理内存 (可选)
# rm(list = ls())
# gc()

# 如果在 RStudio 中运行，以下命令可以在脚本执行完毕后清除控制台
# cat("\014") 

# 筛选出频率大于5的关键词
de_freq <- table(unlist(strsplit(M$DE, ";")))
de_top <- names(de_freq[de_freq > 5])
M_filtered <- M[M$DE %in% paste(de_top, collapse="|"), ]

