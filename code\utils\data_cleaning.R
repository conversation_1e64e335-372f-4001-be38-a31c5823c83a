# =================================================================================================
# === 数据清理工具函数 ===
# =================================================================================================
# 版本: 1.0.0
# 描述: 提供数据清理相关的工具函数

# 加载必要的包
required_packages <- c(
  "tidyverse",
  "stringdist",
  "stringi"
)

# 检查并安装缺失的包
missing_packages <- required_packages[!required_packages %in% installed.packages()[,"Package"]]
if (length(missing_packages) > 0) {
  install.packages(missing_packages)
}

# 加载所有必要的包
for (pkg in required_packages) {
  suppressPackageStartupMessages(library(pkg, character.only = TRUE))
}

# 清理作者名称
clean_author_names <- function(authors) {
  authors %>%
    # 1. 移除特殊字符
    str_replace_all("[^[:alnum:][:space:];]", "") %>%
    # 2. 统一分隔符
    str_replace_all(";;", ";") %>%
    # 3. 处理空格
    str_trim() %>%
    # 4. 统一大小写
    toupper()
}

# 清理机构名称
clean_institution_names <- function(institutions) {
  institutions %>%
    # 1. 移除特殊字符
    str_replace_all("[^[:alnum:][:space:];,]", "") %>%
    # 2. 统一分隔符
    str_replace_all(";;", ";") %>%
    # 3. 处理空格
    str_trim() %>%
    # 4. 统一大小写
    toupper()
}

# 清理关键词
clean_keywords <- function(keywords) {
  keywords %>%
    # 1. 移除特殊字符
    str_replace_all("[^[:alnum:][:space:];]", "") %>%
    # 2. 统一分隔符
    str_replace_all(";;", ";") %>%
    # 3. 处理空格
    str_trim() %>%
    # 4. 统一大小写
    toupper()
}

# 清理引用
clean_citations <- function(citations) {
  citations %>%
    # 1. 移除特殊字符
    str_replace_all("[^[:alnum:][:space:];,]", "") %>%
    # 2. 统一分隔符
    str_replace_all(";;", ";") %>%
    # 3. 处理空格
    str_trim()
}

# 处理缺失值
handle_missing_values <- function(data) {
  data %>%
    # 1. 将"null"转换为NA
    mutate(across(where(is.character), ~ifelse(. == "null", NA, .))) %>%
    # 2. 将空字符串转换为NA
    mutate(across(where(is.character), ~ifelse(. == "", NA, .)))
}

# 导出函数
export_functions <- c(
  "clean_author_names",
  "clean_institution_names",
  "clean_keywords",
  "clean_citations",
  "handle_missing_values"
) 