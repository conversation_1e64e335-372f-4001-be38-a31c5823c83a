#' OpenAlex API接口模块
#' @description 提供与OpenAlex API交互的功能

library(httr)
library(jsonlite)
library(dplyr)
library(tidyr)

#' 从OpenAlex API获取文献数据
#' @param query 搜索查询字符串
#' @param email 用户邮箱（用于API访问）
#' @param per_page 每页结果数
#' @param max_pages 最大页数
#' @return 包含文献数据的data.frame
get_openalex_data <- function(query, email, per_page = 200, max_pages = 10) {
  base_url <- "https://api.openalex.org/works"
  
  # 构建查询参数
  params <- list(
    search = query,
    per_page = per_page,
    page = 1,
    mailto = email
  )
  
  all_results <- list()
  current_page <- 1
  
  while (current_page <= max_pages) {
    # 更新页码
    params$page <- current_page
    
    # 发送请求
    response <- GET(base_url, query = params)
    
    # 检查响应状态
    if (status_code(response) != 200) {
      stop(sprintf("API请求失败: %s", status_code(response)))
    }
    
    # 解析响应
    result <- fromJSON(rawToChar(response$content))
    
    # 提取结果
    if (length(result$results) == 0) {
      break
    }
    
    all_results[[current_page]] <- result$results
    current_page <- current_page + 1
    
    # 检查是否还有更多结果
    if (length(result$results) < per_page) {
      break
    }
  }
  
  # 合并所有结果
  combined_results <- bind_rows(all_results)
  
  return(combined_results)
}

#' 清洗和标准化OpenAlex数据
#' @param data OpenAlex API返回的原始数据
#' @return 清洗后的data.frame
clean_openalex_data <- function(data) {
  cleaned_data <- data %>%
    # 选择需要的字段
    select(
      id, title, display_name, publication_date,
      authorships, abstract_inverted_index,
      cited_by_count, is_open_access,
      type, doi, journal, concepts
    ) %>%
    # 处理作者信息
    mutate(
      authors = sapply(authorships, function(x) {
        paste(sapply(x$author$display_name, function(y) y), collapse = ";")
      }),
      institutions = sapply(authorships, function(x) {
        paste(sapply(x$institutions$display_name, function(y) y), collapse = ";")
      }),
      countries = sapply(authorships, function(x) {
        paste(sapply(x$institutions$country, function(y) y), collapse = ";")
      })
    ) %>%
    # 处理关键词
    mutate(
      keywords = sapply(concepts, function(x) {
        paste(sapply(x$display_name, function(y) y), collapse = ";")
      })
    ) %>%
    # 重命名列以匹配标准格式
    rename(
      UT = id,
      TI = title,
      PY = publication_date,
      AU = authors,
      AF = institutions,
      C1 = countries,
      DE = keywords,
      TC = cited_by_count,
      DI = doi,
      SO = journal$display_name
    )
  
  return(cleaned_data)
}

#' 构建基线数据集
#' @param query 搜索查询字符串
#' @param email 用户邮箱
#' @return 基线数据集
build_baseline_dataset <- function(query, email) {
  # 获取原始数据
  raw_data <- get_openalex_data(query, email)
  
  # 清洗数据
  cleaned_data <- clean_openalex_data(raw_data)
  
  # 保存基线数据集
  saveRDS(cleaned_data, "data/raw/baseline_dataset.rds")
  
  return(cleaned_data)
} 