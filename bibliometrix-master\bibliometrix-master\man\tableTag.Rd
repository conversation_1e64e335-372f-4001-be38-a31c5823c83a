% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/tableTag.R
\name{tableTag}
\alias{tableTag}
\title{Tabulate elements from a Tag Field column}
\usage{
tableTag(
  M,
  Tag = "CR",
  sep = ";",
  ngrams = 1,
  remove.terms = NULL,
  synonyms = NULL
)
}
\arguments{
\item{M}{is a data frame obtained by the converting function \code{\link{convert2df}}.
It is a data matrix with cases corresponding to articles and variables to Field Tag in the original WoS or SCOPUS file.}

\item{Tag}{is a character object. It indicates one of the field tags of the
standard ISI WoS Field Tag codify.}

\item{sep}{is the field separator character. This character separates strings in each column of the data frame. The default is \code{sep = ";"}.}

\item{ngrams}{is an integer between 1 and 3. It indicates the type of n-gram to extract from titles or abstracts.}

\item{remove.terms}{is a character vector. It contains a list of additional terms to delete from the documents before term extraction. The default is \code{remove.terms = NULL}.}

\item{synonyms}{is a character vector. Each element contains a list of synonyms, separated by ";",  that will be merged into a single term (the first word contained in the vector element). The default is \code{synonyms = NULL}.}
}
\value{
an object of class \code{table}
}
\description{
It tabulates elements from a Tag Field column of a bibliographic data frame.
}
\details{
\code{tableTag} is an internal routine of main function \code{\link{biblioAnalysis}}.
}
\examples{

data(scientometrics, package = "bibliometrixData")
Tab <- tableTag(scientometrics, Tag = "CR", sep = ";")
Tab[1:10]

}
