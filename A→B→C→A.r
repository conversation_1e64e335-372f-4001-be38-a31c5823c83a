# WoS数据闭环处理流程：A→B→C→A
# 加载必要的库
if (!require(bibliometrix)) install.packages("bibliometrix")
if (!require(tidyverse)) install.packages("tidyverse")
if (!require(stringr)) install.packages("stringr")
if (!require(openxlsx)) install.packages("openxlsx")

library(bibliometrix)
library(tidyverse)
library(stringr)
library(openxlsx)

#=====================================================
# 闭环处理核心函数
#=====================================================

# 1. A→B：WoS转Bibliometrix（保留映射信息）
wos_to_bibliometrix_with_mapping <- function(wos_file, output_rds) {
  cat("正在读取WoS文件:", wos_file, "\n")
  
  # 尝试读取原始WoS文件
  wos_lines <- tryCatch({
    readLines(wos_file, warn = FALSE)
  }, error = function(e) {
    cat("无法以默认编码读取文件，尝试UTF-8编码...\n")
    tryCatch({
      readLines(wos_file, encoding = "UTF-8", warn = FALSE)
    }, error = function(e2) {
      stop("无法读取文件：", e2$message)
    })
  })
  
  # 建立记录映射
  cat("创建记录映射...\n")
  record_map <- create_record_mapping(wos_lines)
  
  # 使用bibliometrix转换数据
  cat("转换数据为bibliometrix格式...\n")
  bib_data <- convert2df(file = wos_file, dbsource = "wos", format = "plaintext")
  
  # 确保UT字段存在且可用作映射键
  if (!"UT" %in% colnames(bib_data) && nrow(record_map) > 0) {
    warning("警告：转换后的数据中缺少UT字段，将使用其他方法建立映射")
    # 尝试用其他字段建立映射
    record_map$bib_index <- 1:nrow(record_map)
    
    # 如果记录数匹配，假定顺序一致
    if (nrow(record_map) == nrow(bib_data)) {
      cat("记录数量匹配，假定顺序相同\n")
    } else {
      warning("警告：记录数量不匹配，可能导致映射不准确")
    }
  } else if (nrow(record_map) > 0) {
    # 清理UT字段进行匹配
    record_map$ut_clean <- gsub("^WOS:|^UT ", "", record_map$ut)
    bib_data$UT_clean <- gsub("^WOS:|^UT ", "", bib_data$UT)
    
    # 匹配原始记录与转换后数据
    cat("匹配原始记录与转换后数据...\n")
    ut_match <- match(record_map$ut_clean, bib_data$UT_clean)
    record_map$bib_index <- ut_match
    
    # 检查匹配情况
    unmapped <- sum(is.na(record_map$bib_index))
    if (unmapped > 0) {
      warning(paste0("警告：", unmapped, "条记录无法通过UT匹配"))
    }
  }
  
  # 保存数据和映射信息
  result <- list(
    data = bib_data,
    mapping = record_map,
    original_lines = wos_lines
  )
  
  saveRDS(result, file = output_rds)
  cat("转换结果已保存到:", output_rds, "\n")
  
  return(result)
}

# 2. 创建WoS记录映射
create_record_mapping <- function(wos_lines) {
  record_positions <- data.frame(
    record_id = integer(),
    start_line = integer(),
    end_line = integer(),
    ut = character(),
    stringsAsFactors = FALSE
  )
  
  record_id <- 0
  in_record <- FALSE
  start_line <- NULL
  current_ut <- NA
  
  for (i in 1:length(wos_lines)) {
    line <- wos_lines[i]
    
    # 新记录开始
    if (grepl("^PT ", line)) {
      in_record <- TRUE
      start_line <- i
    }
    
    # 查找UT
    if (in_record && grepl("^UT ", line)) {
      current_ut <- gsub("^UT ", "", line)
    }
    
    # 记录结束
    if (line == "ER" && in_record) {
      record_id <- record_id + 1
      
      # 向数据框添加新行
      new_row <- data.frame(
        record_id = record_id,
        start_line = start_line,
        end_line = i,
        ut = current_ut,
        stringsAsFactors = FALSE
      )
      
      record_positions <- rbind(record_positions, new_row)
      
      in_record <- FALSE
      current_ut <- NA
    }
  }
  
  cat("共找到", record_id, "条记录\n")
  return(record_positions)
}

# 3. B→C：模拟数据增强（本阶段仅复制B作为C）
simulate_data_enhancement <- function(mapping_file, output_enhanced) {
  # 读取带有映射信息的数据
  cat("读取带映射信息的数据...\n")
  mapped_data <- readRDS(mapping_file)
  
  # 提取bibliometrix数据
  bib_data <- mapped_data$data
  
  # 说明：本阶段暂不进行实际数据增强，仅复制数据作为占位符
  # 在实际应用中，此处会有真实的数据增强逻辑
  cat("模拟数据增强（当前仅复制数据，未实际修改）...\n")
  enhanced_data <- bib_data
  
  # 创建B-C映射（对于模拟情况，它们是相同的行索引）
  bc_mapping <- data.frame(
    b_index = 1:nrow(bib_data),
    c_index = 1:nrow(enhanced_data),
    stringsAsFactors = FALSE
  )
  
  # 创建空的变更记录（因为当前没有实际修改）
  cat("创建空的变更记录（因为当前没有实际修改）...\n")
  changes <- data.frame(
    row = integer(),
    field = character(),
    original = character(),
    new_value = character(),
    stringsAsFactors = FALSE
  )
  
  # 创建增强数据包
  result <- list(
    original = mapped_data,
    enhanced = enhanced_data,
    bc_mapping = bc_mapping,
    changes = changes
  )
  
  saveRDS(result, output_enhanced)
  cat("模拟增强数据已保存到:", output_enhanced, "\n")
  
  return(result)
}

# 4. C→A：将增强数据写回WoS格式
write_enhanced_to_wos <- function(enhanced_file, output_file) {
  # 读取增强数据包
  cat("读取增强数据包...\n")
  enhanced_package <- readRDS(enhanced_file)
  
  # 提取原始行、映射和变更
  original_lines <- enhanced_package$original$original_lines
  original_mapping <- enhanced_package$original$mapping  # A-B映射
  bc_mapping <- enhanced_package$bc_mapping  # B-C映射
  enhanced_data <- enhanced_package$enhanced  # C数据
  changes <- enhanced_package$changes  # 变更列表
  
  # 创建新文件内容
  new_lines <- original_lines
  
  # 处理数据变更并写回WoS格式
  cat("处理数据变更并写回WoS格式...\n")
  
  if (nrow(changes) > 0) {
    # 按字段分组处理变更，以便更高效地修改
    field_changes <- split(changes, changes$field)
    
    # 遍历每个字段的变更
    for (field_name in names(field_changes)) {
      cat("- 处理字段:", field_name, "\n")
      field_change_group <- field_changes[[field_name]]
      
      # 确定WoS中的字段标记
      wos_tag <- get_wos_tag_for_field(field_name)
      
      if (is.na(wos_tag)) {
        cat("  跳过无法映射的字段:", field_name, "\n")
        next
      }
      
      # 遍历该字段的所有变更
      for (i in 1:nrow(field_change_group)) {
        change <- field_change_group[i, ]
        b_index <- change$row
        
        # 通过B-C映射找到C中的索引（这一步在模拟中索引相同）
        c_in_bc <- which(bc_mapping$c_index == b_index)
        if (length(c_in_bc) == 0) next
        b_in_bc <- bc_mapping$b_index[c_in_bc[1]]
        
        # 通过A-B映射找到A中的记录位置
        record_in_ab <- which(original_mapping$bib_index == b_in_bc)
        if (length(record_in_ab) == 0) next
        
        record_info <- original_mapping[record_in_ab[1], ]
        
        # 在原始文件中定位字段
        found_field <- FALSE
        for (j in record_info$start_line:record_info$end_line) {
          if (grepl(paste0("^", wos_tag, " "), new_lines[j])) {
            # 替换字段内容
            original_content <- substr(new_lines[j], nchar(wos_tag) + 2, nchar(new_lines[j]))
            new_content <- change$new_value
            
            # 构造新行
            new_lines[j] <- paste0(wos_tag, " ", new_content)
            found_field <- TRUE
            
            # 检查多行字段
            if (j < record_info$end_line) {
              line_idx <- j + 1
              # 处理可能的续行（以空格开头的行）
              while (line_idx <= record_info$end_line && 
                     grepl("^\\s+", new_lines[line_idx]) && 
                     !grepl("^[A-Z][A-Z] ", new_lines[line_idx])) {
                # 移除这些续行，将在下一步重建
                new_lines[line_idx] <- ""
                line_idx <- line_idx + 1
              }
            }
            
            # 处理可能的多行内容
            if (grepl("\n", new_content)) {
              content_lines <- strsplit(new_content, "\n")[[1]]
              if (length(content_lines) > 1) {
                # 第一行已经处理过
                continuation_lines <- paste0("   ", content_lines[-1])
                
                # 在原字段行后插入续行
                before <- new_lines[1:(j)]
                after <- new_lines[(j+1):length(new_lines)]
                # 过滤掉前面标记为删除的空行
                after <- after[after != ""]
                
                new_lines <- c(before, continuation_lines, after)
              }
            }
            
            break
          }
        }
        
        # 如果未找到字段但需要添加
        if (!found_field && !is.na(change$new_value) && change$new_value != "") {
          # 在记录末尾（ER标记前）添加新字段
          insertion_point <- record_info$end_line
          
          # 构造新字段行
          new_field_line <- paste0(wos_tag, " ", change$new_value)
          
          # 处理多行内容
          if (grepl("\n", change$new_value)) {
            content_lines <- strsplit(change$new_value, "\n")[[1]]
            new_field_line <- paste0(wos_tag, " ", content_lines[1])
            
            if (length(content_lines) > 1) {
              continuation_lines <- paste0("   ", content_lines[-1])
              
              # 插入新字段和续行
              before <- new_lines[1:(insertion_point-1)]
              after <- new_lines[insertion_point:length(new_lines)]
              
              new_lines <- c(before, new_field_line, continuation_lines, after)
            } else {
              # 单行字段
              before <- new_lines[1:(insertion_point-1)]
              after <- new_lines[insertion_point:length(new_lines)]
              
              new_lines <- c(before, new_field_line, after)
            }
          } else {
            # 单行字段
            before <- new_lines[1:(insertion_point-1)]
            after <- new_lines[insertion_point:length(new_lines)]
            
            new_lines <- c(before, new_field_line, after)
          }
        }
      }
    }
  } else {
    cat("没有检测到变更，文件将保持不变\n")
  }
  
  # 写入文件
  cat("写入WoS文件...\n")
  writeLines(new_lines, output_file)
  
  cat("WoS文件已保存到:", output_file, "\n")
  return(output_file)
}

# 辅助函数：为bibliometrix字段获取对应的WoS标签
get_wos_tag_for_field <- function(field) {
  # 主要字段映射关系
  field_map <- list(
    "AU" = "AU",     # 作者
    "TI" = "TI",     # 标题
    "SO" = "SO",     # 出版物名称
    "DT" = "DT",     # 文档类型
    "DE" = "DE",     # 作者关键词
    "ID" = "ID",     # 关键词Plus
    "AB" = "AB",     # 摘要
    "C1" = "C1",     # 作者地址
    "RP" = "RP",     # 通讯作者地址
    "CR" = "CR",     # 引用文献
    "TC" = "TC",     # 被引次数
    "PY" = "PY",     # 出版年份
    "SC" = "SC",     # 研究领域
    "UT" = "UT",     # 唯一标识符
    "DI" = "DI",     # DOI
    "LA" = "LA",     # 语言
    "EM" = "EM",     # 电子邮件地址
    "FU" = "FU",     # 基金资助信息
    "NR" = "NR",     # 参考文献数量
    "BA" = "BA",     # 作者地址计数
    "A1" = "A1",     # 第一作者信息
    "AF" = "AF",     # 作者全名
    "BF" = "BF",     # 出版标记
    "CA" = "CA"      # 组作者
  )
  
  # 返回映射的标签或NA
  return(field_map[[field]])
}

# 5. 保存分析报告
save_analysis_report <- function(enhanced_package, output_file) {
  cat("生成字段映射分析报告...\n")
  
  # 提取原始数据和增强数据
  original_data <- enhanced_package$original$data
  enhanced_data <- enhanced_package$enhanced
  changes <- enhanced_package$changes
  
  # 检查是否有字段
  if (ncol(original_data) == 0 && ncol(enhanced_data) == 0) {
    cat("警告: 没有找到字段数据，无法生成报告\n")
    # 创建一个空的报告文件
    empty_report <- data.frame(
      信息 = "数据集中没有找到可分析的字段",
      stringsAsFactors = FALSE
    )
    write.xlsx(empty_report, output_file, rowNames = FALSE)
    return(empty_report)
  }
  
  # 创建字段映射分析
  original_fields <- colnames(original_data)
  enhanced_fields <- colnames(enhanced_data)
  
  all_fields <- union(original_fields, enhanced_fields)
  
  if (length(all_fields) == 0) {
    cat("警告: 没有找到字段，无法生成报告\n")
    # 创建一个空的报告文件
    empty_report <- data.frame(
      信息 = "数据集中没有找到可分析的字段",
      stringsAsFactors = FALSE
    )
    write.xlsx(empty_report, output_file, rowNames = FALSE)
    return(empty_report)
  }
  
  # 确保所有向量长度一致
  field_names <- all_fields
  original_presence <- sapply(all_fields, function(x) ifelse(x %in% original_fields, "是", "否"))
  enhanced_presence <- sapply(all_fields, function(x) ifelse(x %in% enhanced_fields, "是", "否"))
  
  wos_tags <- sapply(all_fields, function(x) {
    tag <- get_wos_tag_for_field(x)
    ifelse(is.na(tag), "-", tag)
  })
  
  change_counts <- sapply(all_fields, function(x) {
    if (x %in% changes$field) {
      return(sum(changes$field == x))
    } else {
      return(0)
    }
  })
  
  # 检查所有向量长度是否一致
  if (length(field_names) != length(original_presence) || 
      length(field_names) != length(enhanced_presence) || 
      length(field_names) != length(wos_tags) ||
      length(field_names) != length(change_counts)) {
    cat("警告: 数据维度不一致，尝试修正...\n")
    
    # 找出最短的长度
    min_length <- min(
      length(field_names),
      length(original_presence),
      length(enhanced_presence),
      length(wos_tags),
      length(change_counts)
    )
    
    # 截断所有向量到最短长度
    field_names <- field_names[1:min_length]
    original_presence <- original_presence[1:min_length]
    enhanced_presence <- enhanced_presence[1:min_length]
    wos_tags <- wos_tags[1:min_length]
    change_counts <- change_counts[1:min_length]
  }
  
  # 使用数据框的安全创建方式
  field_analysis <- data.frame(
    字段名 = field_names,
    原始数据集 = original_presence,
    增强数据集 = enhanced_presence,
    WoS标签 = wos_tags,
    变更数量 = change_counts
  )
  
  # 确保数据框非空
  if (nrow(field_analysis) == 0) {
    cat("警告: 生成的数据框为空，创建默认报告\n")
    field_analysis <- data.frame(
      字段名 = "无数据",
      原始数据集 = "无",
      增强数据集 = "无",
      WoS标签 = "无",
      变更数量 = 0,
      stringsAsFactors = FALSE
    )
  } else {
    # 排序：先按是否有变更，再按字段名
    field_analysis <- field_analysis[order(-field_analysis$变更数量, field_analysis$字段名), ]
  }
  
  tryCatch({
    # 保存报告
    write.xlsx(field_analysis, output_file, rowNames = FALSE)
    cat("字段映射分析报告已保存到:", output_file, "\n")
  }, error = function(e) {
    cat("保存报告时出错:", e$message, "\n")
    cat("尝试保存简化版报告...\n")
    # 尝试保存简化版报告
    simple_report <- data.frame(
      字段名 = field_names,
      变更数量 = change_counts,
      stringsAsFactors = FALSE
    )
    write.xlsx(simple_report, output_file, rowNames = FALSE)
  })
  
  return(field_analysis)
}

# 6. 主流程函数
process_wos_with_enhancement <- function(wos_file) {
  # 检查文件是否存在
  if (!file.exists(wos_file)) {
    stop("错误：文件不存在 - ", wos_file)
  }
  
  # 创建输出目录（如果不存在）
  output_dir <- dirname(wos_file)
  if (!dir.exists(output_dir)) {
    dir.create(output_dir, recursive = TRUE)
  }
  
  # 设置输出文件
  file_base <- basename(wos_file)
  file_stem <- tools::file_path_sans_ext(file_base)
  
  mapping_file <- file.path(output_dir, paste0(file_stem, "_mapping.rds"))
  enhanced_file <- file.path(output_dir, paste0(file_stem, "_enhanced.rds"))
  output_wos <- file.path(output_dir, paste0(file_stem, "_closed_loop.txt"))
  report_file <- file.path(output_dir, paste0(file_stem, "_field_mapping.xlsx"))
  
  # 1. A→B：转换并保留映射
  cat("\n== 步骤1: 转换WoS文件(A→B)并创建映射 ==\n")
  mapping_result <- tryCatch({
    wos_to_bibliometrix_with_mapping(wos_file, mapping_file)
  }, error = function(e) {
    cat("转换失败:", e$message, "\n")
    stop("A→B转换失败，中止处理")
  })
  
  # 2. B→C：模拟数据增强
  cat("\n== 步骤2: 模拟数据增强(B→C) ==\n")
  enhanced_result <- tryCatch({
    simulate_data_enhancement(mapping_file, enhanced_file)
  }, error = function(e) {
    cat("数据增强失败:", e$message, "\n")
    stop("B→C转换失败，中止处理")
  })
  
  # 3. 生成分析报告
  cat("\n== 步骤3: 生成字段映射分析报告 ==\n")
  mapping_report <- tryCatch({
    save_analysis_report(enhanced_result, report_file)
  }, error = function(e) {
    cat("报告生成失败:", e$message, "\n")
    cat("继续处理...\n")
    NULL
  })
  
  # 4. C→A：写回WoS格式
  cat("\n== 步骤4: 将增强数据写回WoS格式(C→A) ==\n")
  final_file <- tryCatch({
    write_enhanced_to_wos(enhanced_file, output_wos)
  }, error = function(e) {
    cat("写回WoS格式失败:", e$message, "\n")
    stop("C→A转换失败，中止处理")
  })
  
  cat("\n== 闭环处理完成! ==\n")
  cat("原始WoS文件(A): ", wos_file, "\n")
  cat("转换结果(A→B): ", mapping_file, "\n")
  cat("增强数据(B→C): ", enhanced_file, "\n")
  cat("闭环输出(C→A): ", final_file, "\n")
  if (!is.null(mapping_report)) {
    cat("字段映射报告: ", report_file, "\n")
  } else {
    cat("字段映射报告: 生成失败\n")
  }
  
  # 返回结果文件路径
  return(list(
    original = wos_file,
    mapping = mapping_file,
    enhanced = enhanced_file,
    final = final_file,
    report = if(!is.null(mapping_report)) report_file else NULL
  ))
}

#=====================================================
# 主程序
#=====================================================

# 设置文件路径 - 请修改为您实际的文件路径
wos_file <- "C:/Users/<USER>/Desktop/citespace cleaned/input/download1-500.txt"

# 检查文件是否存在
if (file.exists(wos_file)) {
  # 执行完整闭环处理流程
  cat("开始闭环处理文件:", wos_file, "\n")
  result <- process_wos_with_enhancement(wos_file)
  cat("闭环处理完成! 结果保存在:", result$final, "\n")
} else {
  cat("错误: 文件不存在 -", wos_file, "\n")
  cat("请修改脚本中的'wos_file'变量指向正确的WoS文件路径\n")
}