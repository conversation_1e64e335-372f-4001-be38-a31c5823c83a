# =================================================================================================
# === 数据存储模块 ===
# =================================================================================================
# 版本: 1.0.0
# 描述: 处理OpenAlex API数据的存储

# 加载必要的包
required_packages <- c(
  "jsonlite",
  "tidyverse",
  "lubridate"
)

# 检查并安装缺失的包
missing_packages <- required_packages[!required_packages %in% installed.packages()[,"Package"]]
if (length(missing_packages) > 0) {
  install.packages(missing_packages)
}

# 加载所有必要的包
for (pkg in required_packages) {
  suppressPackageStartupMessages(library(pkg, character.only = TRUE))
}

# 创建数据存储目录
create_storage_dirs <- function(base_dir = "data") {
  dirs <- c(
    file.path(base_dir, "raw"),           # 原始API响应
    file.path(base_dir, "processed"),     # 处理后的数据
    file.path(base_dir, "works"),         # 文献数据
    file.path(base_dir, "authors"),       # 作者数据
    file.path(base_dir, "institutions"),  # 机构数据
    file.path(base_dir, "logs")           # 日志文件
  )
  
  for (dir in dirs) {
    if (!dir.exists(dir)) {
      dir.create(dir, recursive = TRUE)
    }
  }
  
  return(dirs)
}

# 保存原始API响应
save_raw_response <- function(data, type, id, base_dir = "data") {
  # 生成文件名
  timestamp <- format(now(), "%Y%m%d_%H%M%S")
  # 清理ID，移除URL和特殊字符
  clean_id <- gsub("https://openalex.org/", "", id)
  clean_id <- gsub("[/\\\\?%*:|\"<>]", "_", clean_id)
  filename <- sprintf("%s_%s_%s.json", type, clean_id, timestamp)
  filepath <- file.path(base_dir, "raw", filename)
  
  # 保存为JSON
  write_json(data, filepath, pretty = TRUE, auto_unbox = TRUE)
  
  return(filepath)
}

# 保存处理后的文献数据
save_work_data <- function(work_data, base_dir = "data") {
  # 提取基本信息
  work_info <- list(
    id = work_data$id,
    title = work_data$display_name,
    publication_date = work_data$publication_date,
    authors = work_data$authorships,
    citations = work_data$cited_by_count,
    abstract = work_data$abstract_inverted_index,
    keywords = work_data$keywords,
    topics = work_data$topics
  )
  
  # 生成文件名
  work_id <- gsub("https://openalex.org/", "", work_data$id)
  work_id <- gsub("[/\\\\?%*:|\"<>]", "_", work_id)
  filename <- sprintf("work_%s.json", work_id)
  filepath <- file.path(base_dir, "works", filename)
  
  # 保存为JSON
  write_json(work_info, filepath, pretty = TRUE, auto_unbox = TRUE)
  
  return(filepath)
}

# 保存处理后的作者数据
save_author_data <- function(author_data, base_dir = "data") {
  # 提取基本信息
  author_info <- list(
    id = author_data$id,
    name = author_data$display_name,
    orcid = author_data$orcid,
    works_count = author_data$works_count,
    cited_by_count = author_data$cited_by_count,
    h_index = author_data$summary_stats$h_index,
    i10_index = author_data$summary_stats$i10_index,
    affiliations = author_data$affiliations,
    topics = author_data$topics
  )
  
  # 生成文件名
  author_id <- gsub("https://openalex.org/", "", author_data$id)
  author_id <- gsub("[/\\\\?%*:|\"<>]", "_", author_id)
  filename <- sprintf("author_%s.json", author_id)
  filepath <- file.path(base_dir, "authors", filename)
  
  # 保存为JSON
  write_json(author_info, filepath, pretty = TRUE, auto_unbox = TRUE)
  
  return(filepath)
}

# 记录API调用日志
log_api_call <- function(type, id, status, base_dir = "data") {
  # 创建日志条目
  log_entry <- list(
    timestamp = format(now(), "%Y-%m-%d %H:%M:%S"),
    type = type,
    id = id,
    status = status
  )
  
  # 生成日志文件名
  date <- format(now(), "%Y%m%d")
  filename <- sprintf("api_calls_%s.log", date)
  filepath <- file.path(base_dir, "logs", filename)
  
  # 追加到日志文件
  write.table(
    as.data.frame(log_entry),
    filepath,
    append = file.exists(filepath),
    col.names = !file.exists(filepath),
    row.names = FALSE,
    sep = "\t"
  )
  
  return(filepath)
}

# 初始化存储
init_storage <- function(base_dir = "data") {
  # 创建存储目录
  dirs <- create_storage_dirs(base_dir)
  
  # 创建README文件
  readme_path <- file.path(base_dir, "README.md")
  if (!file.exists(readme_path)) {
    readme_content <- c(
      "# OpenAlex数据存储",
      "",
      "## 目录结构",
      "- raw/: 原始API响应",
      "- processed/: 处理后的数据",
      "- works/: 文献数据",
      "- authors/: 作者数据",
      "- institutions/: 机构数据",
      "- logs/: API调用日志",
      "",
      "## 数据格式",
      "- 原始数据：JSON格式",
      "- 处理后的数据：JSON格式",
      "- 日志：TSV格式",
      "",
      "## 更新记录",
      sprintf("- %s: 初始化存储结构", format(now(), "%Y-%m-%d"))
    )
    writeLines(readme_content, readme_path)
  }
  
  return(dirs)
} 