# 文献计量分析系统

## 项目简介
这是一个基于R语言的文献计量分析系统，用于研究评价和文献分析。

## 主要功能
- OpenAlex API数据获取
- 文献数据存储和管理
- BibTeX格式转换
- 文献计量分析

## 项目结构
```
bibliometric-analysis/
├── R/                      # R源代码
├── tests/                  # 测试文件
├── data/                   # 数据存储
├── config/                 # 配置文件
└── docs/                   # 文档
```

## 安装说明
1. 克隆项目
2. 安装依赖包
3. 配置API参数

## 使用说明
1. 配置API参数
2. 运行数据获取
3. 进行数据分析

## 开发说明
- 使用R语言开发
- 遵循tidyverse风格指南
- 使用testthat进行测试

## 许可证
MIT License 