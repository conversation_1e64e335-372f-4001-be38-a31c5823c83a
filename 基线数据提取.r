# research_pipeline.R
# 这个脚本将逐步实现研究框架

# --- 1. 数据加载与格式转换 ---

# 1.1 加载必要的库
# 加载 bibliometrix 用于文献计量分析，tidyverse 用于数据处理和文件操作
library(bibliometrix)
library(tidyverse)

# 检查并安装 writexl 包 (用于写入 Excel 文件)
if (!requireNamespace("writexl", quietly = TRUE)) {
  cat("writexl 包未安装，正在尝试安装...\n")
  install.packages("writexl")
}
library(writexl)

# --- 新增辅助函数：获取原始记录和来源文件 ---
get_raw_record_and_source_file <- function(target_ut, wos_files_list) {
  # target_ut 来自 M$UT, 通常是 "WOS:XXXX" 或 "ISI:XXXX"
  for (file_path in wos_files_list) {
    tryCatch({
      raw_lines <- readLines(file_path, warn = FALSE)
      
      # 查找包含 target_ut 的 UT 标签行
      # WoS 文件中的UT行通常是 "UT WOS:XXXX..." 或 "UT ISI:XXXX..."
      # 我们期望 target_ut 就是 "WOS:XXXX..." 或 "ISI:XXXX..." 部分
      ut_line_indices <- which(startsWith(raw_lines, "UT "))
      found_ut_line_idx <- -1
      if (length(ut_line_indices) > 0) {
        for(idx in ut_line_indices){
          if(grepl(target_ut, raw_lines[idx], fixed = TRUE)){
            found_ut_line_idx <- idx
            break
          }
        }
      }

      if (found_ut_line_idx > 0) {
        current_ut_line <- found_ut_line_idx
        start_line_idx <- -1
        # 向后搜索 PT (Publication Type) 或 FN (File Name) 作为记录开始
        for (k in seq(current_ut_line, 1, by = -1)) {
          if (grepl("^PT ", raw_lines[k]) || grepl("^FN ", raw_lines[k])) {
            start_line_idx <- k# automated_deduplication_testing.R
# 脚本目的: 自动化测试不同的文献去重策略，并将结果输出到文本文件供人工审查。

# --- 1. 初始化与加载数据 ---

# 1.1 加载必要的库
cat("加载必要的库...\\n")
suppressPackageStartupMessages(library(bibliometrix))
suppressPackageStartupMessages(library(tidyverse))
# writexl 不是此特定脚本的直接需求，但如果后续要导出 M_initial_for_testing，可以保留
# if (!requireNamespace("writexl", quietly = TRUE)) {
#   cat("writexl 包未安装，正在尝试安装...\\n")
#   install.packages("writexl")
# }
# library(writexl)
cat("库加载完成。\\n")

# 1.2 设置工作目录和数据路径
# !!! 请确保修改为您的实际路径 !!!
working_dir <- "C:/Users/<USER>/Desktop/article/数据处理部分" 
raw_data_dir <- "C:/Users/<USER>/Desktop/数据文件/citespace数据" 

cat(paste0("尝试设置工作目录到: ", working_dir, "\\n"))
tryCatch({
  setwd(working_dir)
  cat("当前工作目录已设置为:", getwd(), "\\n")
}, error = function(e){
  stop(paste0("无法设置工作目录: ", working_dir, "错误: ", e$message))
})


if (!dir.exists(raw_data_dir)) {
  stop(paste0("指定的原始数据目录不存在: ", raw_data_dir))
} else {
  cat("找到原始数据目录:", raw_data_dir, "\\n")
}

# 1.3 读取并转换 WoS 数据文件
wos_files <- list.files(path = raw_data_dir, pattern = "\\\\.txt$", full.names = TRUE, ignore.case = TRUE) # 忽略大小写

if (length(wos_files) == 0) {
  stop(paste0("在指定目录下没有找到 .txt 文件: ", raw_data_dir))
} else {
  cat("找到", length(wos_files), "个 .txt 文件，准备进行转换...\\n")
  print(basename(wos_files))
}

cat("\\n开始使用 bibliometrix 进行格式转换 (convert2df)...\\n")
# remove.duplicates = TRUE 是 convert2df 的默认行为
M_initial_for_testing <- convert2df(file = wos_files, dbsource = "wos", format = "plaintext") 
cat("初始转换完成 (convert2df)。数据框 M_initial_for_testing 包含", nrow(M_initial_for_testing), "条记录 和", ncol(M_initial_for_testing), "个字段。\\n")
# 注意: convert2df 内部的去重数量不容易直接获取，可以通过比较原始文件总记录数和转换后记录数来估算。


# --- 2. 自动化去重测试框架 ---
cat("\\n--- 初始化自动化去重测试框架 ---\\n")

# 2.1 创建输出子目录
dedup_output_dir <- file.path(working_dir, "deduplication_tests_output")
if (!dir.exists(dedup_output_dir)){
  dir.create(dedup_output_dir, recursive = TRUE)
  cat("创建去重测试输出目录:", dedup_output_dir, "\\n")
} else {
  cat("去重测试输出目录已存在:", dedup_output_dir, "\\n")
}

# 2.2 定义去重测试执行函数
run_deduplication_test <- function(data_input, field_to_match, is_exact_match, tolerance_value, strategy_name_suffix = "", output_dir_path) {
  
  # 构建策略名称和输出文件名
  strategy_name <- paste0(field_to_match, 
                          ifelse(is_exact_match, "_Exact", paste0("_Tol", gsub("\\\\.", "_", as.character(tolerance_value)))), 
                          strategy_name_suffix)
  output_file_path <- file.path(output_dir_path, paste0("report_", strategy_name, ".txt"))
  
  # 使用sink开始将输出重定向到文件 (覆盖模式)
  zz <- file(output_file_path, open = "wt") # open for writing text
  sink(zz)
  sink(zz, type = "message") # Redirect messages (like from cat) as well

  cat(paste0("--- 测试策略报告: ", strategy_name, " ---\\n"))
  cat(paste0("执行时间: ", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\\n"))
  cat(paste0("输入记录数: ", nrow(data_input), "\\n"))
  cat(paste0("匹配字段: '", field_to_match, "', Exact匹配: ", is_exact_match, 
             ", 相似度阈值(tol): ", ifelse(is_exact_match, "N/A", format(tolerance_value, nsmall=2)), "\\n\\n"))
  
  M_before_test_func <- data_input 
  
  # 确保关键字段是字符型 (UT 和要匹配的字段)
  if ("UT" %in% names(M_before_test_func)) M_before_test_func$UT <- as.character(M_before_test_func$UT)
  if (field_to_match %in% names(M_before_test_func)) M_before_test_func[[field_to_match]] <- as.character(M_before_test_func[[field_to_match]])
  # 其他常用字段也转换为字符以避免后续提取时出错
  common_fields <- c("TI", "AU", "AF", "PY", "SO", "AB", "DI")
  for(cf in common_fields){
      if (cf %in% names(M_before_test_func)) M_before_test_func[[cf]] <- as.character(M_before_test_func[[cf]])
  }

  M_to_process_func <- M_before_test_func
  M_skipped_func <- data.frame() # 初始化为空数据框
  
  if (!is_exact_match && field_to_match %in% names(M_before_test_func)) {
    condition_empty_na <- is.na(M_before_test_func[[field_to_match]]) | M_before_test_func[[field_to_match]] == ""
    if(any(condition_empty_na)){
        M_to_process_func <- M_before_test_func[!condition_empty_na, , drop = FALSE]
        M_skipped_func <- M_before_test_func[condition_empty_na, , drop = FALSE]
        cat(paste0("注意: 有 ", nrow(M_skipped_func), " 条记录因 '", field_to_match, "' 字段为空或NA而未直接参与此轮匹配。\\n"))
    }
  }

  M_after_processed_part_func <- M_to_process_func 
  if(nrow(M_to_process_func) > 1) { 
      cat(paste0("对 ", nrow(M_to_process_func), " 条记录执行 duplicatedMatching (Field='", field_to_match, "', exact=", is_exact_match, 
                 if(!is_exact_match) paste0(", tol=", tolerance_value) else "", ")...\n"))
      if(is_exact_match){
          M_after_processed_part_func <- duplicatedMatching(M_to_process_func, Field = field_to_match, exact = TRUE)
      } else {
          M_after_processed_part_func <- duplicatedMatching(M_to_process_func, Field = field_to_match, exact = FALSE, tol = tolerance_value)
      }
      cat("duplicatedMatching 执行完毕。\n")
  } else {
      cat(paste0("字段 '", field_to_match, "' 非空/NA的记录不足2条（仅 ", nrow(M_to_process_func) ," 条），跳过 duplicatedMatching。\n"))
  }

  M_after_test_func <- M_after_processed_part_func
  if (nrow(M_skipped_func) > 0) {
    if (nrow(M_after_processed_part_func) == 0 ) { 
        M_after_test_func <- M_skipped_func
    } else { # 尝试合并，确保列名对齐
        # 找出共同的列名
        cols_processed <- names(M_after_processed_part_func)
        cols_skipped <- names(M_skipped_func)
        common_cols <- intersect(cols_processed, cols_skipped)
        
        # 如果没有共同列或者关键列丢失，这可能是一个问题，但我们尝试合并
        if(length(common_cols) > 0){
             M_after_test_func <- rbind(M_after_processed_part_func[, common_cols, drop=FALSE], 
                                     M_skipped_func[, common_cols, drop=FALSE])
        } else {
             cat("警告: 合并被跳过记录时，处理后部分与跳过部分无共同列名。整体计数可能不准。\n")
             # 此时 M_after_test_func 仍然是 M_after_processed_part_func
        }
    }
  }
  
  removed_in_processed_part_count <- nrow(M_to_process_func) - nrow(M_after_processed_part_func)
  overall_removed_count <- nrow(M_before_test_func) - nrow(M_after_test_func)

  cat(paste0("\\n处理非空'", field_to_match, "'字段记录: 从 ", nrow(M_to_process_func) ," 条变为 ", nrow(M_after_processed_part_func) ," 条 (移除了 ", removed_in_processed_part_count, " 条)。\\n"))
  cat(paste0("整体数据框: 从 ", nrow(M_before_test_func) ," 条变为 ", nrow(M_after_test_func) ," 条 (总共移除了 ", overall_removed_count, " 条)。\\n\\n"))
  
  # 控制台摘要 (输出到标准输出，而不是被sink的文件)
  message(paste0("测试策略 '", strategy_name, "': 在处理部分移除了 ", removed_in_processed_part_count, " 条. 总移除 ", overall_removed_count, " 条. 报告: ", basename(output_file_path)))

  if (removed_in_processed_part_count > 0 && "UT" %in% names(M_to_process_func) && "UT" %in% names(M_after_processed_part_func)) {
    # 使用 M_to_process_func 和 M_after_processed_part_func 来找出被移除的记录的详情
    removed_articles_details_func <- dplyr::anti_join(M_to_process_func, M_after_processed_part_func, by = "UT")
    
    if (nrow(removed_articles_details_func) > 0) {
      cat("--- 被移除文献对详情 (基于参与匹配的部分，最多显示前200对) ---\n")
      limit_print_pairs <- min(nrow(removed_articles_details_func), 200) # 限制输出数量
      
      for (i_pair in 1:limit_print_pairs) {
        # 获取被移除记录的完整信息 (从 M_to_process_func, 即匹配前的状态)
        removed_ut_val <- as.character(removed_articles_details_func$UT[i_pair])
        # 确保 removed_record 只有一行
        removed_record_candidates <- M_to_process_func[M_to_process_func$UT == removed_ut_val, , drop=FALSE]
        if(nrow(removed_record_candidates)==0){
            cat(paste0("警告: 无法在 M_to_process_func 中找到 UT 为 ", removed_ut_val, " 的被移除记录以显示详情。\n"))
            next
        }
        removed_record <- removed_record_candidates[1, , drop=FALSE]


        cat(paste0("\\n--- 识别出的重复文献 (\", i_pair, \" / \", nrow(removed_articles_details_func), \") ---\n"))
        cat("  [被移除文献详情]:\n")
        cat(paste0("    UT: ", removed_record$UT, "\n"))
        cat(paste0("    TI: '", removed_record$TI, "'\n"))
        cat(paste0("    PY: ", removed_record$PY, "\n"))
        cat(paste0("    AU: ", substr(removed_record$AU, 1, 150), ifelse(nchar(removed_record$AU)>150, "...", ""), "\n"))
        cat(paste0("    SO: ", removed_record$SO, "\n"))
        cat(paste0("    AB: ", substr(removed_record$AB, 1, 250), ifelse(nchar(removed_record$AB)>250, "...", ""), "\n"))

        # 查找对应的保留文献 (在 M_after_processed_part_func 中)
        field_content_removed <- removed_record[[field_to_match]]
        
        if (!is.na(field_content_removed) && field_content_removed != "") {
            retained_candidates <- M_after_processed_part_func[
                                        !is.na(M_after_processed_part_func[[field_to_match]]) & 
                                        M_after_processed_part_func[[field_to_match]] != "" & 
                                        M_after_processed_part_func$UT != removed_record$UT, , drop=FALSE] # 排除自身
            
            if(nrow(retained_candidates) > 0){
                best_similarity_val <- -1
                kept_record <- NULL # 初始化为NULL

                if(is_exact_match){
                    exact_matches_retained <- retained_candidates[as.character(retained_candidates[[field_to_match]]) == as.character(field_content_removed), , drop=FALSE]
                    if(nrow(exact_matches_retained) > 0) {
                        kept_record <- exact_matches_retained[1, , drop=FALSE] 
                        best_similarity_val <- 1 
                    }
                } else { # 模糊匹配
                    # 确保 retained_candidates[[field_to_match]] 是字符向量
                    comparison_vector_kept <- as.character(retained_candidates[[field_to_match]])
                    distances <- utils::adist(as.character(field_content_removed), comparison_vector_kept)[1, ]
                    
                    len_removed <- nchar(as.character(field_content_removed))
                    len_kept <- nchar(comparison_vector_kept)
                    max_lengths <- pmax(len_removed, len_kept)
                    max_lengths[max_lengths == 0] <- 1 
                    similarities <- 1 - (distances / max_lengths)
                    similarities[is.nan(similarities) | is.infinite(similarities)] <- 0 
                    
                    candidate_indices_kept <- which(similarities >= tolerance_value)
                    if (length(candidate_indices_kept) > 0) {
                        best_match_local_idx_kept <- which.max(similarities[candidate_indices_kept])
                        kept_record <- retained_candidates[candidate_indices_kept[best_match_local_idx_kept], , drop=FALSE]
                        best_similarity_val <- similarities[candidate_indices_kept[best_match_local_idx_kept]]
                    }
                }

                if(!is.null(kept_record) && nrow(kept_record) > 0){ # 确保kept_record不是NULL且有行
                    cat("  [可能的对应保留文献详情]:\n")
                    cat(paste0("    UT: ", kept_record$UT, " (匹配字段 '", field_to_match, "' 相似度: ", round(best_similarity_val, 4), ")\n"))
                    cat(paste0("    TI: '", kept_record$TI, "'\n"))
                    cat(paste0("    PY: ", kept_record$PY, "\n"))
                    cat(paste0("    AU: ", substr(kept_record$AU, 1, 150), ifelse(nchar(kept_record$AU)>150, "...", ""), "\n"))
                    cat(paste0("    SO: ", kept_record$SO, "\n"))
                    cat(paste0("    AB: ", substr(kept_record$AB, 1, 250), ifelse(nchar(kept_record$AB)>250, "...", ""), "\n"))
                } else { cat("  [可能的对应保留文献详情]: 未能找到满足条件的对应保留文献。\n")}
            } else {cat("  [可能的对应保留文献详情]: 保留数据中无其他有效记录可比较 (在排除自身后)。\n")}
        } else {cat("  [可能的对应保留文献详情]: 被移除文献的匹配字段 ('", field_to_match ,"') 为空或NA。\n")}
        cat(paste0("--- 结束重复对 (\", i_pair, \") ---\n"))
      } 
      if(nrow(removed_articles_details_func) > limit_print_pairs) cat("\\n... (还有更多被移除的文献对未在报告中逐条打印详情) ...\\n")
    } else {
      # This case means removed_in_processed_part_count > 0 but anti_join found nothing,
      # which could happen if UTs are not perfectly unique post-processing or other anomoly.
      cat("提示: 此策略在处理部分移除了记录，但未能通过 UT 明确识别被移除的具体文献及其对应保留文献的详情。\n")
      cat("这可能是由于UT不唯一，或在中间步骤丢失，或duplicatedMatching的内部逻辑导致UT不再是唯一键。\n")
      cat("移除数量 (removed_in_processed_part_count): ", removed_in_processed_part_count, "\n")
    }
  } else if (removed_in_processed_part_count > 0) {
      cat("提示: 此策略在处理部分移除了记录，但因缺少UT字段或其他原因，无法打印详细的文献对信息。\n")
  }

  cat(paste0("\\n--- 测试策略报告结束: ", strategy_name, " ---\n"))
  
  # 关闭sink，恢复输出到控制台
  sink(type = "message")
  sink()
  close(zz)
  
  return(invisible(NULL)) 
}
# --- 测试函数定义结束 ---


# --- 3. 执行系列去重测试 ---
cat("\\n--- 开始执行自动化去重对比测试 ---\n")
cat(paste0("所有测试报告将输出到目录: ", dedup_output_dir, "\\n"))

# 测试1: 基于 UT 的精确匹配
cat("\\n执行测试: UT_Exact\\n")
run_deduplication_test(data_input = M_initial_for_testing, 
                       field_to_match = "UT", 
                       is_exact_match = TRUE, 
                       tolerance_value = NA, # Not used for exact match
                       output_dir_path = dedup_output_dir)

# 测试2: 基于 TI (标题) 的不同 tol 值
cat("\\n执行测试系列: TI_Tol_X\\n")
tolerances_ti <- c(0.99, 0.98, 0.95, 0.90)
for (tol_ti in tolerances_ti) {
  run_deduplication_test(data_input = M_initial_for_testing, 
                         field_to_match = "TI", 
                         is_exact_match = FALSE, 
                         tolerance_value = tol_ti,
                         output_dir_path = dedup_output_dir)
}

# 测试3: 基于 AB (摘要) 的不同 tol 值
cat("\\n检查摘要 (AB) 字段填充情况...\\n")
ab_non_empty_count <- sum(!is.na(M_initial_for_testing$AB) & M_initial_for_testing$AB != "", na.rm = TRUE)
ab_total_count <- nrow(M_initial_for_testing)
ab_fill_rate <- ifelse(ab_total_count > 0, (ab_non_empty_count / ab_total_count) * 100, 0)

cat(paste0("摘要 (AB) 字段在初始数据中有效记录数: ", ab_non_empty_count, " / ", ab_total_count, 
           " (填充比例: ", round(ab_fill_rate, 2), "%)\\n"))

# 您可以调整此阈值，例如，如果填充率过低，测试可能意义不大
ab_test_threshold_percentage <- 30 
if (ab_fill_rate >= ab_test_threshold_percentage) {
  cat(paste0("摘要填充率 (\",round(ab_fill_rate, 2),\"%) >= ", ab_test_threshold_percentage, "%，开始基于AB的去重测试...\\n"))
  tolerances_ab <- c(0.98, 0.95, 0.90)
  for (tol_ab in tolerances_ab) {
    run_deduplication_test(data_input = M_initial_for_testing, 
                           field_to_match = "AB", 
                           is_exact_match = FALSE, 
                           tolerance_value = tol_ab,
                           output_dir_path = dedup_output_dir)
  }
} else {
  cat(paste0("摘要填充率较低 (\",round(ab_fill_rate, 2),\"%)，低于 ", ab_test_threshold_percentage, "%，跳过基于AB的去重测试。\\n"))
}

cat("\\n--- 所有自动化去重对比测试执行完毕 --- \\n")
cat(paste0("测试报告已输出到目录: ", dedup_output_dir, "\\n"))
cat("请检查上述目录中的 .txt 文件以获取详细的去重结果。\n")
cat("注意: 主数据框 M_initial_for_testing 未被这些测试修改。\n")
cat("如需进行后续分析，请基于测试报告选择最佳策略，并手动对 M_initial_for_testing 应用该策略以生成最终的 M 进行下游分析。\n")

# 为了演示，后续代码如果需要一个 M 对象，它将是未经这些测试修改的初始数据
M <- M_initial_for_testing 
cat(paste0("\\n主数据框 M 当前包含 ", nrow(M), " 条记录 (此为 convert2df 后的初始状态，未经额外测试去重)。\\n"))

cat("\\n--- 自动化去重测试脚本执行完毕 ---\n")
            break
          }
        }
        # 如果在UT之前找不到PT/FN，就从UT行的前一行开始（或文件第一行）
        if (start_line_idx == -1) start_line_idx <- max(1, current_ut_line -1) 
        # 如果PT/FN就是UT行本身的前导（不太可能但作为防御），确保至少包含UT行
        if (start_line_idx > current_ut_line) start_line_idx <- current_ut_line

        end_line_idx <- -1
        # 向前搜索 ER (End of Record)
        for (k in seq(current_ut_line, length(raw_lines))) {
          if (grepl("^ER", raw_lines[k])) {
            end_line_idx <- k
            break
          }
        }
        if (end_line_idx == -1) end_line_idx <- length(raw_lines) # 未找到ER则取到文件末尾

        record_block <- raw_lines[start_line_idx:end_line_idx]
        return(list(
          source_file = basename(file_path),
          raw_record = paste(record_block, collapse = "\n")
        ))
      }
    }, error = function(e){
      # 如果读取特定文件出错，打印错误并继续下一个文件
      cat(paste0("Error processing file ", basename(file_path), " for UT ", target_ut, ": ", conditionMessage(e), "\n"))
      #明确返回一个表示错误的列表结构，而不是依赖于循环的最后一个（可能未找到的）结果
      #return(list(source_file = paste("Error reading ", basename(file_path)), raw_record = conditionMessage(e)))
    }) # end tryCatch
  } # end for loop
  # 如果遍历所有文件后都未找到
  return(list(source_file = "Not found in any file", raw_record = paste0("UT '", target_ut, "' not found.")))
}
# --- 辅助函数定义结束 ---

# 1.2 设置工作目录和数据路径
working_dir <- "C:/Users/<USER>/Desktop/article/数据处理部分"
setwd(working_dir)
cat("当前工作目录已设置为:", getwd(), "\n")

raw_data_dir <- "C:/Users/<USER>/Desktop/数据文件/citespace数据"

if (!dir.exists(raw_data_dir)) {
  stop("指定的原始数据目录不存在: ", raw_data_dir)
} else {
  cat("找到原始数据目录:", raw_data_dir, "\n")
}

# 1.3 读取并转换 WoS 数据文件
wos_files <- list.files(path = raw_data_dir, pattern = "\\.txt$", full.names = TRUE)

if (length(wos_files) == 0) {
  stop("在指定目录下没有找到 .txt 文件: ", raw_data_dir)
} else {
  cat("找到", length(wos_files), "个 .txt 文件，准备进行转换...\n")
  print(basename(wos_files))
}

cat("\n开始使用 bibliometrix 进行格式转换...\n")
M <- convert2df(file = wos_files, dbsource = "wos", format = "plaintext")
cat("转换完成。 数据框 M 包含", nrow(M), "条记录 和", ncol(M), "个字段。\n")

# --- 尝试1: 使用 duplicatedMatching 基于 UT (唯一手稿 ID) 进行精确去重 ---
cat("\n--- 尝试1: 使用 duplicatedMatching 基于 UT (唯一手稿 ID) 进行精确去重 ---\n")
M_before_dm <- M 
# 确保 M_before_dm$UT 是字符型，以防万一
M_before_dm$UT <- as.character(M_before_dm$UT) 

M_after_UT_exact <- duplicatedMatching(M_before_dm, Field = "UT", exact = TRUE)

# --- 诊断输出 UT 精确匹配移除的文献 ---
if (nrow(M_before_dm) > nrow(M_after_UT_exact)) {
    removed_by_UT_exact <- dplyr::anti_join(M_before_dm, M_after_UT_exact, by = "UT")
    cat("以下 ", nrow(removed_by_UT_exact), " 篇文献因 UT 完全相同而被 duplicatedMatching 移除:\n")
    # 只打印UT，因为其他字段理论上也应该一样
    print(removed_by_UT_exact[, c("UT", "TI", "AU", "PY", "SO"), drop = FALSE]) 
} else {
    cat("基于 UT 的精确匹配没有移除额外的文献。\n")
}
cat("M_after_UT_exact 包含", nrow(M_after_UT_exact), "条记录。\n")
cat("--- 尝试1结束 ---\n\n")

# 为了后续尝试，我们将 M 更新为这一步的结果，或者您可以选择每次都从 M_before_dm 开始
M <- M_after_UT_exact 

# --- 原有 duplicatedMatching 逻辑 (现在基于UT去重后的M) ---
cat("\n--- 开始使用 duplicatedMatching 进行基于标题的额外去重 (输入数据已经过UT精确去重) ---\n")
M_before_dm <- M # 保存去重前的数据框状态
original_nrow_M <- nrow(M_before_dm)

# 确保 TI 字段存在且为字符型
if ("TI" %in% names(M_before_dm) && is.character(M_before_dm$TI)) {
  M_after_dm <- duplicatedMatching(M_before_dm, Field = "TI", exact = FALSE, tol = 0.95)
  
  if ("UT" %in% names(M_before_dm) && "UT" %in% names(M_after_dm)) {
    M_before_dm$UT <- as.character(M_before_dm$UT)
    M_after_dm$UT <- as.character(M_after_dm$UT)
    removed_articles_info <- dplyr::anti_join(M_before_dm, M_after_dm, by = "UT")
    
    if (nrow(removed_articles_info) > 0) {
      similarity_tol <- 0.95 # 与 duplicatedMatching 中 tol 参数一致
      cat("\n以下 ", nrow(removed_articles_info), " 篇文献因与另一篇文献标题高度相似 (tol=", similarity_tol, ") 而被 duplicatedMatching 移除:\n")
      
      for (i in 1:nrow(removed_articles_info)) {
        removed_ut <- as.character(removed_articles_info$UT[i])
        # 从 removed_articles_info 中提取所需字段
        removed_ti <- removed_articles_info$TI[i]
        removed_py <- removed_articles_info$PY[i]
        removed_au <- removed_articles_info$AU[i]
        removed_af <- removed_articles_info$AF[i]
        removed_so <- removed_articles_info$SO[i]
        
        cat(paste0("\n--- 重复文献对 (", i, " / ", nrow(removed_articles_info), ") ---\n"))
        cat("  [被移除文献详情]:\n")
        cat(paste0("    UT: ", removed_ut, "\n"))
        cat(paste0("    标题 (TI): '", removed_ti, "'\n"))
        cat(paste0("    年份 (PY): ", removed_py, "\n"))
        cat(paste0("    作者 (AU): ", substr(removed_au, 1, 100), if(nchar(removed_au)>100) "..." else "", "\n")) # 限制作者长度
        cat(paste0("    作者全名 (AF): ", substr(removed_af, 1, 100), if(nchar(removed_af)>100) "..." else "", "\n")) # 限制作者全名长度
        cat(paste0("    来源 (SO): ", removed_so, "\n"))

        if (!is.na(removed_ti) && removed_ti != "") {
          if ("TI" %in% names(M_after_dm) && is.character(M_after_dm$TI) && "UT" %in% names(M_after_dm)){
            M_after_dm_valid_ti <- M_after_dm[!is.na(M_after_dm$TI) & M_after_dm$TI != "", ]
            M_after_dm_valid_ti$UT <- as.character(M_after_dm_valid_ti$UT)
            if(nrow(M_after_dm_valid_ti) > 0){
              distances <- utils::adist(removed_ti, M_after_dm_valid_ti$TI)[1, ]
              len_removed_ti <- nchar(removed_ti)
              len_kept_tis <- nchar(M_after_dm_valid_ti$TI)
              max_lengths <- pmax(len_removed_ti, len_kept_tis)
              max_lengths[max_lengths == 0] <- 1 
              similarities <- 1 - (distances / max_lengths)
              similarities[is.nan(similarities)] <- 0 
              candidate_indices <- which(similarities >= similarity_tol)
              if (length(candidate_indices) > 0) {
                best_match_local_idx <- which.max(similarities[candidate_indices])
                best_match_global_idx_in_valid <- candidate_indices[best_match_local_idx]
                
                # 从 M_after_dm_valid_ti 中提取保留文献的所需字段
                kept_ut <- as.character(M_after_dm_valid_ti$UT[best_match_global_idx_in_valid])
                kept_ti <- M_after_dm_valid_ti$TI[best_match_global_idx_in_valid]
                kept_py <- M_after_dm_valid_ti$PY[best_match_global_idx_in_valid]
                kept_au <- M_after_dm_valid_ti$AU[best_match_global_idx_in_valid]
                kept_af <- M_after_dm_valid_ti$AF[best_match_global_idx_in_valid]
                kept_so <- M_after_dm_valid_ti$SO[best_match_global_idx_in_valid]
                best_similarity <- similarities[best_match_global_idx_in_valid]
                
                cat("  [对应保留文献详情]:\n")
                cat(paste0("    UT: ", kept_ut, " (相似度: ", round(best_similarity, 4), ")\n"))
                cat(paste0("    标题 (TI): '", kept_ti, "'\n"))
                cat(paste0("    年份 (PY): ", kept_py, "\n"))
                cat(paste0("    作者 (AU): ", substr(kept_au, 1, 100), if(nchar(kept_au)>100) "..." else "", "\n"))
                cat(paste0("    作者全名 (AF): ", substr(kept_af, 1, 100), if(nchar(kept_af)>100) "..." else "", "\n"))
                cat(paste0("    来源 (SO): ", kept_so, "\n"))
              } else {
                cat("    [对应保留文献详情]: 未能找到相似度 >=", similarity_tol, " 的对应保留文献。\n")
              }
            } else {
               cat("    [对应保留文献详情]: 保留的数据框中没有有效的标题进行比较。\n")
            }
          } else {
            cat("    [对应保留文献详情]: 保留的数据框中缺少 TI 或 UT 字段，或 TI 不是字符型。\n")
          }
        } else {
          cat("    [对应保留文献详情]: 被移除文献的标题为空或NA，无法查找匹配项。\n")
        }
        cat(paste0("--- 结束重复文献对 (", i, ") ---\n"))
      }
    } else {
      cat("提示: duplicatedMatching 移除了记录，但通过 UT 未能明确识别出被移除的具体文献详情。可能是UT不唯一或存在其他数据问题。\n")
    }
  } else if ("DI" %in% names(M_before_dm) && "DI" %in% names(M_after_dm)) {
    M_before_dm$DI <- as.character(M_before_dm$DI)
    M_after_dm$DI <- as.character(M_after_dm$DI)
    removed_by_dm_di_approx <- dplyr::anti_join(M_before_dm, M_after_dm, by = "DI")
    if(nrow(removed_by_dm_di_approx) > 0){
        cat("警告: 'UT'字段用于识别差异失败或不可用，尝试基于'DI'识别。以下为基于DI被移除的记录的DI:\n")
        print(removed_by_dm_di_approx$DI)
        cat("请注意：基于DI的匹配主要用于计数，未实现详细的核心字段对比展示。
")
    } else {
        removed_count_dm_check <- original_nrow_M - nrow(M_after_dm)
        if(removed_count_dm_check > 0) {
            cat("警告: duplicatedMatching 移除了 ", removed_count_dm_check, " 条记录，但通过 UT 和 DI 均无法明确识别它们。请检查数据一致性。
")
        } else {
             cat("提示: 通过 UT 和 DI 均未识别出差异，且记录总数在 duplicatedMatching 后未改变或已通过UT明确识别。
")
        }
    }
  } else {
    cat("警告: 关键字段'UT'或'DI'在 duplicatedMatching 前后的数据框中不都存在或不一致，无法准确列出被移除记录的详细标识。
")
  }
  
  M <- M_after_dm # 更新 M
  removed_count_dm <- original_nrow_M - nrow(M)
  cat("duplicatedMatching 完成。基于标题移除了额外的", removed_count_dm, "条重复记录。
")
  cat("数据框 M 现在包含", nrow(M), "条记录。
")
} else {
  cat("警告: 字段 'TI' 不存在或不是字符型，跳过 duplicatedMatching。
")
}
cat("--- duplicatedMatching 结束 ---\n\n")

# 在 convert2df 之后立即添加以下诊断代码
cat("\n--- 数据量检查 ---\n")
cat("原始文件数量:", length(wos_files), "\n")

# 检查每个原始文件中的记录数
for(file in wos_files) {
  # 读取原始文件内容
  raw_content <- readLines(file)
  # 计算 ER 标记的数量(每条记录以 ER 结束)
  record_count <- sum(grepl("^ER", raw_content))
  cat("文件", basename(file), "中的记录数:", record_count, "\n")
}

# 检查转换后的数据框
cat("\n转换后的数据框 M 中的记录数:", nrow(M), "\n")

# 检查是否有重复的 UT (WoS 唯一标识符)
duplicate_UTs <- M$UT[duplicated(M$UT)]
if(length(duplicate_UTs) > 0) {
  cat("\n发现重复的 UT:\n")
  print(duplicate_UTs)
  
  # 显示重复记录的具体信息
  duplicate_records <- M[M$UT %in% duplicate_UTs, ]
  cat("\n重复记录的详细信息:\n")
  print(duplicate_records[, c("TI", "AU", "PY", "UT")])
}

# cat("\n--- 诊断: M 对象 --- \n") 
cat("维度 (Dim_M): ", paste(dim(M), collapse = " x "), "\n")
cat("列名 (Colnames_M): ", paste(colnames(M), collapse = ", "), "\n")
cat("前几行 AU (Authors_M_Head):\n"); print(head(M$AU))
cat("前几行 DE (AuthorKeywords_M_Head):\n"); print(head(M$DE))
cat("前几行 ID (KeywordsPlus_M_Head):\n"); print(head(M$ID))
cat("前几行 SO (Sources_M_Head):\n"); print(head(M$SO))
cat("非空 AU 数量 (NonEmpty_AU_M): ", sum(!is.na(M$AU) & M$AU != ""), "\n")
cat("非空 DE 数量 (NonEmpty_DE_M): ", sum(!is.na(M$DE) & M$DE != ""), "\n")
cat("非空 ID 数量 (NonEmpty_ID_M): ", sum(!is.na(M$ID) & M$ID != ""), "\n")
cat("非空 SO 数量 (NonEmpty_SO_M): ", sum(!is.na(M$SO) & M$SO != ""), "\n")
cat("--- 诊断: M 对象结束 --- \n\n")

M_for_excel <- M
all_na_cols <- sapply(M_for_excel, function(x) all(is.na(x)))
if (any(all_na_cols)) {
  cat("检测到以下列完全由 NA 组成，将其转换为字符型以便写入 Excel:",
      paste(names(M_for_excel)[all_na_cols], collapse=", "), "\n")
  for (col_name in names(M_for_excel)[all_na_cols]) {
    M_for_excel[[col_name]] <- as.character(M_for_excel[[col_name]])
  }
}

excel_char_limit <- 32700
truncated_cols <- character(0)
char_cols <- names(M_for_excel)[sapply(M_for_excel, is.character)]
for (col_name in char_cols) {
  char_lengths <- nchar(M_for_excel[[col_name]], keepNA = FALSE)
  if (any(char_lengths > excel_char_limit, na.rm = TRUE)) {
    truncated_cols <- c(truncated_cols, col_name)
    M_for_excel[[col_name]] <- ifelse(
      !is.na(M_for_excel[[col_name]]) & char_lengths > excel_char_limit,
      paste0(substr(M_for_excel[[col_name]], 1, excel_char_limit), "... [截断]"),
      M_for_excel[[col_name]]
    )
  }
}
if (length(truncated_cols) > 0) {
  cat("警告：检测到以下列中存在超长文本 (超过", excel_char_limit, "字符)，已截断以便写入 Excel:",
      paste(unique(truncated_cols), collapse=", "), "\n")
}

output_xlsx_file <- file.path(working_dir, "WoS_converted_data.xlsx")
tryCatch({
  write_xlsx(M_for_excel, path = output_xlsx_file)
  cat("已将转换后的数据框同时保存为 Excel 文件到:", output_xlsx_file, "\n")
}, error = function(e) {
  cat("错误：无法将数据框保存为 Excel 文件。错误信息:", conditionMessage(e), "\n")
})

output_rdata_file <- file.path(working_dir, "WoS_converted_data.RData")
save(M, file = output_rdata_file)
cat("已将转换后的数据框保存到:", output_rdata_file, "\n")
cat("\n--- 第一步：数据加载与格式转换完成 ---\n")

# --- 2. 数据探索与初步缺失分析 ---
load("WoS_converted_data.RData")
cat("\n已加载数据框 M。\n")

cat("\n--- 数据框 M 基本信息 ---\n")
cat("维度 (记录数 x 字段数):", dim(M)[1], "x", dim(M)[2], "\n")

field_mapping <- data.frame(
  Tag = c("DT", "AU", "AF", "TI", "SO", "LA", "DE", "ID", "AB", "C1", "RP", "CR", "TC",
          "PY", "SC", "UT", "DI", "WC", "J9", "JI", "PU", "PI", "PA", "SN", "BN",
          "FU", "NR", "VL", "IS", "BP", "EP", "PG", "DA", "EM", "OI", "RI", "PM",
          "OA", "HC", "HP", "Z9", "U1", "U2", "U3", "U4", "U5", "U6", "D2", "EA",
          "EY", "DB", "AU_CO", "AU_UN", "AU1_CO", "AU1_UN", "SR", "LCS", "GCS", "MCP",
          "SCP", "TI_TM", "AB_TM", "DE_TM", "ID_TM", "CO_CA", "N_GRANT",
          "AR", "BA", "BE", "BF", "C3", "CA", "CL", "CT", "CY",
          "EF", "EI", "ER", "FX", "GA", "HO", "PD", "PN", "PT", "SE", "SI", "SP", "SU",
          "SR_FULL", "WE", "C1raw", "AU_UN_NR"
          ),
  Meaning = c("文档类型", "作者", "作者全名", "标题", "出版物来源", "语言", "作者关键词",
              "关键词Plus", "摘要", "作者地址", "通讯作者地址", "引用参考文献", "被引频次",
              "出版年份", "WoS学科类别", "唯一标识符(WoS)", "DOI", "Web of Science类别",
              "期刊缩写(J9)", "ISO期刊缩写(JI)", "出版商", "出版商城市", "出版商地址",
              "ISSN", "ISBN", "资助机构与编号", "参考文献数量", "卷号", "期号", "起始页码",
              "结束页码", "页数", "出版日期(数据库记录)", "电子邮件地址", "ORCID标识符", "ResearcherID",
              "PubMed ID", "开放获取状态", "高被引论文", "热点论文", "总引用次数(WoS)",
              "过去180天的使用计数", "自2013年以来的使用计数", "用户定义字段3", "用户定义字段4",
              "用户定义字段5", "用户定义字段6", "电子出版日期", "提前访问日期",
              "提前访问年份", "数据来源", "作者国家", "作者机构", "第一作者国家",
              "第一作者机构", "简短引用格式", "局部引用得分", "全局引用得分", "多国出版物",
              "单国出版物", "标题术语矩阵", "摘要术语矩阵", "关键词术语矩阵",
              "KeywordsPlus术语矩阵", "通讯作者国家", "资助项目数",
              "文章编号", "图书作者", "编者", "图书作者全名", "会议标题(C3)",
              "会议赞助者(CA)", "会议地点", "会议标题(CT)", "会议日期",
              "文件结束符", "电子ISSN", "记录结束符", "资助文本", "团体作者", "会议主办方",
              "出版日期(月/日)", "部分号", "出版物类型", "丛书标题", "特刊/增刊标识", "会议赞助者(SP)",
              "增刊", "完整引文格式", "WoS 版本", "原始作者地址", "作者机构数量"
              )
)
actual_colnames <- colnames(M)
colnames_df <- data.frame(Tag = actual_colnames) %>%
  left_join(field_mapping, by = "Tag") %>%
  mutate(Meaning = ifelse(is.na(Meaning) & Tag %in% actual_colnames,
                        paste0("-- (", Tag, ") 未在预定义映射中 --"),
                        Meaning))
cat("\n--- 数据框 M 字段列表 (Tag 与 中文含义) ---\n")
print(colnames_df, row.names = FALSE, right = FALSE)

if (!requireNamespace("naniar", quietly = TRUE)) {
  cat("naniar 包未安装，正在尝试安装...\n")
  install.packages("naniar")
}
library(naniar)

cat("\n--- 字段缺失值分析 (按缺失比例降序排列) ---\n")
missing_summary <- miss_var_summary(M) %>%
  arrange(desc(pct_miss)) %>%
  left_join(select(colnames_df, Tag, Meaning), by = c("variable" = "Tag")) %>%
  select(Tag = variable, Meaning, n_miss, pct_miss)
cat("\n确认：计算得到的 missing_summary 对象包含", nrow(missing_summary), "行 (字段)。\n\n")
print(missing_summary, row.names = FALSE, right = FALSE, n = Inf)
cat("\n--- 第二步：数据探索与初步缺失分析完成 ---\n")