# 修改版本，暂时移除数据库操作以确保核心功能正常运行

# 设置工作目录
setwd("C:/Users/<USER>/Desktop/google数据处相关/作者消岐")
cat("工作目录设置为:", getwd(), "\n")

# 加载必要的包
cat("正在加载所需R包...\n")
libraries_to_load <- c("bibliometrix", "tidyverse", "rcrossref", "openalexR", "stringr", "R.utils")

for (lib in libraries_to_load) {
  if (!require(lib, character.only = TRUE, quietly = TRUE)) {
    cat(sprintf("尝试安装并加载包: %s\n", lib))
    tryCatch({
      install.packages(lib)
      library(lib, character.only = TRUE)
    }, error = function(e) {
      stop(sprintf("无法安装或加载必要的包 '%s': %s\n", lib, e$message))
    })
  }
}
cat("所有包加载完成\n")

# 替代数据库函数的简单版本
save_api_response <- function(source, query_type, query_value, response_data) {
  cat(sprintf("模拟保存API响应: %s, %s, %s\n", source, query_type, query_value))
  return(1) # 模拟返回ID
}

save_author_identifier <- function(author_name, identifier_type, identifier_value, source, verification_status = NULL, document_id = NULL) {
  cat(sprintf("模拟保存作者标识符: %s, %s, %s\n", author_name, identifier_type, identifier_value))
}

#=========================
# 1. 导入WoS数据并检查标识符覆盖情况
#=========================

# 导入WoS数据 - 使用指定的文件路径
wos_file <- "C:/Users/<USER>/Desktop/google数据处相关/作者消岐/download1-500.txt"

# 检查文件是否存在
if(!file.exists(wos_file)) {
  stop("指定的数据文件不存在: ", wos_file)
}

cat("正在导入文件:", wos_file, "\n")
cat("文件大小:", file.info(wos_file)$size, "字节\n")

tryCatch({
  cat("开始转换WoS数据...\n")
  # 使用bibliometrix的convert2df函数，遵循bibliometrix标准
  wos_data <- convert2df(wos_file, dbsource = "wos", format = "plaintext")
  cat("WoS数据转换完成\n")
  
  # 检查数据结构
  cat("数据维度:", paste(dim(wos_data), collapse="x"), "\n")
  cat("数据列名:", paste(names(wos_data), collapse=", "), "\n")
  
  # 检查标识符覆盖情况
  cat("计算标识符覆盖情况...\n")
  identifiers_coverage <- data.frame(
    文章总数 = nrow(wos_data),
    有DOI文章数 = sum(!is.na(wos_data$DI) & wos_data$DI != ""),
    有DOI百分比 = round(sum(!is.na(wos_data$DI) & wos_data$DI != "")/nrow(wos_data)*100, 2),
    有UT文章数 = sum(!is.na(wos_data$UT) & wos_data$UT != ""),
    有UT百分比 = round(sum(!is.na(wos_data$UT) & wos_data$UT != "")/nrow(wos_data)*100, 2)
  )
  
  print(identifiers_coverage)
  
}, error = function(e) {
  cat("导入数据时出错:\n")
  print(e)
  stop("数据导入失败，脚本终止")
})

#=========================
# 2. 提取并标准化作者和机构信息（严格遵照bibliometrix规范）
#=========================

# 使用bibliometrix的原生函数提取作者-机构信息
tryCatch({
  cat("提取作者-机构信息...\n")
  # 使用bibliometrix的metaTagExtraction函数处理作者和机构关系
  author_affiliation <- metaTagExtraction(wos_data, Field = "AU_UN", sep = ";")
  cat("作者-机构信息提取完成\n")
  
  # 保存到CSV文件
  write.csv(author_affiliation, "author_affiliations.csv", row.names = FALSE, fileEncoding = "UTF-8")
  cat("作者-机构关系保存到CSV文件\n")
  
}, error = function(e) {
  warning("提取作者-机构信息时出错: ", e$message)
  author_affiliation <- data.frame()
})

# 使用bibliometrix的authors函数获取作者信息 - 明确指定包名
tryCatch({
  cat("获取标准化作者信息...\n")
  # 使用bibliometrix::biblioAnalysis进行分析
  biblio_results <- bibliometrix::biblioAnalysis(wos_data)
  # 然后使用bibliometrix::authors提取作者信息
  authors_data <- bibliometrix::authors(biblio_results)
  cat("获取到", length(authors_data), "位不同作者\n")
  
  # 保存到CSV文件
  authors_df <- data.frame(
    author = names(authors_data),
    frequency = as.numeric(authors_data),
    stringsAsFactors = FALSE
  )
  write.csv(authors_df, "bibliometrix_authors.csv", row.names = FALSE, fileEncoding = "UTF-8")
  cat("作者信息保存到CSV文件\n")
  
}, error = function(e) {
  warning("获取作者信息时出错: ", e$message)
})

# 严格遵照bibliometrix作者名称标准化
wos_data$AU_NORM <- NA
tryCatch({
  cat("标准化作者名称...\n")
  # 使用bibliometrix的标准化方法，保留原始格式
  wos_data$AU_NORM <- wos_data$AU
  cat("作者名称标准化完成\n")
}, error = function(e) {
  warning("作者名称标准化时出错: ", e$message)
})

#=========================
# 3. 使用外部API获取ORCID等标识符，严格按bibliometrix格式处理返回数据
#=========================

# 安全获取DOI列表
dois_to_check <- character(0)
if("DI" %in% names(wos_data)) {
  dois_to_check <- wos_data$DI[!is.na(wos_data$DI) & wos_data$DI != ""]
}

# 避免空DOI列表
if(length(dois_to_check) == 0) {
  cat("警告: 未找到有效的DOI，无法从外部API获取作者标识符\n")
} else {
  # 限制请求数量避免API限制
  if(length(dois_to_check) > 10) { # 减少为10个以提高测试速度
    set.seed(123) # 确保可重复性
    dois_to_check <- sample(dois_to_check, 10)
    cat("注意: 仅抽样处理了10个DOI以避免API限制\n")
  }
}

# 从Crossref获取ORCID信息
get_crossref_orcids <- function(dois) {
  if(length(dois) == 0) return(data.frame())
  
  orcid_data <- list()
  for(i in seq_along(dois)) {
    cat(sprintf("处理DOI %d/%d: %s\n", i, length(dois), dois[i]))
    
    # 添加重试机制
    max_retries <- 3
    retry_count <- 0
    success <- FALSE
    
    while(!success && retry_count < max_retries) {
      tryCatch({
        # 增加超时时间
        result <- cr_works(dois = dois[i], .progress = FALSE, verbose = FALSE, timeout = 30)
        
        # 根据bibliometrix格式处理数据
        if(is.list(result)) {
          # 模拟保存API响应
          save_api_response("Crossref", "DOI", dois[i], result)
          
          # 更严格的数据结构检查并格式化数据
          if(is.list(result$data)) {
            if("author" %in% names(result$data)) {
              authors <- result$data$author
              
              if(is.list(authors) && length(authors) > 0) {
                # 按照bibliometrix格式处理每位作者
                for(j in seq_along(authors)) {
                  if(is.list(authors[[j]])) {
                    # 按照bibliometrix格式提取姓名：姓氏大写，名前置
                    author_name <- NA_character_
                    if("family" %in% names(authors[[j]]) && "given" %in% names(authors[[j]])) {
                      family <- toupper(authors[[j]]$family)  # 姓氏大写
                      given <- authors[[j]]$given
                      author_name <- paste(family, given, sep=", ")  # bibliometrix格式
                    }
                    
                    # 处理ORCID
                    if("ORCID" %in% names(authors[[j]]) && is.character(authors[[j]]$ORCID)) {
                      orcid <- gsub("http[s]?://orcid.org/", "", authors[[j]]$ORCID)
                      
                      # 如果作者名称和ORCID有效，添加到结果
                      if(!is.na(author_name) && !is.na(orcid) && nchar(orcid) > 0) {
                        # 添加到结果列表
                        orcid_data[[length(orcid_data) + 1]] <- list(
                          DOI = dois[i],
                          author_name = author_name,
                          orcid = orcid
                        )
                        
                        # 模拟保存到数据库
                        save_author_identifier(
                          author_name = author_name,
                          identifier_type = "ORCID",
                          identifier_value = orcid,
                          source = "Crossref",
                          verification_status = "Crossref验证",
                          document_id = dois[i]
                        )
                      }
                    }
                  }
                }
              }
            }
          }
        }
        
        # 标记成功
        success <- TRUE
        
      }, error = function(e) {
        retry_count <- retry_count + 1
        cat(sprintf("处理DOI时出错(尝试 %d/%d): %s - 错误: %s\n", 
                   retry_count, max_retries, dois[i], e$message))
        
        # 如果是超时错误，增加等待时间
        if(grepl("Timeout", e$message)) {
          Sys.sleep(5) # 超时错误时等待更长时间
        } else {
          Sys.sleep(2) # 其他错误等待标准时间
        }
      })
      
      # 即使成功也添加延迟避免API速率限制
      Sys.sleep(1)
    }
    
    # 如果所有重试都失败，记录最终失败
    if(!success) {
      cat(sprintf("处理DOI最终失败: %s - 已尝试 %d 次\n", dois[i], max_retries))
    }
  }
  
  # 安全合并结果
  if(length(orcid_data) > 0) {
    # 将列表转换为数据框
    result_df <- do.call(rbind, lapply(orcid_data, function(x) {
      data.frame(
        DOI = x$DOI,
        author_name = x$author_name,
        orcid = x$orcid,
        stringsAsFactors = FALSE
      )
    }))
    return(result_df)
  }
  
  return(data.frame()) # 返回空数据框
}

# 获取ORCID数据
cat("从Crossref获取ORCID数据...\n")
orcid_info <- data.frame()
if(length(dois_to_check) > 0) {
  orcid_info <- get_crossref_orcids(dois_to_check)
  # 保存结果到CSV
  if(nrow(orcid_info) > 0) {
    write.csv(orcid_info, "crossref_orcids.csv", row.names = FALSE, fileEncoding = "UTF-8")
    cat("Crossref ORCID信息保存到CSV文件\n")
  }
}

# 尝试使用bibliometrix的原生作者分析功能
tryCatch({
  cat("\n执行bibliometrix原生作者分析...\n")
  authors_results <- biblioAnalysis(wos_data)
  auth_stats <- bibliometrix::authors(authors_results)
  cat("成功完成bibliometrix作者分析\n")
  
  # 保存bibliometrix标准分析结果
  write.csv(auth_stats, "bibliometrix_authors_stats.csv", row.names = FALSE, fileEncoding = "UTF-8")
  cat("Bibliometrix作者分析结果保存到CSV文件\n")
  
}, error = function(e) {
  cat("执行bibliometrix作者分析时出错:", e$message, "\n")
})

# 输出汇总信息
cat("\n作者分析工作流程完成!\n")
cat("生成的文件:\n")
if(exists("orcid_info") && is.data.frame(orcid_info) && nrow(orcid_info) > 0) {
  cat("1. crossref_orcids.csv - Crossref ORCID信息\n")
}
if(exists("author_affiliation") && is.data.frame(author_affiliation) && nrow(author_affiliation) > 0) {
  cat("2. author_affiliations.csv - 作者-机构关系\n")
}
if(file.exists("bibliometrix_authors.csv")) {
  cat("3. bibliometrix_authors.csv - 作者列表\n")
}
if(file.exists("bibliometrix_authors_stats.csv")) {
  cat("4. bibliometrix_authors_stats.csv - bibliometrix原生作者分析结果\n")
}